# Knowledge Base Change Tracking Guide

## Overview

This guide helps you track and understand knowledge base changes, actions, filter construction, and document retrieval in your `manual_test.py` pipeline.

## 🎯 What's Been Added

### 1. KB Tracking System (`kb_tracking_system.py`)
- **ActionTracker**: Tracks individual KB actions with detailed metadata
- **FilterCondition**: Records how filters are constructed
- **CollectionStats**: Monitors collection-level statistics
- **KBChangeTracker**: Main tracking coordinator

### 2. Dashboard (`kb_dashboard.py`)
- Real-time monitoring of active and completed actions
- Filter usage analysis
- Performance metrics
- Collection statistics

### 3. Enhanced Logging in `manual_test.py`
- Automatic tracking of KB operations
- Progress summaries every 10 notifications
- Final dashboard display with export

## 🔍 Understanding the Flow

### Filter Construction Process
```
Action Input → Filter Builder → Qdrant Query
     ↓              ↓              ↓
1. filter_fields  → Direct field mapping
2. METADATA_MAP   → document_id → metadata.document_id
3. target_document → Fallback to metadata.document_id
```

### Collection Targeting
- `rbi_circular` - Regular circulars and notifications
- `rbi_master_circular` - Master circulars
- `rbi_master_direction` - Master directions
- `rbi_other` - Informational/administrative documents

### Action Types Tracked
- **ADD_DOCUMENT**: New document insertion
- **REMOVE_DOCUMENT**: Document deletion (supersession)
- **UPDATE_DOCUMENT**: Metadata/content updates
- **LINK_DOCUMENT**: Cross-document relationships

## 📊 How to Monitor Operations

### During Execution
The system automatically logs:
```
🎯 STARTED: REMOVE_DOCUMENT action action_1699123456789
   Target: RBI/FED/2024-25/17
   Collection: rbi_circular
   Filter conditions: 3
   📋 Filter: metadata.document_id = RBI/FED/2024-25/17 (metadata_map)
   📋 Filter: metadata.long_code = FED.CO.NBFC.BC.No.109/22.10.106/2024-25 (filter_fields)
   📋 Filter: metadata.title = Guidelines on... (filter_fields)
```

### Progress Updates
Every 10 notifications:
```
📊 PROGRESS UPDATE - 10/50 COMPLETED (20.0%)
    ✅ Successful: 8
    ❌ Failed: 2
    📈 Success Rate: 80.0%

📊 KB Operations Summary:
    Active actions: 2
    Completed actions: 25
    Success rate: 23/25
```

### Final Dashboard
At the end of execution:
```
🔍 FINAL KNOWLEDGE BASE OPERATIONS DASHBOARD
================================================================================
📊 KB OPERATIONS SUMMARY
   Active: 0
   Completed: 127
   Success rate: 119/127
   Total documents: 1,245
```

## 🛠️ Manual Monitoring

### View Real-time Dashboard
```python
from kb_dashboard import show_dashboard
show_dashboard()
```

### Get Quick Summary
```python
from kb_dashboard import show_summary
show_summary()
```

### Export Tracking Data
```python
from kb_tracking_system import kb_tracker
export_file = kb_tracker.export_tracking_data()
print(f"Data exported to: {export_file}")
```

## 🔧 Key Tracking Points

### Filter Construction
- **Source tracking**: Where each filter condition comes from
- **Field mapping**: How action fields map to Qdrant fields
- **Fallback logic**: When primary filters fail

### Collection Operations
- **Document counts**: Added/removed/updated per collection
- **Operation types**: Distribution of action types
- **Success rates**: Per collection and overall

### Performance Metrics
- **Duration tracking**: How long each action takes
- **Success rates**: Percentage of successful operations
- **Error patterns**: Common failure modes

## 📈 Understanding the Data

### Filter Condition Sources
- `filter_fields`: Explicitly provided in action
- `metadata_map`: Mapped from standard fields
- `target_document`: Fallback when no other filters

### Collection Statistics
- `total_documents`: Current document count
- `documents_added`: New documents in this session
- `documents_removed`: Deleted documents (supersession)
- `documents_updated`: Modified documents

### Action Success Patterns
- High success rate (>90%): Good filter construction
- Medium success rate (70-90%): Some filter issues
- Low success rate (<70%): Major filter problems

## 🚨 Troubleshooting

### Common Issues
1. **No documents found for removal**: Check filter construction
2. **Multiple documents matched**: Filters too broad
3. **Vector search fallback**: Exact filters failed

### Debug Steps
1. Check the exported tracking data JSON
2. Look for filter condition patterns
3. Verify collection targeting
4. Review error messages in logs

## 📁 Generated Files

- `kb_operations.log`: Detailed operation logs
- `kb_tracking_export_YYYYMMDD_HHMMSS.json`: Complete tracking data
- `chunk_processing_results.json`: Chunk-level processing details
- `debug_affected_documents.json`: Document action details

## 🎯 Next Steps

1. Run your `manual_test.py` with the tracking enabled
2. Monitor the progress logs for filter construction details
3. Review the final dashboard for overall performance
4. Export and analyze the tracking data for patterns
5. Use the insights to optimize filter construction and collection targeting

The tracking system will help you understand exactly how your KB operations work and identify areas for improvement!
