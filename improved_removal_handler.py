"""
Improved document removal handler to address edge cases identified in testing.

This module provides fixes for:
1. Rejecting MANUAL_REVIEW_REQUIRED document IDs
2. Properly handling non-existent document IDs 
3. Implementing document ID normalization
4. Adding additional validation for removal requests

Usage:
    # Import in knowledge_base_update_executor.py
    from improved_removal_handler import handle_special_document_ids, normalize_document_id
"""

import re
import logging

# Configure logger
logger = logging.getLogger(__name__)

def handle_special_document_ids(document_id):
    """
    Check if the document ID is a special case that should be rejected.
    
    Args:
        document_id (str): The document ID to check
        
    Returns:
        tuple: (is_valid, reason)
            - is_valid (bool): True if document ID is valid for removal, False otherwise
            - reason (str): Explanation of why the document ID is invalid (if applicable)
    """
    if not document_id:
        return False, "Empty document ID"
    
    # Check for MANUAL_REVIEW_REQUIRED pattern (exact match or substring)
    if document_id == "MANUAL_REVIEW_REQUIRED" or "MANUAL_REVIEW_REQUIRED" in document_id:
        return False, "MANUAL_REVIEW_REQUIRED documents cannot be removed automatically"
    
    # Check for PRESERVE_DOCUMENT pattern
    if document_id == "PRESERVE_DOCUMENT" or document_id.startswith("PRESERVE_"):
        return False, "Documents marked for preservation cannot be removed"
    
    # Check for TEST_ prefix (test documents)
    if document_id.startswith("TEST_"):
        return False, "Test documents should be removed using test tools"
    
    return True, ""

def normalize_document_id(document_id):
    """
    Normalize document ID to handle format variations.
    
    For example, RBI/DOR/2021-22/80 and RBI/DOR/2021-22/80/ should be treated the same.
    
    Args:
        document_id (str): The document ID to normalize
        
    Returns:
        str: Normalized document ID
    """
    if not document_id:
        return document_id
        
    # Remove trailing slashes
    normalized_id = document_id.rstrip("/")
    
    # Handle spaces in document IDs
    normalized_id = normalized_id.replace(" ", "")
    
    # Normalize RBI document IDs (handle inconsistent formatting)
    rbi_pattern = r'RBI[/\\]([A-Z]+)[/\\](\d{4}-\d{2})[/\\](\d+)'
    match = re.match(rbi_pattern, normalized_id)
    if match:
        dept, year, number = match.groups()
        normalized_id = f"RBI/{dept}/{year}/{number}"
    
    # Normalize FIDD document IDs
    fidd_pattern = r'FIDD\.CO\.([A-Z]+)\.BC\.No\.(\d+)[/\\](\d+\.\d+\.\d+)[/\\](\d{4}-\d{2})'
    match = re.match(fidd_pattern, normalized_id)
    if match:
        dept, num, code, year = match.groups()
        normalized_id = f"FIDD.CO.{dept}.BC.No.{num}/{code}/{year}"
    
    # Normalize DBR document IDs
    dbr_pattern = r'DBR\.No\.([A-Z]+)\.BC\.(\d+)[/\\](\d+\.\d+\.\d+)[/\\](\d{4}-\d{2})'
    match = re.match(dbr_pattern, normalized_id)
    if match:
        dept, num, code, year = match.groups()
        normalized_id = f"DBR.No.{dept}.BC.{num}/{code}/{year}"
        
    return normalized_id

def get_reliable_fields():
    """
    Get the list of fields that are reliable for document matching,
    in order of priority.
    
    Returns:
        list: List of field names that should be used for matching
    """
    return [
        'document_id',     # Most reliable - exact document ID match
        'document_number', # Next most reliable - document number
        'title'           # Title can be used if available
    ]

def get_available_reliable_fields(metadata_fields):
    """
    Get list of available reliable fields from the metadata.
    
    Args:
        metadata_fields (dict): Dictionary of field names to their values
        
    Returns:
        list: List of available reliable field names, in priority order
    """
    reliable_fields = get_reliable_fields()
    return [field for field in reliable_fields if metadata_fields.get(field)]

def validate_fields_for_removal(metadata_fields):
    """
    Check if we have sufficient reliable fields for document removal.
    
    Args:
        metadata_fields (dict): Dictionary of field names to their values
        
    Returns:
        tuple: (is_valid, reason)
            - is_valid (bool): True if we have sufficient fields
            - reason (str): Explanation if invalid
    """
    available_reliable = get_available_reliable_fields(metadata_fields)
    
    if not available_reliable:
        return False, "No reliable fields provided for document identification"
    
    # If we only have document_id, that's okay - it's the most reliable
    if len(available_reliable) == 1 and available_reliable[0] == 'document_id':
        return True, "Using document_id only"
        
    # For other cases, we want at least two reliable fields
    if len(available_reliable) < 2:
        missing = [f for f in get_reliable_fields()[:2] if f not in available_reliable]
        return False, f"Insufficient reliable fields. Consider adding: {', '.join(missing)}"
    
    return True, f"Using fields: {', '.join(available_reliable)}"

def validate_document_removal(action, results):
    """
    Validate a document removal action and modify the results accordingly.
    
    Args:
        action (dict): The removal action
        results (dict): The current results dict
        
    Returns:
        dict: Modified results dict with validation information
    """
    document_id = action.get('document_id', '') or action.get('target_document', '')
    
    # Check if this is a special document ID case
    is_valid, reason = handle_special_document_ids(document_id)
    if not is_valid:
        results["success"] = False
        results["details"] = reason
        logger.warning(f"[QDRANT] ⚠️ Document removal rejected: {reason} for document_id={document_id}")
        return results
    
    # Extract only reliable metadata fields from the action
    metadata_fields = {
        'document_id': document_id,
        'document_number': action.get('document_number', ''),
        'title': action.get('title', '') or action.get('document_title', '')
    }
    
    # Validate we have sufficient reliable fields
    is_valid, reason = validate_fields_for_removal(metadata_fields)
    if not is_valid:
        results["success"] = False
        results["details"] = reason
        logger.warning(f"[QDRANT] ⚠️ Document removal failed: {reason}")
        return results
    
    # Log which reliable fields we're using
    available_reliable = get_available_reliable_fields(metadata_fields)
    logger.info(f"[QDRANT] 🔍 Using reliable fields for matching: {', '.join(available_reliable)}")
    
    # Check if no documents were found (empty executed list)
    if "[]" in results.get("details", ""):
        # This indicates that no documents were actually removed
        if "No matches found" in results.get("logs", ""):
            details = f"Document not found using reliable fields: {', '.join(available_reliable)}"
            results["success"] = False
            results["details"] = details
            logger.warning(f"[QDRANT] ⚠️ Document removal failed: {details}")
            
            # Suggest which additional reliable fields might help
            missing_reliable = [f for f in get_reliable_fields() 
                              if f not in available_reliable]
            if missing_reliable:
                suggestion = f"Consider providing these reliable fields: {', '.join(missing_reliable)}"
                results["suggestion"] = suggestion
                logger.info(f"[QDRANT] 💡 {suggestion}")
            
            return results
    
    return results
