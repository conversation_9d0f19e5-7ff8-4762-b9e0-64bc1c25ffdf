2025-08-04 08:24:12,883 - INFO - 🚀 Starting removal action debugging...
2025-08-04 08:24:12,883 - INFO - ============================================================
2025-08-04 08:24:12,883 - INFO - 
📋 Running test: Notification Analysis
2025-08-04 08:24:12,883 - INFO - ----------------------------------------
2025-08-04 08:24:12,883 - INFO - 🔍 Testing notification analysis for removal action generation...
2025-08-04 08:24:12,999 - INFO - Logger configured to write logs to: logs/manual_test_execution_20250804_082412.log
2025-08-04 08:24:12,999 - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-04 08:24:15,572 - INFO - Configuration loaded successfully
2025-08-04 08:24:16,035 - INFO - Processed 0 chunks from content
2025-08-04 08:24:16,035 - INFO - Chunks: []
2025-08-04 08:24:16,036 - INFO - Logger configured to write logs to: logs/manual_test_execution_20250804_082416.log
2025-08-04 08:24:16,036 - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-04 08:24:16,047 - INFO - Configured Hugging Face HTTP backend to bypass SSL verification
2025-08-04 08:24:16,048 - INFO - Configuration loaded successfully
2025-08-04 08:24:17,287 - INFO - Loaded SPLADE model: naver/efficient-splade-VI-BT-large-doc
2025-08-04 08:24:17,287 - INFO - Initializing QdrantManager with URL: http://localhost:6333
2025-08-04 08:24:17,301 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:24:17,305 - INFO - HTTP Request: GET http://localhost:6333/collections "HTTP/1.1 200 OK"
2025-08-04 08:24:17,306 - INFO - ✅ QdrantClient initialized successfully
2025-08-04 08:24:18,171 - INFO - ✅ Sparse embedder initialized with model: Qdrant/bm25
2025-08-04 08:24:18,172 - INFO - 🔍 FastEmbed creates sparse vector type: <class 'langchain_qdrant.sparse_embeddings.SparseVector'>
2025-08-04 08:24:18,172 - INFO - 🔍 FastEmbed sparse vector indices count: 1
2025-08-04 08:24:18,188 - INFO - Initialized key rotator with 2 keys
2025-08-04 08:24:18,188 - INFO - ✅ Environment setup complete
2025-08-04 08:24:18,189 - INFO - ✅ Real notification processor initialized
2025-08-04 08:24:18,189 - INFO - Logger configured to write logs to: logs/knowledge_base_executor_execution_20250804_082418.log
2025-08-04 08:24:18,189 - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-04 08:24:18,201 - INFO - Use pytorch device_name: mps
2025-08-04 08:24:18,201 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:24:21,972 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:24:21,972 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:24:21,972 - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-04 08:24:21,973 - INFO - Use pytorch device_name: mps
2025-08-04 08:24:21,973 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:24:25,495 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:24:25,496 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:24:25,496 - INFO - ✅ Metadata vector search initialized
2025-08-04 08:24:25,496 - INFO - 🧠 Selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-04 08:24:25,497 - INFO - Use pytorch device_name: mps
2025-08-04 08:24:25,497 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:24:28,791 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:24:28,791 - INFO - ✅ Direct metadata search components initialized
2025-08-04 08:24:28,791 - INFO - 📋 Testing with notification: Amendment to Guidelines on Risk Management - Supersedes RBI/FED/2023-24/15
2025-08-04 08:24:28,791 - INFO - 🔍 Step 1: Analyzing notification...
2025-08-04 08:24:28,791 - INFO - 🔍 Starting notification analysis for: Amendment to Guidelines on Risk Management - Super...
2025-08-04 08:24:28,791 - INFO - 📏 Notification length: 316 characters
2025-08-04 08:24:28,792 - INFO - 📝 Prompt created, making LLM call...
2025-08-04 08:24:28,794 - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:24:31,327 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 08:24:31,348 - INFO - ✅ Notification categorized as: Superseded (confidence: high)
2025-08-04 08:24:31,348 - INFO - 📊 Analysis result: {
  "category": "Superseded",
  "confidence": "high",
  "reasoning": "The notification explicitly states that it supersedes the earlier circular RBI/FED/2023-24/15 and amends the Guidelines on Risk Management and Inter-Bank Dealings. It instructs banks to follow the revised guidelines immediately, indicating a regulatory change that replaces previous instructions.",
  "affects_regulations": true,
  "keywords_found": [
    "amends",
    "superseded",
    "revised guidelines",
    "follow with immediate effect"
  ],
  "requires_kb_update": true
}
2025-08-04 08:24:31,348 - INFO - 🔍 Step 2: Extracting affected documents...
2025-08-04 08:24:31,348 - INFO - 🔍 Extracting affected documents for: Amendment to Guidelines on Risk Management - Super...
2025-08-04 08:24:31,348 - INFO - 📏 Notification length: 316 characters
2025-08-04 08:24:31,355 - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:24:38,290 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 08:24:38,304 - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:24:44,946 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 08:24:44,949 - INFO - ✅ Found 1 unique document actions (0 removals for supersession)
2025-08-04 08:24:44,949 - INFO - 📊 Action reduction: 2 → 1 after deduplication
2025-08-04 08:24:44,950 - INFO - 📊 Affected documents: {
  "document_actions": [
    {
      "document_id": "RBI/FED/2023-24/15",
      "action_type": "REMOVE_DOCUMENT",
      "confidence": "high",
      "document_title": "Guidelines on Risk Management and Inter-Bank Dealings",
      "document_url": null,
      "reference_number": "RBI/FED/2023-24/15",
      "department": "FED",
      "original_date": "2023-03-15",
      "update_location": "full-document",
      "sunset_withdraw_date": null,
      "reasoning": "This is the new circular amending the Guidelines on Risk Management and Inter-Bank Dealings, and it supersedes the previous version. It should be added to the knowledge base as the current applicable document.. As per supersession rules, the older version must be removed from the knowledge base and replaced by the new version.. The notification explicitly states that RBI/FED/2023-24/15 is hereby superseded by the new circular",
      "source_chunks": []
    }
  ],
  "document_keywords": [
    ""
  ],
  "has_new_document_link": false,
  "new_document_url": "",
  "rbi_links": [],
  "processing_notes": "Processed 0 additional chunks, found 2 total actions, deduplicated to 1 actions"
}
2025-08-04 08:24:44,950 - INFO - 🎯 Found 1 REMOVE_DOCUMENT actions:
2025-08-04 08:24:44,950 - INFO -    1. Target: N/A
2025-08-04 08:24:44,950 - INFO -       Reasoning: This is the new circular amending the Guidelines on Risk Management and Inter-Bank Dealings, and it supersedes the previous version. It should be added to the knowledge base as the current applicable document.. As per supersession rules, the older version must be removed from the knowledge base and replaced by the new version.. The notification explicitly states that RBI/FED/2023-24/15 is hereby superseded by the new circular
2025-08-04 08:24:44,950 - INFO - 🔍 Step 3: Determining update actions...
2025-08-04 08:24:44,950 - INFO - 🎯 Determining update actions for: Amendment to Guidelines on Risk Management - Super...
2025-08-04 08:24:44,950 - INFO - 📏 Notification length: 316 characters
2025-08-04 08:24:44,958 - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:24:47,943 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 08:24:47,953 - INFO - ✅ Generated action ID: REMOVE_DOCUMENT_RBI_FED_2023-24_15_c39470710_0804_0824
2025-08-04 08:24:47,953 - WARNING - ⚠️ Action missing URLs: ['new_document_url', 'rbi_page_url'] for action type: REMOVE_DOCUMENT
2025-08-04 08:24:47,953 - INFO - ✅ Generated action ID: ADD_DOCUMENT_RBI_FED_2024-25_XX_c39470710_0804_0824
2025-08-04 08:24:47,954 - WARNING - ⚠️ Action missing URLs: ['new_document_url', 'rbi_page_url'] for action type: ADD_DOCUMENT
2025-08-04 08:24:47,954 - INFO - ✅ Generated 2 update actions
2025-08-04 08:24:47,954 - INFO - 📊 Update actions: {
  "actions": [
    {
      "action_type": "REMOVE_DOCUMENT",
      "target_document": "RBI/FED/2023-24/15",
      "priority": "high",
      "details": "Superseded by new amendment; remove from knowledge base.",
      "expiry_date": null,
      "new_document_url": null,
      "rbi_page_url": null,
      "action_id": "REMOVE_DOCUMENT_RBI_FED_2023-24_15_c39470710_0804_0824"
    },
    {
      "action_type": "ADD_DOCUMENT",
      "target_document": "RBI/FED/2024-25/XX",
      "priority": "high",
      "details": "Add new amendment circular as current guidelines on Risk Management and Inter-Bank Dealings.",
      "expiry_date": null,
      "new_document_url": null,
      "rbi_page_url": null,
      "action_id": "ADD_DOCUMENT_RBI_FED_2024-25_XX_c39470710_0804_0824"
    }
  ],
  "processing_notes": "Supersession is clear; no manual review required. New document ID assumed as RBI/FED/2024-25/XX (update with actual ID if available).",
  "requires_manual_review": false
}
2025-08-04 08:24:47,954 - INFO - 🎯 Found 1 REMOVE_DOCUMENT actions in update actions:
2025-08-04 08:24:47,954 - INFO -    1. Target: RBI/FED/2023-24/15
2025-08-04 08:24:47,954 - INFO -       Details: Superseded by new amendment; remove from knowledge base.
2025-08-04 08:24:47,954 - INFO - ✅ SUCCESS: Found 2 total removal actions
2025-08-04 08:24:47,960 - INFO - ✅ PASSED Notification Analysis
2025-08-04 08:24:47,960 - INFO - 
📋 Running test: KB Execution
2025-08-04 08:24:47,960 - INFO - ----------------------------------------
2025-08-04 08:24:47,960 - INFO - 🔍 Testing KB execution with mock removal action...
2025-08-04 08:24:47,964 - INFO - Logger configured to write logs to: logs/knowledge_base_executor_execution_20250804_082447.log
2025-08-04 08:24:47,964 - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-04 08:24:47,968 - INFO - Use pytorch device_name: mps
2025-08-04 08:24:47,968 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:24:51,235 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:24:51,235 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:24:51,235 - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-04 08:24:51,235 - INFO - 🎯 Executing removal action: {'action_type': 'REMOVE_DOCUMENT', 'target_document': 'RBI/FED/2023-24/15', 'collection_name': ['rbi_circular'], 'use_vector_search': True, 'document_id': 'RBI/FED/2023-24/15', 'reasoning': 'Superseded by new circular'}
2025-08-04 08:24:51,235 - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: REMOVE_DOCUMENT
2025-08-04 08:24:51,235 - INFO - [QDRANT] Action details: REMOVE_DOCUMENT
2025-08-04 08:24:51,236 - INFO - 🗑️ [QDRANT] Executing REMOVE_DOCUMENT action: {"action_type": "REMOVE_DOCUMENT", "target_document": "RBI/FED/2023-24/15", "collection_name": ["rbi_circular"], "use_vector_search": true, "document_id": "RBI/FED/2023-24/15", "reasoning": "Superseded by new circular"}
2025-08-04 08:24:51,236 - INFO - 🔍 [QDRANT] Target collections for removal: ['rbi_circular']
2025-08-04 08:24:51,236 - INFO - [QDRANT] 🔍 Trying to find documents to remove using vector search
2025-08-04 08:24:51,236 - INFO - [QDRANT] 🔍 Search parameters: document_id='RBI/FED/2023-24/15', document_number=''
2025-08-04 08:24:51,237 - INFO - Use pytorch device_name: mps
2025-08-04 08:24:51,237 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:24:54,380 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:24:54,380 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:24:54,381 - INFO - [QDRANT] 🔍 Searching across collections: ['rbi_circular']
2025-08-04 08:24:54,381 - INFO - [QDRANT] 🔍 SEARCHING BY DOCUMENT ID: 'RBI/FED/2023-24/15'
2025-08-04 08:24:54,460 - INFO - Searching metadata_vectors for 'RBI/FED/2023-24/15' in field 'document_id'
2025-08-04 08:24:54,482 - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-04 08:24:54,482 - INFO - Found 10 results for metadata search
2025-08-04 08:24:54,482 - INFO - [QDRANT] 🔍 PERFORMING FULL TEXT SEARCH with: 'RBI/FED/2023-24/15'
2025-08-04 08:24:54,492 - INFO - Searching metadata_vectors for 'RBI/FED/2023-24/15' in field 'short_summary'
2025-08-04 08:24:54,504 - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-04 08:24:54,504 - INFO - Found 10 results for metadata search
2025-08-04 08:24:54,504 - INFO - [QDRANT] ✅ Found 10 matches in 'short_summary' vector
2025-08-04 08:24:54,515 - INFO - Searching metadata_vectors for 'RBI/FED/2023-24/15' in field 'content'
2025-08-04 08:24:54,518 - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 400 Bad Request"
2025-08-04 08:24:54,518 - ERROR - Error in metadata vector search: Unexpected Response: 400 (Bad Request)
Raw response content:
b'{"status":{"error":"Wrong input: Vector with name `content_vec` is not configured in this collection, available names: document_id_vec, document_number_vec, document_title_vec, short_summary_vec"}, ...'
2025-08-04 08:24:54,518 - INFO - [QDRANT] ➕ Added 10 new unique matches from text search
2025-08-04 08:24:54,519 - INFO - [QDRANT] 🎯 Found total of 10 potential documents to remove via vector search
2025-08-04 08:24:54,519 - INFO - [QDRANT] 📊 FINAL SEARCH RESULTS SUMMARY:
2025-08-04 08:24:54,519 - INFO - [QDRANT] #1. Score: 0.6216 | ID: 38553b01-10a1-45e7-8bc1-dd4a71e02576 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:24:54,519 - INFO - [QDRANT] #2. Score: 0.6216 | ID: eb015e72-8375-46de-8eaf-d927bcf54102 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:24:54,519 - INFO - [QDRANT] #3. Score: 0.6216 | ID: 150aabca-3d4b-4722-a7f2-342fa87d5ed9 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:24:54,519 - INFO - [QDRANT] #4. Score: 0.6216 | ID: bf944bbe-f6bc-4269-9e4a-a72afdfc777d | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:24:54,519 - INFO - [QDRANT] #5. Score: 0.6216 | ID: f7ee1056-586e-4a86-8a1e-f8d8a08c1361 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:24:54,519 - INFO - [QDRANT] #6. Score: 0.6216 | ID: 81835ca3-9c65-4491-94a8-6b88ca352557 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:24:54,519 - INFO - [QDRANT] #7. Score: 0.6216 | ID: f7c4d7aa-f3d9-440d-822d-1bced88e50d8 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:24:54,519 - INFO - [QDRANT] #8. Score: 0.6216 | ID: 2f658f80-79c7-4ad0-b52f-5c5ae831dc01 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:24:54,519 - INFO - [QDRANT] #9. Score: 0.6216 | ID: d3a7519d-d3e9-4485-bb3a-6b28a1782cf5 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:24:54,519 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:24:54,520 - INFO - [QDRANT] #10. Score: 0.6196 | ID: 0e08f04e-5bd9-4ced-89ed-24b0c9e2c49e | DocID: RBI/2017-18/165  Ref. No. IDMD/2669/08.02.032/2017-18
2025-08-04 08:24:54,520 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:24:54,520 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:24:54,520 - INFO - [QDRANT] 🎯 Using extracted document IDs for filter: ['RBI/2017-18/165  Ref. No. IDMD/2669/08.02.032/2017-18', 'RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23']
2025-08-04 08:24:54,520 - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for REMOVE_DOCUMENT
2025-08-04 08:24:54,520 - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action_type', 'target_document', 'collection_name', 'use_vector_search', 'document_id', 'reasoning']
2025-08-04 08:24:54,520 - INFO - [QDRANT] 🔧 BUILD FILTER: No filter_fields provided
2025-08-04 08:24:54,520 - INFO - [QDRANT] 🔧 BUILD FILTER: Adding metadata filter: metadata.document_id = RBI/2017-18/165  Ref. No. IDMD/2669/08.02.032/2017-18
2025-08-04 08:24:54,520 - INFO - [QDRANT] 🔧 BUILD FILTER: Created filter with 2 conditions
2025-08-04 08:24:54,520 - INFO - [QDRANT] REMOVE: Collection=rbi_circular, Filter=should=None min_should=None must=[FieldCondition(key='metadata.document_id', match=MatchValue(value='RBI/2017-18/165  Ref. No. IDMD/2669/08.02.032/2017-18'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None), FieldCondition(key='document_id', match=MatchValue(value='RBI/2017-18/165  Ref. No. IDMD/2669/08.02.032/2017-18'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None)] must_not=None
2025-08-04 08:24:54,596 - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-04 08:24:54,597 - WARNING - [QDRANT] ⚠️ No documents found matching filter in rbi_circular
2025-08-04 08:24:54,597 - INFO - [QDRANT] 🔍 Using reliable fields for matching: document_id
2025-08-04 08:24:54,597 - INFO - 🔍 [QDRANT] REMOVE operation completed with result: {"action": "REMOVE_DOCUMENT", "success": false, "details": "No matching documents found for ID: RBI/2017-18/165  Ref. No. IDMD/2669/08.02.032/2017-18", "timestamp": "2025-08-04T02:54:54.597262"}
2025-08-04 08:24:54,597 - INFO - [QDRANT] Action REMOVE_DOCUMENT executed with result: {"action": "REMOVE_DOCUMENT", "success": false, "details": "No matching documents found for ID: RBI/2017-18/165  Ref. No. IDMD/2669/08.02.032/2017-18", "timestamp": "2025-08-04T02:54:54.597262"}
2025-08-04 08:24:54,597 - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-04 08:24:54,597 - INFO - [QDRANT] Action 1: REMOVE_DOCUMENT - ❌ FAILED
2025-08-04 08:24:54,600 - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:24:54,601 - INFO - 📊 Execution results: [
  {
    "action": "REMOVE_DOCUMENT",
    "success": false,
    "details": "No matching documents found for ID: RBI/2017-18/165  Ref. No. IDMD/2669/08.02.032/2017-18",
    "timestamp": "2025-08-04T02:54:54.597262"
  }
]
2025-08-04 08:24:54,601 - WARNING - ⚠️ Removal action failed: No matching documents found for ID: RBI/2017-18/165  Ref. No. IDMD/2669/08.02.032/2017-18
2025-08-04 08:24:54,601 - INFO - ❌ FAILED KB Execution
2025-08-04 08:24:54,601 - INFO - 
📋 Running test: End-to-End Flow
2025-08-04 08:24:54,601 - INFO - ----------------------------------------
2025-08-04 08:24:54,601 - INFO - 🔍 Testing end-to-end removal action flow...
2025-08-04 08:24:54,601 - INFO - Initialized key rotator with 2 keys
2025-08-04 08:24:54,601 - INFO - ✅ Environment setup complete
2025-08-04 08:24:54,602 - INFO - ✅ Real notification processor initialized
2025-08-04 08:24:54,602 - INFO - Logger configured to write logs to: logs/knowledge_base_executor_execution_20250804_082454.log
2025-08-04 08:24:54,602 - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-04 08:24:54,604 - INFO - Use pytorch device_name: mps
2025-08-04 08:24:54,604 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:24:57,789 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:24:57,790 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:24:57,790 - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-04 08:24:57,791 - INFO - Use pytorch device_name: mps
2025-08-04 08:24:57,791 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:25:01,101 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:25:01,102 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:25:01,102 - INFO - ✅ Metadata vector search initialized
2025-08-04 08:25:01,102 - INFO - 🧠 Selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-04 08:25:01,103 - INFO - Use pytorch device_name: mps
2025-08-04 08:25:01,103 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:25:04,354 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:25:04,354 - INFO - ✅ Direct metadata search components initialized
2025-08-04 08:25:04,354 - INFO - 📋 Testing end-to-end with: Revised Guidelines on Risk Management - Supersedes RBI/FED/2023-24/15
2025-08-04 08:25:04,356 - INFO - ✅ Code extraction successful - Short: RBI/FED/2023-24/15, Long: 
2025-08-04 08:25:04,357 - INFO - 🚀 ENHANCED Processing notification: Revised Guidelines on Risk Management - Supersedes...
2025-08-04 08:25:04,357 - INFO - 📊 Initial notification codes from title: {'short_code': 'RBI/FED/2023-24/15', 'long_code': '', 'full_code': 'RBI/FED/2023-24/15', 'year': '2023', 'all_codes': ['RBI/FED/2023-24/15']}
2025-08-04 08:25:04,357 - INFO - 📝 Using description content: 274 chars
2025-08-04 08:25:04,357 - INFO - ⚠️ No local PDF available, using codes from title only: {'short_code': 'RBI/FED/2023-24/15', 'long_code': '', 'full_code': 'RBI/FED/2023-24/15', 'year': '2023', 'all_codes': ['RBI/FED/2023-24/15']}
2025-08-04 08:25:04,357 - INFO - --- ENHANCED Stage 1: Notification Analysis ---
2025-08-04 08:25:04,357 - INFO - 🔍 Starting notification analysis for: Revised Guidelines on Risk Management - Supersedes...
2025-08-04 08:25:04,357 - INFO - 📏 Notification length: 274 characters
2025-08-04 08:25:04,357 - INFO - 📝 Prompt created, making LLM call...
2025-08-04 08:25:04,360 - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:25:06,306 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 08:25:06,309 - INFO - ✅ Notification categorized as: Superseded (confidence: high)
2025-08-04 08:25:06,315 - INFO - 🧠 Smart Model Selection: gpt-4.1 for regulatory_interpretation - Selected complex tier for regulatory_interpretation (context: 421, priority: quality)
2025-08-04 08:25:09,401 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 08:25:09,408 - INFO - 📋 KB Decision: update_existing (store: True, remove: True)
2025-08-04 08:25:09,408 - INFO - --- ENHANCED Stage 2: Notification-level KB Actions ---
2025-08-04 08:25:09,408 - INFO - notification_codes being passed to KB storage: {'short_code': 'RBI/FED/2023-24/15', 'long_code': '', 'full_code': 'RBI/FED/2023-24/15', 'year': '2023', 'all_codes': ['RBI/FED/2023-24/15']}
2025-08-04 08:25:09,408 - INFO - 🔍 Detecting document type for flowchart-based processing...
2025-08-04 08:25:09,408 - INFO - 🔍 Detecting document type from title: Revised Guidelines on Risk Management - Supersedes RBI/FED/2023-24/15...
2025-08-04 08:25:09,408 - INFO - 📋 Target collection from KB decision: rbi_circular
2025-08-04 08:25:09,408 - INFO - ✅ Detected: Other
2025-08-04 08:25:09,408 - INFO - 📋 Document Type Detected: other
2025-08-04 08:25:09,408 - INFO - 🎯 Generating flowchart-based actions for document type: other
2025-08-04 08:25:09,409 - INFO - 📑 Processing Other document actions...
2025-08-04 08:25:09,409 - INFO - ✅ Added: ADD_DOCUMENT action for Other document
2025-08-04 08:25:09,409 - INFO - 🎯 Generated 1 KB actions based on flowchart:
2025-08-04 08:25:09,409 - INFO -   📝 Action 1: ADD_DOCUMENT in rbi_other
2025-08-04 08:25:09,409 - INFO -       🎯 Target: RBI/FED/2023-24/15
2025-08-04 08:25:09,409 - INFO -       🤔 Reasoning: Other notification type - follows flowchart logic
2025-08-04 08:25:09,409 - INFO -       📊 Confidence: 0.9
2025-08-04 08:25:09,409 - INFO - ✅ Executing 1 flowchart-based KB actions
2025-08-04 08:25:09,409 - INFO -   📝 Action 1: ADD_DOCUMENT for collection rbi_other
2025-08-04 08:25:09,409 - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:25:09,409 - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:25:09,409 - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=126075d8-8eef-57b7-9250-335f6a572fd6, Document=RBI/FED/2023-24/15, Chunk=None
2025-08-04 08:25:09,409 - INFO - ✅ Using qdrant_manager.upsert_point for RBI/FED/2023-24/15 with text: 568 chars
2025-08-04 08:25:09,413 - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_other/exists "HTTP/1.1 200 OK"
2025-08-04 08:25:09,419 - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_other "HTTP/1.1 200 OK"
2025-08-04 08:25:09,421 - INFO - 🔍 Collection rbi_other vectors: size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-04 08:25:09,421 - INFO - 🔍 Collection rbi_other sparse vectors: None
2025-08-04 08:25:09,422 - WARNING - Collection rbi_other exists but uses hybrid/single format. Will work with existing.
2025-08-04 08:25:11,445 - INFO - Embedding 1 documents
2025-08-04 08:25:12,902 - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 08:25:13,013 - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_other "HTTP/1.1 200 OK"
2025-08-04 08:25:13,013 - INFO - Collection info for rbi_other: vectors=size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-04 08:25:13,013 - INFO - Collection format: single
2025-08-04 08:25:13,013 - WARNING - Collection 'rbi_other' is single‑vector. Using single-vector format...
2025-08-04 08:25:13,026 - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_other/points?wait=true "HTTP/1.1 200 OK"
2025-08-04 08:25:13,026 - INFO - ✅ Used single-vector format for point 126075d8-8eef-57b7-9250-335f6a572fd6 (dense only)
2025-08-04 08:25:13,026 - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/FED/2023-24/15 (ID: 126075d8-8eef-57b7-9250-335f6a572fd6)
2025-08-04 08:25:13,026 - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/FED/2023-24/15 (ID: 126075d8-8eef-57b7-9250-335f6a572fd6) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:55:13.026839", "document_id": "RBI/FED/2023-24/15", "uuid": "126075d8-8eef-57b7-9250-335f6a572fd6", "collection": "rbi_other"}
2025-08-04 08:25:13,026 - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:25:13,026 - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:25:13,027 - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:25:13,027 - INFO - ✅ Flowchart-based KB Results summary:
2025-08-04 08:25:13,027 - INFO -   📋 Result 1: ADD_DOCUMENT - SUCCESS
2025-08-04 08:25:13,027 - INFO -       📦 Collection: rbi_other
2025-08-04 08:25:13,027 - INFO -       🆔 UUID: 126075d8-8eef-57b7-9250-335f6a572fd6
2025-08-04 08:25:13,027 - INFO -       🤔 Reasoning: Other notification type - follows flowchart logic
2025-08-04 08:25:13,027 - INFO -       📊 Confidence: 0.9
2025-08-04 08:25:13,027 - INFO - 📊 Added 1 documents with UUIDs: ['126075d8-8eef-57b7-9250-335f6a572fd6']
2025-08-04 08:25:13,027 - INFO - 📊 Adding notification to metadata_vectors collection for enhanced search
2025-08-04 08:25:13,029 - INFO - Configured Hugging Face HTTP backend to bypass SSL verification
2025-08-04 08:25:14,179 - INFO - Loaded SPLADE model: naver/efficient-splade-VI-BT-large-doc
2025-08-04 08:25:14,179 - INFO - Initializing QdrantManager with URL: http://localhost:6333
2025-08-04 08:25:14,193 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:25:14,196 - INFO - HTTP Request: GET http://localhost:6333/collections "HTTP/1.1 200 OK"
2025-08-04 08:25:14,196 - INFO - ✅ QdrantClient initialized successfully
2025-08-04 08:25:15,058 - INFO - ✅ Sparse embedder initialized with model: Qdrant/bm25
2025-08-04 08:25:15,058 - INFO - 🔍 FastEmbed creates sparse vector type: <class 'langchain_qdrant.sparse_embeddings.SparseVector'>
2025-08-04 08:25:15,058 - INFO - 🔍 FastEmbed sparse vector indices count: 1
2025-08-04 08:25:15,058 - INFO - 🧠 Using smart-selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-04 08:25:15,059 - INFO - Use pytorch device_name: mps
2025-08-04 08:25:15,059 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:25:18,583 - INFO - 🔍 Adding notification to metadata_vectors with ID: 15dcddc7-2d1e-44da-b41e-853a83727db2
2025-08-04 08:25:18,583 - INFO - 🔍 Vector keys: ['document_title_vec', 'document_number_vec', 'short_summary_vec', 'document_id_vec']
2025-08-04 08:25:18,584 - INFO - 🔍 Vector dimensions: [('document_title_vec', 384), ('document_number_vec', 384), ('short_summary_vec', 384), ('document_id_vec', 384)]
2025-08-04 08:25:18,591 - INFO - HTTP Request: GET http://localhost:6333/collections/metadata_vectors "HTTP/1.1 200 OK"
2025-08-04 08:25:18,625 - INFO - HTTP Request: PUT http://localhost:6333/collections/metadata_vectors/points?wait=true "HTTP/1.1 200 OK"
2025-08-04 08:25:18,625 - INFO - ✅ Added notification to metadata_vectors: 15dcddc7-2d1e-44da-b41e-853a83727db2
2025-08-04 08:25:18,626 - INFO -    📊 Notification metadata vectors UUIDs: 1
2025-08-04 08:25:18,626 - INFO - --- ENHANCED Stage 3: STREAMLINED PDF Processing and Action Execution ---
2025-08-04 08:25:18,626 - INFO - ⚠️ No local PDF available for streamlined processing
2025-08-04 08:25:18,626 - INFO - 📊 Processing result keys: ['notification', 'analysis', 'kb_decision', 'notification_codes', 'pdf_processing', 'added_uuids', 'operation_details', 'processing_timestamp', 'status']
2025-08-04 08:25:18,627 - INFO - ✅ PASSED End-to-End Flow
2025-08-04 08:25:18,627 - INFO - 
============================================================
2025-08-04 08:25:18,627 - INFO - 🎯 Debug session complete. Check logs for details.
2025-08-04 08:29:09,180 - INFO - 🚀 Starting removal action debugging...
2025-08-04 08:29:09,181 - INFO - ============================================================
2025-08-04 08:29:09,181 - INFO - 
📋 Running test: Notification Analysis
2025-08-04 08:29:09,181 - INFO - ----------------------------------------
2025-08-04 08:29:09,181 - INFO - 🔍 Testing notification analysis for removal action generation...
2025-08-04 08:29:09,279 - INFO - Logger configured to write logs to: logs/manual_test_execution_20250804_082909.log
2025-08-04 08:29:09,279 - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-04 08:29:11,853 - INFO - Configuration loaded successfully
2025-08-04 08:29:12,259 - INFO - Processed 0 chunks from content
2025-08-04 08:29:12,259 - INFO - Chunks: []
2025-08-04 08:29:12,259 - INFO - Logger configured to write logs to: logs/manual_test_execution_20250804_082912.log
2025-08-04 08:29:12,259 - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-04 08:29:12,267 - INFO - Configured Hugging Face HTTP backend to bypass SSL verification
2025-08-04 08:29:12,340 - INFO - Configuration loaded successfully
2025-08-04 08:29:13,551 - INFO - Loaded SPLADE model: naver/efficient-splade-VI-BT-large-doc
2025-08-04 08:29:13,551 - INFO - Initializing QdrantManager with URL: http://localhost:6333
2025-08-04 08:29:13,564 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:29:13,568 - INFO - HTTP Request: GET http://localhost:6333/collections "HTTP/1.1 200 OK"
2025-08-04 08:29:13,569 - INFO - ✅ QdrantClient initialized successfully
2025-08-04 08:29:14,511 - INFO - ✅ Sparse embedder initialized with model: Qdrant/bm25
2025-08-04 08:29:14,511 - INFO - 🔍 FastEmbed creates sparse vector type: <class 'langchain_qdrant.sparse_embeddings.SparseVector'>
2025-08-04 08:29:14,511 - INFO - 🔍 FastEmbed sparse vector indices count: 1
2025-08-04 08:29:14,526 - INFO - Initialized key rotator with 2 keys
2025-08-04 08:29:14,526 - INFO - ✅ Environment setup complete
2025-08-04 08:29:14,527 - INFO - ✅ Real notification processor initialized
2025-08-04 08:29:14,527 - INFO - Logger configured to write logs to: logs/knowledge_base_executor_execution_20250804_082914.log
2025-08-04 08:29:14,527 - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-04 08:29:14,540 - INFO - Use pytorch device_name: mps
2025-08-04 08:29:14,540 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:29:18,064 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:29:18,065 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:29:18,065 - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-04 08:29:18,066 - INFO - Use pytorch device_name: mps
2025-08-04 08:29:18,066 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:29:21,294 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:29:21,295 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:29:21,295 - INFO - ✅ Metadata vector search initialized
2025-08-04 08:29:21,295 - INFO - 🧠 Selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-04 08:29:21,296 - INFO - Use pytorch device_name: mps
2025-08-04 08:29:21,296 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:29:24,579 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:29:24,580 - INFO - ✅ Direct metadata search components initialized
2025-08-04 08:29:24,580 - INFO - 📋 Testing with notification: Amendment to Guidelines on Risk Management - Supersedes RBI/FED/2023-24/15
2025-08-04 08:29:24,580 - INFO - 🔍 Step 1: Analyzing notification...
2025-08-04 08:29:24,580 - INFO - 🔍 Starting notification analysis for: Amendment to Guidelines on Risk Management - Super...
2025-08-04 08:29:24,580 - INFO - 📏 Notification length: 316 characters
2025-08-04 08:29:24,580 - INFO - 📝 Prompt created, making LLM call...
2025-08-04 08:29:24,584 - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:29:26,656 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 08:29:26,676 - INFO - ✅ Notification categorized as: Superseded (confidence: high)
2025-08-04 08:29:26,676 - INFO - 📊 Analysis result: {
  "category": "Superseded",
  "confidence": "high",
  "reasoning": "The notification explicitly states that it supersedes the earlier circular RBI/FED/2023-24/15 and amends the Guidelines on Risk Management and Inter-Bank Dealings. It instructs banks to follow revised guidelines, indicating a regulatory change that replaces previous instructions.",
  "affects_regulations": true,
  "keywords_found": [
    "amends",
    "superseded",
    "revised guidelines",
    "follow with immediate effect"
  ],
  "requires_kb_update": true
}
2025-08-04 08:29:26,676 - INFO - 🔍 Step 2: Extracting affected documents...
2025-08-04 08:29:26,676 - INFO - 🔍 Extracting affected documents for: Amendment to Guidelines on Risk Management - Super...
2025-08-04 08:29:26,676 - INFO - 📏 Notification length: 316 characters
2025-08-04 08:29:26,682 - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:29:31,060 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 08:29:31,074 - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:29:36,078 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 08:29:36,081 - INFO - ✅ Found 1 unique document actions (0 removals for supersession)
2025-08-04 08:29:36,081 - INFO - 📊 Action reduction: 2 → 1 after deduplication
2025-08-04 08:29:36,082 - INFO - 📊 Affected documents: {
  "document_actions": [
    {
      "document_id": "RBI/FED/2023-24/15",
      "action_type": "REMOVE_DOCUMENT",
      "confidence": "high",
      "document_title": "Guidelines on Risk Management and Inter-Bank Dealings (2023)",
      "document_url": null,
      "reference_number": "RBI/FED/2023-24/15",
      "department": "FED",
      "original_date": "2023-03-15",
      "update_location": "full-document",
      "sunset_withdraw_date": null,
      "reasoning": "This is the new amendment circular that supersedes the previous guidelines. This document is explicitly stated as being superseded by the new amendment circular in the notification. As per RBI versioning rules, the older version must be removed when a new version is issued and supersedes it.. It should be added to the knowledge base as the current applicable document.",
      "source_chunks": []
    }
  ],
  "document_keywords": [
    ""
  ],
  "has_new_document_link": false,
  "new_document_url": "",
  "rbi_links": [],
  "processing_notes": "Processed 0 additional chunks, found 2 total actions, deduplicated to 1 actions"
}
2025-08-04 08:29:36,083 - INFO - 🎯 Found 1 REMOVE_DOCUMENT actions:
2025-08-04 08:29:36,083 - INFO -    1. Target: N/A
2025-08-04 08:29:36,083 - INFO -       Reasoning: This is the new amendment circular that supersedes the previous guidelines. This document is explicitly stated as being superseded by the new amendment circular in the notification. As per RBI versioning rules, the older version must be removed when a new version is issued and supersedes it.. It should be added to the knowledge base as the current applicable document.
2025-08-04 08:29:36,083 - INFO - 🔍 Step 3: Determining update actions...
2025-08-04 08:29:36,083 - INFO - 🎯 Determining update actions for: Amendment to Guidelines on Risk Management - Super...
2025-08-04 08:29:36,083 - INFO - 📏 Notification length: 316 characters
2025-08-04 08:29:36,091 - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:29:39,147 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 08:29:39,159 - INFO - ✅ Generated action ID: REMOVE_DOCUMENT_RBI_FED_2023-24_15_c39470710_0804_0829
2025-08-04 08:29:39,160 - WARNING - ⚠️ Action missing URLs: ['new_document_url', 'rbi_page_url'] for action type: REMOVE_DOCUMENT
2025-08-04 08:29:39,160 - INFO - ✅ Generated action ID: ADD_DOCUMENT_RBI_FED_2024-25_17_c39470710_0804_0829
2025-08-04 08:29:39,160 - WARNING - ⚠️ Action missing URLs: ['new_document_url', 'rbi_page_url'] for action type: ADD_DOCUMENT
2025-08-04 08:29:39,161 - INFO - ✅ Generated 2 update actions
2025-08-04 08:29:39,161 - INFO - 📊 Update actions: {
  "actions": [
    {
      "action_type": "REMOVE_DOCUMENT",
      "target_document": "RBI/FED/2023-24/15",
      "priority": "high",
      "details": "Superseded by new amendment; remove from active knowledge base.",
      "expiry_date": null,
      "new_document_url": null,
      "rbi_page_url": null,
      "action_id": "REMOVE_DOCUMENT_RBI_FED_2023-24_15_c39470710_0804_0829"
    },
    {
      "action_type": "ADD_DOCUMENT",
      "target_document": "RBI/FED/2024-25/17",
      "priority": "high",
      "details": "Add new amendment circular as current applicable guidelines on risk management.",
      "expiry_date": null,
      "new_document_url": null,
      "rbi_page_url": null,
      "action_id": "ADD_DOCUMENT_RBI_FED_2024-25_17_c39470710_0804_0829"
    }
  ],
  "processing_notes": "Supersession is explicit; document IDs are clear. No URLs found in content.",
  "requires_manual_review": false
}
2025-08-04 08:29:39,161 - INFO - 🎯 Found 1 REMOVE_DOCUMENT actions in update actions:
2025-08-04 08:29:39,161 - INFO -    1. Target: RBI/FED/2023-24/15
2025-08-04 08:29:39,161 - INFO -       Details: Superseded by new amendment; remove from active knowledge base.
2025-08-04 08:29:39,161 - INFO - ✅ SUCCESS: Found 2 total removal actions
2025-08-04 08:29:39,169 - INFO - ✅ PASSED Notification Analysis
2025-08-04 08:29:39,169 - INFO - 
📋 Running test: KB Execution
2025-08-04 08:29:39,169 - INFO - ----------------------------------------
2025-08-04 08:29:39,169 - INFO - 🔍 Testing KB execution with mock removal action...
2025-08-04 08:29:39,173 - INFO - Logger configured to write logs to: logs/knowledge_base_executor_execution_20250804_082939.log
2025-08-04 08:29:39,173 - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-04 08:29:39,176 - INFO - Use pytorch device_name: mps
2025-08-04 08:29:39,176 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:29:42,503 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:29:42,504 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:29:42,504 - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-04 08:29:42,504 - INFO - 🎯 Executing removal action: {'action_type': 'REMOVE_DOCUMENT', 'target_document': 'RBI/FED/2023-24/15', 'collection_name': ['rbi_circular'], 'use_vector_search': True, 'document_id': 'RBI/FED/2023-24/15', 'reasoning': 'Superseded by new circular'}
2025-08-04 08:29:42,504 - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: REMOVE_DOCUMENT
2025-08-04 08:29:42,504 - INFO - [QDRANT] Action details: REMOVE_DOCUMENT
2025-08-04 08:29:42,504 - INFO - 🗑️ [QDRANT] Executing REMOVE_DOCUMENT action: {"action_type": "REMOVE_DOCUMENT", "target_document": "RBI/FED/2023-24/15", "collection_name": ["rbi_circular"], "use_vector_search": true, "document_id": "RBI/FED/2023-24/15", "reasoning": "Superseded by new circular"}
2025-08-04 08:29:42,504 - INFO - 🔍 [QDRANT] Target collections for removal: ['rbi_circular']
2025-08-04 08:29:42,505 - INFO - [QDRANT] 🔍 Trying to find documents to remove using vector search
2025-08-04 08:29:42,505 - INFO - [QDRANT] 🔍 Search parameters: document_id='RBI/FED/2023-24/15', document_number=''
2025-08-04 08:29:42,506 - INFO - Use pytorch device_name: mps
2025-08-04 08:29:42,506 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:29:45,674 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:29:45,674 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:29:45,674 - INFO - [QDRANT] 🔍 Searching across collections: ['rbi_circular']
2025-08-04 08:29:45,674 - INFO - [QDRANT] 🔍 SEARCHING BY DOCUMENT ID: 'RBI/FED/2023-24/15'
2025-08-04 08:29:45,758 - INFO - Searching metadata_vectors for 'RBI/FED/2023-24/15' in field 'document_id'
2025-08-04 08:29:45,778 - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-04 08:29:45,779 - INFO - Found 10 results for metadata search
2025-08-04 08:29:45,779 - INFO - [QDRANT] 🔍 PERFORMING FULL TEXT SEARCH with: 'RBI/FED/2023-24/15'
2025-08-04 08:29:45,789 - INFO - Searching metadata_vectors for 'RBI/FED/2023-24/15' in field 'short_summary'
2025-08-04 08:29:45,805 - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-04 08:29:45,805 - INFO - Found 10 results for metadata search
2025-08-04 08:29:45,806 - INFO - [QDRANT] ✅ Found 10 matches in 'short_summary' vector
2025-08-04 08:29:45,816 - INFO - Searching metadata_vectors for 'RBI/FED/2023-24/15' in field 'content'
2025-08-04 08:29:45,820 - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 400 Bad Request"
2025-08-04 08:29:45,820 - ERROR - Error in metadata vector search: Unexpected Response: 400 (Bad Request)
Raw response content:
b'{"status":{"error":"Wrong input: Vector with name `content_vec` is not configured in this collection, available names: document_id_vec, document_number_vec, document_title_vec, short_summary_vec"}, ...'
2025-08-04 08:29:45,820 - INFO - [QDRANT] ➕ Added 10 new unique matches from text search
2025-08-04 08:29:45,820 - INFO - [QDRANT] 🎯 Found total of 10 potential documents to remove via vector search
2025-08-04 08:29:45,820 - INFO - [QDRANT] 📊 FINAL SEARCH RESULTS SUMMARY:
2025-08-04 08:29:45,820 - INFO - [QDRANT] #1. Score: 0.6216 | ID: 38553b01-10a1-45e7-8bc1-dd4a71e02576 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,820 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,820 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,820 - INFO - [QDRANT] #2. Score: 0.6216 | ID: eb015e72-8375-46de-8eaf-d927bcf54102 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,820 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,820 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,820 - INFO - [QDRANT] #3. Score: 0.6216 | ID: 150aabca-3d4b-4722-a7f2-342fa87d5ed9 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,820 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,820 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,820 - INFO - [QDRANT] #4. Score: 0.6216 | ID: bf944bbe-f6bc-4269-9e4a-a72afdfc777d | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,820 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,820 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,820 - INFO - [QDRANT] #5. Score: 0.6216 | ID: f7ee1056-586e-4a86-8a1e-f8d8a08c1361 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,820 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,820 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,820 - INFO - [QDRANT] #6. Score: 0.6216 | ID: 81835ca3-9c65-4491-94a8-6b88ca352557 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,820 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,820 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,821 - INFO - [QDRANT] #7. Score: 0.6216 | ID: f7c4d7aa-f3d9-440d-822d-1bced88e50d8 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,821 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,821 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,821 - INFO - [QDRANT] #8. Score: 0.6216 | ID: 2f658f80-79c7-4ad0-b52f-5c5ae831dc01 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,821 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,821 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,821 - INFO - [QDRANT] #9. Score: 0.6216 | ID: d3a7519d-d3e9-4485-bb3a-6b28a1782cf5 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,821 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,821 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,821 - INFO - [QDRANT] #10. Score: 0.6196 | ID: 0e08f04e-5bd9-4ced-89ed-24b0c9e2c49e | DocID: RBI/2017-18/165  Ref. No. IDMD/2669/08.02.032/2017-18
2025-08-04 08:29:45,821 - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,821 - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,821 - WARNING - [QDRANT] ⚠️ No similar matches found for RBI/FED/2023-24/15, keeping original target
2025-08-04 08:29:45,821 - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for REMOVE_DOCUMENT
2025-08-04 08:29:45,821 - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action_type', 'target_document', 'collection_name', 'use_vector_search', 'document_id', 'reasoning', 'filter_fields']
2025-08-04 08:29:45,821 - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'metadata.document_id': 'RBI/FED/2023-24/15'}
2025-08-04 08:29:45,821 - INFO - [QDRANT] 🔧 BUILD FILTER: Adding filter: metadata.document_id = RBI/FED/2023-24/15
2025-08-04 08:29:45,822 - INFO - [QDRANT] 🔧 BUILD FILTER: Adding metadata filter: metadata.document_id = RBI/FED/2023-24/15
2025-08-04 08:29:45,822 - INFO - [QDRANT] 🔧 BUILD FILTER: Created filter with 3 conditions
2025-08-04 08:29:45,822 - INFO - [QDRANT] REMOVE: Collection=rbi_circular, Filter=should=None min_should=None must=[FieldCondition(key='metadata.document_id', match=MatchValue(value='RBI/FED/2023-24/15'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None), FieldCondition(key='metadata.document_id', match=MatchValue(value='RBI/FED/2023-24/15'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None), FieldCondition(key='document_id', match=MatchValue(value='RBI/FED/2023-24/15'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None)] must_not=None
2025-08-04 08:29:45,822 - INFO - [QDRANT] REMOVE filter fields: {"metadata.document_id": "RBI/FED/2023-24/15"}
2025-08-04 08:29:45,906 - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-04 08:29:45,907 - WARNING - [QDRANT] ⚠️ No documents found matching filter in rbi_circular
2025-08-04 08:29:45,907 - INFO - [QDRANT] 🔍 Using reliable fields for matching: document_id
2025-08-04 08:29:45,907 - INFO - 🔍 [QDRANT] REMOVE operation completed with result: {"action": "REMOVE_DOCUMENT", "success": false, "details": "No matching documents found for ID: RBI/FED/2023-24/15", "timestamp": "2025-08-04T02:59:45.907116"}
2025-08-04 08:29:45,907 - INFO - [QDRANT] Action REMOVE_DOCUMENT executed with result: {"action": "REMOVE_DOCUMENT", "success": false, "details": "No matching documents found for ID: RBI/FED/2023-24/15", "timestamp": "2025-08-04T02:59:45.907116"}
2025-08-04 08:29:45,907 - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-04 08:29:45,907 - INFO - [QDRANT] Action 1: REMOVE_DOCUMENT - ❌ FAILED
2025-08-04 08:29:45,909 - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:29:45,914 - INFO - 📊 Execution results: [
  {
    "action": "REMOVE_DOCUMENT",
    "success": false,
    "details": "No matching documents found for ID: RBI/FED/2023-24/15",
    "timestamp": "2025-08-04T02:59:45.907116"
  }
]
2025-08-04 08:29:45,914 - WARNING - ⚠️ Removal action failed: No matching documents found for ID: RBI/FED/2023-24/15
2025-08-04 08:29:45,914 - INFO - ❌ FAILED KB Execution
2025-08-04 08:29:45,914 - INFO - 
📋 Running test: End-to-End Flow
2025-08-04 08:29:45,914 - INFO - ----------------------------------------
2025-08-04 08:29:45,914 - INFO - 🔍 Testing end-to-end removal action flow...
2025-08-04 08:29:45,914 - INFO - Initialized key rotator with 2 keys
2025-08-04 08:29:45,914 - INFO - ✅ Environment setup complete
2025-08-04 08:29:45,915 - INFO - ✅ Real notification processor initialized
2025-08-04 08:29:45,916 - INFO - Logger configured to write logs to: logs/knowledge_base_executor_execution_20250804_082945.log
2025-08-04 08:29:45,916 - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-04 08:29:45,917 - INFO - Use pytorch device_name: mps
2025-08-04 08:29:45,917 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:29:49,145 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:29:49,145 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:29:49,145 - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-04 08:29:49,146 - INFO - Use pytorch device_name: mps
2025-08-04 08:29:49,146 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:29:52,266 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:29:52,267 - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-04 08:29:52,267 - INFO - ✅ Metadata vector search initialized
2025-08-04 08:29:52,267 - INFO - 🧠 Selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-04 08:29:52,268 - INFO - Use pytorch device_name: mps
2025-08-04 08:29:52,268 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:29:55,483 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:29:55,483 - INFO - ✅ Direct metadata search components initialized
2025-08-04 08:29:55,483 - INFO - 📋 Testing end-to-end with: Revised Guidelines on Risk Management - Supersedes RBI/FED/2023-24/15
2025-08-04 08:29:55,486 - INFO - ✅ Code extraction successful - Short: RBI/FED/2023-24/15, Long: 
2025-08-04 08:29:55,486 - INFO - 🚀 ENHANCED Processing notification: Revised Guidelines on Risk Management - Supersedes...
2025-08-04 08:29:55,486 - INFO - 📊 Initial notification codes from title: {'short_code': 'RBI/FED/2023-24/15', 'long_code': '', 'full_code': 'RBI/FED/2023-24/15', 'year': '2023', 'all_codes': ['RBI/FED/2023-24/15']}
2025-08-04 08:29:55,486 - INFO - 📝 Using description content: 274 chars
2025-08-04 08:29:55,486 - INFO - ⚠️ No local PDF available, using codes from title only: {'short_code': 'RBI/FED/2023-24/15', 'long_code': '', 'full_code': 'RBI/FED/2023-24/15', 'year': '2023', 'all_codes': ['RBI/FED/2023-24/15']}
2025-08-04 08:29:55,486 - INFO - --- ENHANCED Stage 1: Notification Analysis ---
2025-08-04 08:29:55,486 - INFO - 🔍 Starting notification analysis for: Revised Guidelines on Risk Management - Supersedes...
2025-08-04 08:29:55,486 - INFO - 📏 Notification length: 274 characters
2025-08-04 08:29:55,486 - INFO - 📝 Prompt created, making LLM call...
2025-08-04 08:29:55,490 - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:29:57,377 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 08:29:57,380 - INFO - ✅ Notification categorized as: Superseded (confidence: high)
2025-08-04 08:29:57,385 - INFO - 🧠 Smart Model Selection: gpt-4.1 for regulatory_interpretation - Selected complex tier for regulatory_interpretation (context: 421, priority: quality)
2025-08-04 08:29:59,425 - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-04 08:29:59,429 - INFO - 📋 KB Decision: update_existing (store: True, remove: True)
2025-08-04 08:29:59,430 - INFO - --- ENHANCED Stage 2: Notification-level KB Actions ---
2025-08-04 08:29:59,430 - INFO - notification_codes being passed to KB storage: {'short_code': 'RBI/FED/2023-24/15', 'long_code': '', 'full_code': 'RBI/FED/2023-24/15', 'year': '2023', 'all_codes': ['RBI/FED/2023-24/15']}
2025-08-04 08:29:59,430 - INFO - 🔍 Detecting document type for flowchart-based processing...
2025-08-04 08:29:59,430 - INFO - 🔍 Detecting document type from title: Revised Guidelines on Risk Management - Supersedes RBI/FED/2023-24/15...
2025-08-04 08:29:59,430 - INFO - 📋 Target collection from KB decision: rbi_circular
2025-08-04 08:29:59,430 - INFO - ✅ Detected: Other
2025-08-04 08:29:59,431 - INFO - 📋 Document Type Detected: other
2025-08-04 08:29:59,431 - INFO - 🎯 Generating flowchart-based actions for document type: other
2025-08-04 08:29:59,431 - INFO - 📑 Processing Other document actions...
2025-08-04 08:29:59,431 - INFO - ✅ Added: ADD_DOCUMENT action for Other document
2025-08-04 08:29:59,431 - INFO - 🎯 Generated 1 KB actions based on flowchart:
2025-08-04 08:29:59,431 - INFO -   📝 Action 1: ADD_DOCUMENT in rbi_other
2025-08-04 08:29:59,431 - INFO -       🎯 Target: RBI/FED/2023-24/15
2025-08-04 08:29:59,431 - INFO -       🤔 Reasoning: Other notification type - follows flowchart logic
2025-08-04 08:29:59,431 - INFO -       📊 Confidence: 0.9
2025-08-04 08:29:59,432 - INFO - ✅ Executing 1 flowchart-based KB actions
2025-08-04 08:29:59,432 - INFO -   📝 Action 1: ADD_DOCUMENT for collection rbi_other
2025-08-04 08:29:59,432 - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:29:59,432 - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:29:59,432 - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=170b3594-a3a7-5a05-a1ca-7d6874b5df61, Document=RBI/FED/2023-24/15, Chunk=None
2025-08-04 08:29:59,432 - INFO - ✅ Using qdrant_manager.upsert_point for RBI/FED/2023-24/15 with text: 568 chars
2025-08-04 08:29:59,438 - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_other/exists "HTTP/1.1 200 OK"
2025-08-04 08:29:59,445 - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_other "HTTP/1.1 200 OK"
2025-08-04 08:29:59,447 - INFO - 🔍 Collection rbi_other vectors: size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-04 08:29:59,447 - INFO - 🔍 Collection rbi_other sparse vectors: None
2025-08-04 08:29:59,447 - WARNING - Collection rbi_other exists but uses hybrid/single format. Will work with existing.
2025-08-04 08:30:02,467 - INFO - Embedding 1 documents
2025-08-04 08:30:03,112 - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-04 08:30:03,301 - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_other "HTTP/1.1 200 OK"
2025-08-04 08:30:03,302 - INFO - Collection info for rbi_other: vectors=size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-04 08:30:03,302 - INFO - Collection format: single
2025-08-04 08:30:03,302 - WARNING - Collection 'rbi_other' is single‑vector. Using single-vector format...
2025-08-04 08:30:03,313 - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_other/points?wait=true "HTTP/1.1 200 OK"
2025-08-04 08:30:03,313 - INFO - ✅ Used single-vector format for point 170b3594-a3a7-5a05-a1ca-7d6874b5df61 (dense only)
2025-08-04 08:30:03,313 - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/FED/2023-24/15 (ID: 170b3594-a3a7-5a05-a1ca-7d6874b5df61)
2025-08-04 08:30:03,314 - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/FED/2023-24/15 (ID: 170b3594-a3a7-5a05-a1ca-7d6874b5df61) to collections: ['rbi_other']", "timestamp": "2025-08-04T03:00:03.313994", "document_id": "RBI/FED/2023-24/15", "uuid": "170b3594-a3a7-5a05-a1ca-7d6874b5df61", "collection": "rbi_other"}
2025-08-04 08:30:03,314 - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:30:03,314 - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:30:03,315 - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:30:03,315 - INFO - ✅ Flowchart-based KB Results summary:
2025-08-04 08:30:03,315 - INFO -   📋 Result 1: ADD_DOCUMENT - SUCCESS
2025-08-04 08:30:03,315 - INFO -       📦 Collection: rbi_other
2025-08-04 08:30:03,315 - INFO -       🆔 UUID: 170b3594-a3a7-5a05-a1ca-7d6874b5df61
2025-08-04 08:30:03,315 - INFO -       🤔 Reasoning: Other notification type - follows flowchart logic
2025-08-04 08:30:03,315 - INFO -       📊 Confidence: 0.9
2025-08-04 08:30:03,315 - INFO - 📊 Added 1 documents with UUIDs: ['170b3594-a3a7-5a05-a1ca-7d6874b5df61']
2025-08-04 08:30:03,315 - INFO - 📊 Adding notification to metadata_vectors collection for enhanced search
2025-08-04 08:30:03,317 - INFO - Configured Hugging Face HTTP backend to bypass SSL verification
2025-08-04 08:30:04,445 - INFO - Loaded SPLADE model: naver/efficient-splade-VI-BT-large-doc
2025-08-04 08:30:04,446 - INFO - Initializing QdrantManager with URL: http://localhost:6333
2025-08-04 08:30:04,461 - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-04 08:30:04,463 - INFO - HTTP Request: GET http://localhost:6333/collections "HTTP/1.1 200 OK"
2025-08-04 08:30:04,463 - INFO - ✅ QdrantClient initialized successfully
2025-08-04 08:30:05,361 - INFO - ✅ Sparse embedder initialized with model: Qdrant/bm25
2025-08-04 08:30:05,361 - INFO - 🔍 FastEmbed creates sparse vector type: <class 'langchain_qdrant.sparse_embeddings.SparseVector'>
2025-08-04 08:30:05,361 - INFO - 🔍 FastEmbed sparse vector indices count: 1
2025-08-04 08:30:05,361 - INFO - 🧠 Using smart-selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-04 08:30:05,362 - INFO - Use pytorch device_name: mps
2025-08-04 08:30:05,362 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-04 08:30:09,205 - INFO - 🔍 Adding notification to metadata_vectors with ID: 30b2edc1-27a9-44c8-ab7d-2b77fb049e73
2025-08-04 08:30:09,206 - INFO - 🔍 Vector keys: ['document_title_vec', 'document_number_vec', 'short_summary_vec', 'document_id_vec']
2025-08-04 08:30:09,206 - INFO - 🔍 Vector dimensions: [('document_title_vec', 384), ('document_number_vec', 384), ('short_summary_vec', 384), ('document_id_vec', 384)]
2025-08-04 08:30:09,210 - INFO - HTTP Request: GET http://localhost:6333/collections/metadata_vectors "HTTP/1.1 200 OK"
2025-08-04 08:30:09,248 - INFO - HTTP Request: PUT http://localhost:6333/collections/metadata_vectors/points?wait=true "HTTP/1.1 200 OK"
2025-08-04 08:30:09,248 - INFO - ✅ Added notification to metadata_vectors: 30b2edc1-27a9-44c8-ab7d-2b77fb049e73
2025-08-04 08:30:09,248 - INFO -    📊 Notification metadata vectors UUIDs: 1
2025-08-04 08:30:09,248 - INFO - --- ENHANCED Stage 3: STREAMLINED PDF Processing and Action Execution ---
2025-08-04 08:30:09,248 - INFO - ⚠️ No local PDF available for streamlined processing
2025-08-04 08:30:09,248 - INFO - 📊 Processing result keys: ['notification', 'analysis', 'kb_decision', 'notification_codes', 'pdf_processing', 'added_uuids', 'operation_details', 'processing_timestamp', 'status']
2025-08-04 08:30:09,250 - INFO - ✅ PASSED End-to-End Flow
2025-08-04 08:30:09,250 - INFO - 
============================================================
2025-08-04 08:30:09,250 - INFO - 🎯 Debug session complete. Check logs for details.
