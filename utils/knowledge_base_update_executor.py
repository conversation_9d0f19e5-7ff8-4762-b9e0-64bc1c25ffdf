import logging
import hashlib
import uuid
import json
import re
import traceback
from datetime import datetime

# Qdrant imports
try:
    # Try newer location first
    from qdrant_client.http.models import Filter, FieldCondition, MatchValue, PointStruct, MinShould, HasIdCondition
except ImportError:
    # Fallback for older Qdrant versions
    from qdrant_client.models import Filter, FieldCondition, MatchV<PERSON>ue, PointStruct, MinShould, HasIdCondition

from qdrant_client.http import models
from qdrant_utils import qdrant_manager

# Optional imports - will be used if available
try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    import numpy as np
    from typing import Dict, List, Tuple
    from scipy.sparse import csr_matrix
    VECTORIZER_AVAILABLE = True
except ImportError:
    VECTORIZER_AVAILABLE = False
    logging.warning("TfidfVectorizer not available, sparse vector generation will be limited")

class KnowledgeBaseUpdateExecutor:
    # Mapping from doc_type to Qdrant collection name
    DOC_TYPE_COLLECTION_MAP = {
        'master_direction': 'rbi_master_direction',
        'master_circular': 'rbi_master_circular',
        'circular': 'rbi_circular',
        'circular_ext': 'rbi_circular_ext',
        # Add more mappings as needed
    }
    """
    Executes knowledge base update actions in Qdrant, with exact and fuzzy matching support
    across multiple collections and metadata fields.

    If `collection_name` is omitted, actions will target all default collections.
    Supported metadata keys for filtering: document_id, document_title, short_id,
    long_code, document_number.
    """

    # Default collections to search when no explicit collection is provided
    DEFAULT_COLLECTIONS = [
        'rbi_master_direction',
        'rbi_master_circular',
        'rbi_circular',
    ]

    METADATA_MAP = {
        'document_id': 'metadata.document_id',
        'document_title': 'metadata.document_title',
        'short_id': 'metadata.short_id',
        'long_code': 'metadata.long_code',
        'document_number': 'metadata.document_number',
        'target_document_id': 'notification_metadata.target_document_id',
    }

    def __init__(self):
        # Configure proper logging with file handler that flushes
        try:
            from utils.logging_utils import setup_logging
            self.logger = setup_logging(
                log_dir="logs", 
                module_name="knowledge_base_executor",
                flush_interval=1  # Flush every second
            )
        except ImportError:
            # Fall back to basic logger if the utility isn't available
            self.logger = logging.getLogger(__name__)
            # Ensure the logger has a file handler that flushes frequently
            if not any(isinstance(h, logging.FileHandler) for h in self.logger.handlers):
                log_dir = "logs"
                import os
                if not os.path.exists(log_dir):
                    os.makedirs(log_dir)
                file_handler = logging.FileHandler(
                    f"{log_dir}/knowledge_base_executor.log", 
                    encoding='utf-8'
                )
                file_handler.setFormatter(logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                ))
                self.logger.addHandler(file_handler)
        
        # Initialize sparse vector generator if available
        if VECTORIZER_AVAILABLE:
            try:
                # Initialize sparse vector generator (BM25-like TF-IDF)
                self.sparse_vectorizer = TfidfVectorizer(
                    max_features=226,  # Match your sparse dimension
                    stop_words='english',
                    ngram_range=(1, 2),  # Include bigrams for better BM25-like behavior
                    sublinear_tf=True,   # Use log-scaled TF (closer to BM25)
                    norm='l2'            # L2 normalization
                )
                self._sparse_vocab_fitted = False
                self.logger.info("✅ Sparse vectorizer initialized successfully")
            except Exception as e:
                self.logger.error(f"❌ Error initializing sparse vectorizer: {e}")
                self.sparse_vectorizer = None
                self._sparse_vocab_fitted = False
        else:
            self.logger.warning("⚠️ Sparse vectorizer not available (missing sklearn)")
            self.sparse_vectorizer = None
            self._sparse_vocab_fitted = False
        
        # Initialize metadata vector search if available
        try:
            from utils.metadata_vector_utils import MetadataVectorSearch
            self.metadata_search = MetadataVectorSearch()
            self.logger.info("✅ Metadata vector search initialized in KB executor")
            self.has_metadata_search = True
        except Exception as e:
            self.logger.warning(f"⚠️ Could not initialize metadata vector search in KB executor: {e}")
            self.metadata_search = None
            self.has_metadata_search = False
        
        self.supported = {
            'REMOVE_DOCUMENT': self._remove,
            'ADD_DOCUMENT': self._add,
            'UPDATE_DOCUMENT': self._update,
            'ADD_TEMPORARY_NOTE': self._note,
            'NO_ACTION': lambda a, n, c=None: self._success(a, 'No action executed.')
        }

    def _validate_and_fix_action(self, action: dict, notification_data: dict) -> dict:
        """Validate and fix missing URLs and document IDs in actions"""
        try:
            action_type = action.get('action_type', '')
            
            # Fix missing target_document for REMOVE_DOCUMENT actions
            if action_type == 'REMOVE_DOCUMENT' and not action.get('target_document'):
                self.logger.warning(f"⚠️ REMOVE_DOCUMENT action missing target_document")
                
                # Try to extract from document_id or details
                document_id = action.get('document_id') or action.get('target_document_id')
                if document_id:
                    action['target_document'] = document_id
                    self.logger.info(f"✅ Fixed target_document using document_id: {document_id}")
                else:
                    # Try to extract from details field
                    details = action.get('details', '')
                    import re
                    doc_id_match = re.search(r'(RBI/[A-Z]+/\d{4}-\d{2}/\d+|[A-Z]+\.[A-Z]+\.[A-Z]+\.\d+/\d+\.\d+\.\d+/\d{4}-?\d{0,2})', details)
                    if doc_id_match:
                        extracted_id = doc_id_match.group(1)
                        action['target_document'] = extracted_id
                        self.logger.info(f"✅ Extracted target_document from details: {extracted_id}")
                    else:
                        # Don't set target_document if we can't find a valid value
                        self.logger.warning(f"⚠️ Could not extract target_document from details")
            
            # Fix missing URLs for all actions
            if notification_data:
                pdf_link = notification_data.get('PDF Link', '')
                
                # Fix new_document_url
                if not action.get('new_document_url') and pdf_link:
                    action['new_document_url'] = pdf_link
                    self.logger.info(f"✅ Fixed new_document_url: {pdf_link}")
                
                # Fix rbi_page_url
                if not action.get('rbi_page_url'):
                    if pdf_link and 'rbidocs.rbi.org.in' in pdf_link:
                        # Extract ID from PDF link and construct page URL
                        import re
                        pdf_match = re.search(r'PDFs/(.+?)\.PDF', pdf_link)
                        if pdf_match:
                            pdf_id = pdf_match.group(1)
                            rbi_page_url = f"https://rbi.org.in/Scripts/NotificationUser.aspx?Id={pdf_id}&Mode=0"
                            action['rbi_page_url'] = rbi_page_url
                            self.logger.info(f"✅ Constructed rbi_page_url: {rbi_page_url}")
                        else:
                            action['rbi_page_url'] = pdf_link  # Fallback
                            self.logger.info(f"✅ Used PDF link as rbi_page_url fallback: {pdf_link}")
                    elif pdf_link:
                        action['rbi_page_url'] = pdf_link  # Use PDF link as fallback
                        self.logger.info(f"✅ Used PDF link as rbi_page_url: {pdf_link}")
            
            return action
            
        except Exception as e:
            self.logger.error(f"❌ Error validating action: {e}")
            return action

    def _generate_point_id(self, string_id: str, chunk_index: int = None, timestamp: str = None) -> str:
        """Convert a string ID to a valid Qdrant point ID (UUID) with uniqueness factors"""
        # Create a unique UUID by combining string_id with chunk_index and timestamp
        unique_components = [string_id]
        
        if chunk_index is not None:
            unique_components.append(f"chunk_{chunk_index}")
        
        if timestamp:
            unique_components.append(timestamp)
        else:
            # Add current timestamp for uniqueness
            unique_components.append(datetime.now().isoformat())
        
        # Create unique string for UUID generation
        unique_string = "_".join(str(c) for c in unique_components)
        
        # Create a deterministic UUID from the unique string
        namespace = uuid.UUID('6ba7b810-9dad-11d1-80b4-00c04fd430c8')  # Standard namespace UUID
        return str(uuid.uuid5(namespace, unique_string))

    def _generate_sparse_vector(self, text: str) -> Dict[int, float]:
        """Generate SPLADE sparse vector for the given text."""
        try:
            # Use SPLADE instead of TF-IDF
            from utils.qdrant_utils import splade_encoder
            
            splade_vector = splade_encoder.encode(text)
            
            # Convert to dict format: {index: value}
            if hasattr(splade_vector, 'indices') and hasattr(splade_vector, 'values'):
                sparse_dict = {}
                for idx, val in zip(splade_vector.indices, splade_vector.values):
                    if val > 0:
                        sparse_dict[int(idx)] = float(val)
                return sparse_dict
            else:
                self.logger.warning("SPLADE vector format not recognized")
                return self._fallback_sparse_vector(text)
                
        except Exception as e:
            self.logger.warning(f"SPLADE encoding failed: {e}, using fallback")
            return self._fallback_sparse_vector(text)

    
    def _fallback_sparse_vector(self, text: str) -> Dict[int, float]:
        """
        Simple fallback method for generating sparse vectors when TfidfVectorizer is not available.
        Uses a simple word frequency approach with a hash function to map words to indices.
        """
        try:
            # Simple tokenization
            tokens = re.findall(r'\w+', text.lower())
            
            # Count term frequencies
            term_freq = {}
            for token in tokens:
                if len(token) > 2:  # Skip very short tokens
                    term_freq[token] = term_freq.get(token, 0) + 1
            
            # Convert to sparse format using hash function for indices
            sparse_dict = {}
            for term, freq in term_freq.items():
                # Use hash function to map term to an index in range [0, 225]
                idx = abs(hash(term)) % 226
                sparse_dict[idx] = sparse_dict.get(idx, 0) + freq
            
            # Normalize values
            if sparse_dict:
                max_val = max(sparse_dict.values())
                for idx in sparse_dict:
                    sparse_dict[idx] = sparse_dict[idx] / max_val
            
            self.logger.info(f"Generated fallback sparse vector with {len(sparse_dict)} elements")
            return sparse_dict
            
        except Exception as e:
            self.logger.error(f"Error in fallback sparse vector generation: {e}")
            return {}

    def execute_updates(self, actions: list, notification_data: dict, chunk_metadata: dict = None) -> list:
        # Import force_flush_logs if available
        try:
            from utils.logging_utils import force_flush_logs
            flush_logs_available = True
        except ImportError:
            flush_logs_available = False
            
        results = []
        for idx, action in enumerate(actions, start=1):
            try:
                typ = action.get('action_type')
                func = self.supported.get(typ)
                
                # Enhanced logging to provide clear visibility into action processing
                self.logger.info(f"[QDRANT] ▶️ EXECUTING ACTION {idx}/{len(actions)}: {typ}")
                
                # Flush logs after each significant log message
                for handler in self.logger.handlers:
                    handler.flush()
                
                # CRITICAL: Validate and fix missing URLs and document IDs before processing
                action = self._validate_and_fix_action(action, notification_data)
                
                # Safe JSON serialization
                try:
                    action_json = json.dumps(action, default=str)
                    self.logger.info(f"[QDRANT] Action details: {action.get('action_type', 'UNKNOWN')}")
                except Exception as json_err:
                    self.logger.warning(f"[QDRANT] Could not serialize action to JSON: {json_err}")
                    self.logger.info(f"[QDRANT] Action type: {typ}, Keys: {list(action.keys())}")
                
                # Flush logs again after processing
                for handler in self.logger.handlers:
                    handler.flush()
                    
                if not func:
                    msg = f"Unsupported action: {typ}"
                    self.logger.error(msg)
                    results.append(self._error(action, msg))
                    continue
            except Exception as e:
                self.logger.error(f"[QDRANT] Error processing action: {e}")
                results.append(self._error({'action_type': 'UNKNOWN'}, f"Error processing action: {e}"))
                continue

            # Assign collection_name based on doc_type if present
            doc_type = action.get('doc_type')
            if doc_type and doc_type in self.DOC_TYPE_COLLECTION_MAP:
                action['collection_name'] = self.DOC_TYPE_COLLECTION_MAP[doc_type]
            elif 'collection_name' not in action:
                action['collection_name'] = self.DEFAULT_COLLECTIONS

            try:
                # Fuzzy fallback if requested for remove/update
                if action.get('use_fuzzy') and typ in ('REMOVE_DOCUMENT', 'UPDATE_DOCUMENT'):
                    self.logger.info(f"[QDRANT] Fuzzy matching enabled for action: {action}")
                    ids = self._fuzzy_match(action)
                    self.logger.info(f"[QDRANT] Fuzzy match IDs: {ids}")
                    if ids:
                        action['filter_fields'] = {'_id': ids}
                    else:
                        warning = f"No fuzzy match ≥ {action.get('fuzzy_threshold')} for {action}"
                        self.logger.warning(warning)
                        results.append(self._error(action, 'No fuzzy match found'))
                        continue
                result = func(action, notification_data, chunk_metadata)
                self.logger.info(f"[QDRANT] Action {typ} executed with result: {json.dumps(result, default=str)}")
                results.append(result)
            except Exception as e:
                self.logger.error(f"[QDRANT] Error executing {typ}: {e}")
                self.logger.error(f"[QDRANT] Error traceback: {traceback.format_exc()}")
                results.append(self._error(action, str(e)))
        
        # Log overall execution summary
        success_count = sum(1 for r in results if r.get('success', False))
        self.logger.info(f"[QDRANT] Execution summary: {success_count}/{len(results)} actions succeeded")
        for idx, result in enumerate(results):
            action_type = result.get('action') or "UNKNOWN"
            status = "✅ SUCCESS" if result.get('success', False) else "❌ FAILED"
            self.logger.info(f"[QDRANT] Action {idx+1}: {action_type} - {status}")
        
        # Force flush all logs before returning
        try:
            from utils.logging_utils import force_flush_logs
            force_flush_logs()
            self.logger.info("[QDRANT] All logs flushed to disk")
        except ImportError:
            # Manual flush if the utility isn't available
            for handler in self.logger.handlers:
                handler.flush()
            for handler in logging.root.handlers:
                handler.flush()
            self.logger.info("[QDRANT] Logs manually flushed to disk")
            
        return results

    def _fuzzy_match(self, action: dict) -> list:
        from openai_utils import en_embeddings
        texts = [action.get(f) for f in action.get('fuzzy_fields', []) if action.get(f)]
        
        # Log all available fields in the action for debugging
        self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: Available action fields: {list(action.keys())}")
        self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: Action type: {action.get('action_type')}")
        
        # Log the target document if available
        if action.get('target_document'):
            self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: Target document: '{action.get('target_document')}'")
        if action.get('document_id'):
            self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: Document ID: '{action.get('document_id')}'")
            
        # Extract relevant fields for search
        document_id = action.get('document_id', '') or action.get('target_document', '')
        document_number = action.get('document_number', '')
        details = action.get('details', '')
        
        # Check if fuzzy_fields was provided
        fuzzy_fields = action.get('fuzzy_fields', [])
        self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: Specified fuzzy fields: {fuzzy_fields}")
        self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: Extracted texts: {texts}")
        
        if not texts and not document_id and not document_number:
            self.logger.warning(f"[QDRANT] ❌ FUZZY MATCH: No search texts, document_id, or document_number available")
            return []
            
        # First try metadata vector search if available
        try:
            from utils.metadata_vector_utils import MetadataVectorSearch
            metadata_search = MetadataVectorSearch()
            
            # Combine available search text
            search_text = ' '.join(texts) if texts else document_id
            if details and len(search_text) < 50:  # Add details if search text is short
                search_text += " " + details
                
            self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: Trying metadata vector search")
            self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: document_id='{document_id}', document_number='{document_number}'")
            self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: search_text='{search_text[:100]}...'")
            
            # Try different search methods
            results = []
            
            # If we have a document ID, try that first
            if document_id:
                self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: Searching by document ID '{document_id}'")
                id_results = metadata_search.search_document_by_id(document_id, top_k=10)
                if id_results:
                    self.logger.info(f"[QDRANT] ✅ Found {len(id_results)} matches by document ID")
                    self.logger.info(f"[QDRANT] 📋 DOCUMENT ID SEARCH RESULTS:")
                    for i, hit in enumerate(id_results):
                        doc_id = hit.payload.get('metadata', {}).get('document_id', hit.payload.get('document_id', 'unknown'))
                        title = hit.payload.get('metadata', {}).get('document_title', hit.payload.get('title', 'unknown'))
                        self.logger.info(f"[QDRANT]    {i+1}. ID={hit.id} | Score={hit.score:.4f} | DocID={doc_id} | Title={title}")
                    results.extend(id_results)
                else:
                    self.logger.warning(f"[QDRANT] ❌ No matches found by document ID: '{document_id}'")
                    
                    # Try a fallback search with partial document ID
                    if len(document_id) > 10:
                        partial_id = document_id[:10]
                        self.logger.info(f"[QDRANT] 🔍 TRYING FALLBACK SEARCH with partial document ID: '{partial_id}'")
                        fallback_matches = metadata_search.search_named_vector("document_id", partial_id, top_k=10)
                        if fallback_matches:
                            self.logger.info(f"[QDRANT] ✅ Found {len(fallback_matches)} matches by partial document ID")
                            results.extend(fallback_matches)
                    
            # If we have a document number, try that next
            if document_number and (not results or len(results) < 3):
                self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: Searching by document number '{document_number}'")
                num_results = metadata_search.search_document_by_number(document_number, top_k=10)
                if num_results:
                    self.logger.info(f"[QDRANT] ✅ Found {len(num_results)} matches by document number")
                    self.logger.info(f"[QDRANT] 📋 DOCUMENT NUMBER SEARCH RESULTS:")
                    for i, hit in enumerate(num_results):
                        doc_num = hit.payload.get('metadata', {}).get('document_number', 'unknown')
                        doc_id = hit.payload.get('metadata', {}).get('document_id', hit.payload.get('document_id', 'unknown'))
                        self.logger.info(f"[QDRANT]    {i+1}. ID={hit.id} | Score={hit.score:.4f} | DocID={doc_id} | DocNum={doc_num}")
                    
                    # Add only new results
                    existing_ids = {r.id for r in results}
                    new_matches = [r for r in num_results if r.id not in existing_ids]
                    results.extend(new_matches)
                    self.logger.info(f"[QDRANT] ➕ Added {len(new_matches)} new unique matches from document number search")
                else:
                    self.logger.warning(f"[QDRANT] ❌ No matches found by document number: '{document_number}'")
            
            # If we still don't have enough results, try searching by full text
            if not results or len(results) < 3:
                self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: Searching by text summary")
                
                # Try both short_summary and content vectors
                text_results = []
                for vector_name in ["short_summary", "content"]:
                    try:
                        vector_matches = metadata_search.search_named_vector(vector_name, search_text, top_k=10)
                        if vector_matches:
                            self.logger.info(f"[QDRANT] ✅ Found {len(vector_matches)} matches in '{vector_name}' vector")
                            text_results.extend(vector_matches)
                    except Exception as ve:
                        self.logger.warning(f"[QDRANT] ⚠️ Error searching '{vector_name}' vector: {ve}")
                
                if text_results:
                    self.logger.info(f"[QDRANT] 📋 TEXT SEARCH RESULTS:")
                    for i, hit in enumerate(text_results[:5]):  # Show top 5
                        doc_id = hit.payload.get('metadata', {}).get('document_id', hit.payload.get('document_id', 'unknown'))
                        score = hit.score
                        self.logger.info(f"[QDRANT]    {i+1}. ID={hit.id} | Score={score:.4f} | DocID={doc_id}")
                    
                    # Add only new results
                    existing_ids = {r.id for r in results}
                    new_matches = [r for r in text_results if r.id not in existing_ids]
                    results.extend(new_matches)
                    self.logger.info(f"[QDRANT] ➕ Added {len(new_matches)} new unique matches from text search")
                else:
                    self.logger.warning(f"[QDRANT] ❌ No matches found in text search")
            
            # If we found results through metadata search, return them
            if results:
                # Filter by threshold
                threshold = action.get('fuzzy_threshold', 0.8)
                filtered_results = [r for r in results if r.score >= threshold]
                
                self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: Applied threshold {threshold}: {len(filtered_results)}/{len(results)} results passed")
                
                if filtered_results:
                    self.logger.info(f"[QDRANT] 🎯 FUZZY MATCH: Final results that meet threshold:")
                    for i, hit in enumerate(filtered_results[:10]):  # Show top 10
                        doc_id = hit.payload.get('metadata', {}).get('document_id', hit.payload.get('document_id', 'unknown'))
                        title = hit.payload.get('metadata', {}).get('document_title', hit.payload.get('title', 'unknown'))
                        self.logger.info(f"[QDRANT] #{i+1}. Score: {hit.score:.4f} | ID: {hit.id} | DocID: {doc_id} | Title: {title}")
                    
                    return [pt.id for pt in filtered_results]
                else:
                    self.logger.info(f"[QDRANT] ⚠️ No metadata matches meet threshold {threshold}")
                    
        except Exception as e:
            self.logger.warning(f"[QDRANT] ❌ Metadata vector search failed: {e}")
            self.logger.warning(f"[QDRANT] ❌ Error traceback: {traceback.format_exc()}")
            self.logger.warning(f"[QDRANT] 🔄 Falling back to regular search")
        
        # Fall back to regular embedding search
        self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: Falling back to regular embedding search")
        
        try:
            query_text = ' '.join(texts) if texts else (document_id or details)
            self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: Embedding query text: '{query_text[:100]}...'")
            
            query_vec = en_embeddings.embed_query(query_text)
            self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: Successfully created embedding vector (dim: {len(query_vec)})")
            
            threshold = action.get('fuzzy_threshold', 0.8)
            self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: Using score threshold: {threshold}")
            
            # Try each default collection
            all_results = []
            for collection in self.DEFAULT_COLLECTIONS:
                try:
                    self.logger.info(f"[QDRANT] 🔍 FUZZY MATCH: Searching in collection '{collection}'")
                    collection_results = qdrant_manager.client.search(
                        collection_name=collection,
                        query_vector=query_vec,
                        with_payload=True,  # Get payload for debugging
                        limit=10,
                        score_threshold=threshold * 0.9  # Slightly lower threshold for more results
                    )
                    
                    if collection_results:
                        self.logger.info(f"[QDRANT] ✅ Found {len(collection_results)} matches in collection '{collection}'")
                        self.logger.info(f"[QDRANT] 📋 EMBEDDING SEARCH RESULTS ({collection}):")
                        for i, hit in enumerate(collection_results):
                            doc_id = hit.payload.get('metadata', {}).get('document_id', hit.payload.get('document_id', 'unknown'))
                            title = hit.payload.get('metadata', {}).get('document_title', hit.payload.get('title', 'unknown'))
                            self.logger.info(f"[QDRANT]    {i+1}. ID={hit.id} | Score={hit.score:.4f} | DocID={doc_id} | Title={title}")
                        all_results.extend(collection_results)
                    else:
                        self.logger.info(f"[QDRANT] ⚠️ No matches found in collection '{collection}'")
                except Exception as col_err:
                    self.logger.warning(f"[QDRANT] ❌ Error searching collection '{collection}': {col_err}")
            
            # Filter by threshold and remove duplicates
            filtered_results = []
            result_ids = set()
            for hit in sorted(all_results, key=lambda x: x.score, reverse=True):
                if hit.id not in result_ids and hit.score >= threshold:
                    filtered_results.append(hit)
                    result_ids.add(hit.id)
            
            if filtered_results:
                self.logger.info(f"[QDRANT] 🎯 FUZZY MATCH: Final {len(filtered_results)} results from embedding search")
                return [pt.id for pt in filtered_results]
            else:
                self.logger.warning(f"[QDRANT] ❌ No results meet threshold {threshold} in embedding search")
                return []
                
        except Exception as e:
            self.logger.error(f"[QDRANT] ❌ Embedding search failed: {e}")
            self.logger.error(f"[QDRANT] ❌ Error traceback: {traceback.format_exc()}")
            return []

    def _build_filter(self, action: dict) -> Filter:
        self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Starting filter construction for {action.get('action_type', 'unknown')}")
        self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Available action fields: {list(action.keys())}")
        
        # Import HasIdCondition at function level to ensure it's always available
        try:
            # Try newer location first
            from qdrant_client.http.models.models import HasIdCondition
        except ImportError:
            # Fallback for older versions
            from qdrant_client.models import HasIdCondition
        
        # Import the improved removal handler for document ID normalization
        try:
            from improved_removal_handler import normalize_document_id, handle_special_document_ids
        except ImportError:
            self.logger.warning("[QDRANT] 🔧 BUILD FILTER: improved_removal_handler not available")
        
        # Check if we have a target_document but no document_id
        # Only use target_document if it's not a special case
        target_doc = action.get('target_document', '')
        if target_doc and not action.get('document_id'):
            # Check if this is a special document ID that should be rejected
            try:
                is_valid, _ = handle_special_document_ids(target_doc)
                if not is_valid:
                    self.logger.warning(f"[QDRANT] 🔧 BUILD FILTER: Rejecting special document ID: {target_doc}")
                else:
                    # Normalize the document ID before using it
                    normalized_id = normalize_document_id(target_doc)
                    if normalized_id != target_doc:
                        self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Normalized document ID: '{target_doc}' → '{normalized_id}'")
                        target_doc = normalized_id
                    
                    action['document_id'] = target_doc
                    self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: {target_doc}")
            except NameError:
                # Handle the case where improved_removal_handler is not available
                if target_doc != 'MANUAL_REVIEW_REQUIRED':
                    action['document_id'] = target_doc
                    self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: {target_doc}")
        
        conds = []
        
        # Process explicit filter fields first
        filter_fields = action.get('filter_fields', {})
        if filter_fields:
            self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {filter_fields}")
            for path, val in filter_fields.items():
                if isinstance(val, list):
                    self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Adding list filter: {path} = [{len(val)} items]")
                    for v in val[:3]:  # Log first few values
                        self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: List item sample: {path} = {v}")
                    
                    # Special handling for _id field - we need to search in document_id and metadata.document_id fields
                    if path == '_id':
                        # For _id field, use HasIdCondition to match Qdrant's internal point IDs
                        self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: {val[:3]}...")
                        id_condition = HasIdCondition(has_id=val)
                        
                        # If we're at the top level with only point IDs, return immediately
                        if not conds:
                            self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for {len(val)} point IDs")
                            return Filter(must=[id_condition])
                        else:
                            # If we have other conditions, add HasIdCondition to them
                            self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Adding HasIdCondition with {len(val)} point IDs to existing conditions")
                            conds.append(id_condition)
                    else:
                        # For non-_id fields with list values, use proper approach based on Qdrant version
                        if path == 'ids':
                            # Direct ID lookup
                            self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for ID list")
                            conds.append(HasIdCondition(has_id=val))
                        else:
                            # Try to handle this as one-of matching if supported
                            try:
                                from qdrant_client.models import MatchAny
                                conds.append(FieldCondition(key=path, match=MatchAny(any=val)))
                                self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Using MatchAny for list values")
                            except ImportError:
                                # Fallback to individual conditions with OR
                                self.logger.warning(f"[QDRANT] 🔧 BUILD FILTER: MatchAny not available, using individual conditions")
                                list_conds = []
                                for item in val:
                                    list_conds.append(FieldCondition(key=path, match=MatchValue(value=item)))
                                # Add as a should filter
                                if list_conds:
                                    conds.append(Filter(should=list_conds, min_should=MinShould(min_count=1, conditions=list_conds)))
                else:
                    self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Adding filter: {path} = {val}")
                    # Skip None values to avoid validation errors
                    if val is not None:
                        conds.append(FieldCondition(key=path, match=MatchValue(value=val)))
                    else:
                        self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Skipping None value for {path}")
        else:
            self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: No filter_fields provided")
        
        # Add metadata filters from action fields using METADATA_MAP
        metadata_filters_added = 0
        for key, path in self.METADATA_MAP.items():
            if key in action and action[key] and action[key] != 'MANUAL_REVIEW_REQUIRED':
                value = action[key]
                self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Adding metadata filter: {path} = {value}")
                
                # Special handling for document_id to search in different fields
                if key == 'document_id':
                    # For document_id, add separate conditions for both fields
                    conds.append(FieldCondition(key='metadata.document_id', match=MatchValue(value=value)))
                    # Also add the regular document_id field
                    conds.append(FieldCondition(key='document_id', match=MatchValue(value=value)))
                else:
                    # Normal case for other fields
                    conds.append(FieldCondition(key=path, match=MatchValue(value=value)))
                metadata_filters_added += 1
                
        if metadata_filters_added == 0:
            self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: No metadata filters added from action fields")
            
            # Special case: if we have a target_document, try to use it directly
            # But only if it's not MANUAL_REVIEW_REQUIRED
            if action.get('target_document') and action.get('target_document') != 'MANUAL_REVIEW_REQUIRED' and not filter_fields:
                target_doc = action.get('target_document')
                self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Adding direct metadata.document_id filter using target_document: {target_doc}")
                conds.append(FieldCondition(key='metadata.document_id', match=MatchValue(value=target_doc)))
        
        # Create final filter
        if conds:
            # Force log flushing to ensure messages are written to file
            for handler in logging.root.handlers:
                handler.flush()
            result_filter = Filter(must=conds)
            self.logger.info(f"[QDRANT] 🔧 BUILD FILTER: Created filter with {len(conds)} conditions")
            return result_filter
        else:
            self.logger.warning(f"[QDRANT] 🔧 BUILD FILTER: No filter conditions could be created")
            return None

    def _remove(self, action: dict, notification_data: dict, chunk_metadata: dict = None) -> dict:
        # Import the improved removal handler
        from improved_removal_handler import handle_special_document_ids, normalize_document_id
        
        # Enhanced logging to clearly identify the action being processed
        action_type = action.get('action_type', 'REMOVE_DOCUMENT')
        self.logger.info(f"🗑️ [QDRANT] Executing {action_type} action: {json.dumps(action, default=str)}")
        cols = action['collection_name'] if isinstance(action['collection_name'], list) else [action['collection_name']]
        self.logger.info(f"🔍 [QDRANT] Target collections for removal: {cols}")
        executed = []
        
        # Check for special document IDs that should not be removed
        document_id = action.get('document_id', '') or action.get('target_document', '')
        if document_id:
            # Normalize the document ID
            normalized_id = normalize_document_id(document_id)
            if normalized_id != document_id:
                self.logger.info(f"[QDRANT] Normalized document ID: '{document_id}' → '{normalized_id}'")
                action['document_id'] = normalized_id
                document_id = normalized_id
            
            # Check if this is a special case document ID
            is_valid, reason = handle_special_document_ids(document_id)
            if not is_valid:
                self.logger.warning(f"[QDRANT] ⚠️ Document removal rejected: {reason}")
                return {"action": "REMOVE_DOCUMENT", 
                        "success": False, 
                        "details": reason,
                        "timestamp": datetime.now().isoformat()}
        
        # Check if we should use vector search to find documents to remove
        use_vector_search = action.get('use_vector_search', True)
        document_id = action.get('document_id', '') or action.get('target_document', '')
        document_number = action.get('document_number', '')
        
        # If we have document identifiers but no filter, try to use vector search
        if use_vector_search and (document_id or document_number) and not action.get('filter_fields'):
            self.logger.info(f"[QDRANT] 🔍 Trying to find documents to remove using vector search")
            self.logger.info(f"[QDRANT] 🔍 Search parameters: document_id='{document_id}', document_number='{document_number}'")
            
            try:
                from utils.metadata_vector_utils import MetadataVectorSearch
                metadata_search = MetadataVectorSearch()
                
                # Log collections being searched
                self.logger.info(f"[QDRANT] 🔍 Searching across collections: {cols}")
                
                # Use our vector search to find potentially superseded documents
                matches = []
                
                # Try document_id search first
                if document_id:
                    self.logger.info(f"[QDRANT] 🔍 SEARCHING BY DOCUMENT ID: '{document_id}'")
                    id_matches = metadata_search.search_named_vector("document_id", document_id, top_k=10)
                    
                    if id_matches:
                        self.logger.info(f"[QDRANT] ✅ Found {len(id_matches)} matches by document ID")
                        self.logger.info(f"[QDRANT] 📋 DOCUMENT ID SEARCH RESULTS:")
                        for i, hit in enumerate(id_matches):
                            doc_id = hit.payload.get('metadata', {}).get('document_id', hit.payload.get('document_id', 'unknown'))
                            title = hit.payload.get('metadata', {}).get('document_title', hit.payload.get('title', 'unknown'))
                            self.logger.info(f"[QDRANT]    {i+1}. ID={hit.id} | Score={hit.score:.4f} | DocID={doc_id} | Title={title}")
                        matches.extend(id_matches)
                    else:
                        self.logger.warning(f"[QDRANT] ❌ No matches found by document ID: '{document_id}'")
                        
                        # Try a fallback search with partial document ID
                        if len(document_id) > 10:
                            partial_id = document_id[:10]
                            self.logger.info(f"[QDRANT] 🔍 TRYING FALLBACK SEARCH with partial document ID: '{partial_id}'")
                            fallback_matches = metadata_search.search_named_vector("document_id", partial_id, top_k=10)
                            if fallback_matches:
                                self.logger.info(f"[QDRANT] ✅ Found {len(fallback_matches)} matches by partial document ID")
                                matches.extend(fallback_matches)
                
                # Try document_number search if needed
                if document_number and (not matches or len(matches) < 3):
                    self.logger.info(f"[QDRANT] 🔍 SEARCHING BY DOCUMENT NUMBER: '{document_number}'")
                    num_matches = metadata_search.search_named_vector("document_number", document_number, top_k=10)
                    
                    if num_matches:
                        self.logger.info(f"[QDRANT] ✅ Found {len(num_matches)} matches by document number")
                        self.logger.info(f"[QDRANT] 📋 DOCUMENT NUMBER SEARCH RESULTS:")
                        for i, hit in enumerate(num_matches):
                            doc_num = hit.payload.get('metadata', {}).get('document_number', 'unknown')
                            doc_id = hit.payload.get('metadata', {}).get('document_id', hit.payload.get('document_id', 'unknown'))
                            self.logger.info(f"[QDRANT]    {i+1}. ID={hit.id} | Score={hit.score:.4f} | DocID={doc_id} | DocNum={doc_num}")
                        
                        # Add only new results
                        existing_ids = {hit.id for hit in matches}
                        new_matches = [hit for hit in num_matches if hit.id not in existing_ids]
                        matches.extend(new_matches)
                        self.logger.info(f"[QDRANT] ➕ Added {len(new_matches)} new unique matches from document number search")
                    else:
                        self.logger.warning(f"[QDRANT] ❌ No matches found by document number: '{document_number}'")
                
                # Try text search as last resort
                if not matches or len(matches) < 2:
                    # Use target_document or document_id as text search
                    search_text = document_id
                    self.logger.info(f"[QDRANT] 🔍 PERFORMING FULL TEXT SEARCH with: '{search_text}'")
                    
                    # Try both short_summary and content vectors
                    text_matches = []
                    for vector_name in ["short_summary", "content"]:
                        try:
                            vector_matches = metadata_search.search_named_vector(vector_name, search_text, top_k=10)
                            if vector_matches:
                                self.logger.info(f"[QDRANT] ✅ Found {len(vector_matches)} matches in '{vector_name}' vector")
                                text_matches.extend(vector_matches)
                        except Exception as ve:
                            self.logger.warning(f"[QDRANT] ⚠️ Error searching '{vector_name}' vector: {ve}")
                    
                    # Add unique results
                    if text_matches:
                        existing_ids = {hit.id for hit in matches}
                        new_matches = [hit for hit in text_matches if hit.id not in existing_ids]
                        matches.extend(new_matches)
                        self.logger.info(f"[QDRANT] ➕ Added {len(new_matches)} new unique matches from text search")
                    else:
                        self.logger.warning(f"[QDRANT] ❌ No matches found in text search")
                
                # If we found matches, use them to build a filter
                if matches:
                    self.logger.info(f"[QDRANT] 🎯 Found total of {len(matches)} potential documents to remove via vector search")
                    
                    # Show detailed match information
                    self.logger.info(f"[QDRANT] 📊 FINAL SEARCH RESULTS SUMMARY:")
                    for i, hit in enumerate(matches[:10]):  # Show top 10 results
                        doc_id = hit.payload.get('metadata', {}).get('document_id', hit.payload.get('document_id', 'unknown'))
                        title = hit.payload.get('metadata', {}).get('document_title', hit.payload.get('title', 'unknown'))
                        content_preview = hit.payload.get('content', '')[:100] if hit.payload.get('content') else ''
                        self.logger.info(f"[QDRANT] #{i+1}. Score: {hit.score:.4f} | ID: {hit.id} | DocID: {doc_id}")
                        self.logger.info(f"[QDRANT]     Title: {title}")
                        self.logger.info(f"[QDRANT]     Preview: {content_preview}...")
                        
                    # Extract the document IDs from the matches
                    doc_ids = set()
                    for hit in matches:
                        doc_id = hit.payload.get('metadata', {}).get('document_id', hit.payload.get('document_id', ''))
                        if doc_id:
                            doc_ids.add(doc_id)
                    
                    if doc_ids:
                        # Compare extracted IDs with our original search ID
                        if document_id in doc_ids:
                            # If our original document_id is among the matches, keep using it
                            self.logger.info(f"[QDRANT] 🎯 Original document_id {document_id} found in results, using it directly")
                            action['document_id'] = document_id
                        elif "MANUAL_REVIEW_REQUIRED" not in doc_ids:
                            # Filter out "MANUAL_REVIEW_REQUIRED" if present
                            valid_ids = [id for id in doc_ids if id != "MANUAL_REVIEW_REQUIRED"]
                            if valid_ids:
                                # Otherwise use the extracted IDs directly
                                self.logger.info(f"[QDRANT] 🎯 Using extracted document IDs for filter: {valid_ids}")
                                action['document_id'] = valid_ids[0]  # Use the first document ID
                            else:
                                # If no valid IDs after filtering, fall back to original ID
                                self.logger.info(f"[QDRANT] 🎯 No valid extracted IDs, falling back to original document_id: {document_id}")
                                action['document_id'] = document_id
                    else:
                        # Fall back to using point IDs directly
                        self.logger.info(f"[QDRANT] 🎯 Using point IDs as fallback")
                        action['filter_fields'] = {'ids': [hit.id for hit in matches]}
                else:
                    self.logger.warning(f"[QDRANT] ❌ No matches found in any search method for document_id='{document_id}', document_number='{document_number}'")
                    
                    # Fall back to direct ID search in collection
                    if document_id:
                        self.logger.info(f"[QDRANT] 🔍 Attempting direct ID lookup with document_id as filter")
                        action['filter_fields'] = {'metadata.document_id': document_id}
                
            except Exception as e:
                self.logger.error(f"[QDRANT] ❌ Vector search for removal failed: {e}")
                self.logger.error(f"[QDRANT] ❌ Error traceback: {traceback.format_exc()}")
        
        # Process each collection
        for col in cols:
            f = self._build_filter(action)
            self.logger.info(f"[QDRANT] REMOVE: Collection={col}, Filter={f}")
            
            # Enhanced logging to show filter conditions clearly
            if action.get('filter_fields'):
                self.logger.info(f"[QDRANT] REMOVE filter fields: {json.dumps(action.get('filter_fields'), default=str)}")
            
            if f:
                try:
                    # Get the matching documents before deletion to verify what we're removing
                    try:
                        matching_points, _ = qdrant_manager.client.scroll(
                            collection_name=col,
                            scroll_filter=f,
                            limit=100,
                            with_payload=True
                        )
                        
                        if matching_points:
                            self.logger.info(f"[QDRANT] 🎯 Found {len(matching_points)} documents matching removal filter in {col}")
                            self.logger.info(f"[QDRANT] 📋 DOCUMENTS TO BE REMOVED:")
                            
                            for idx, point in enumerate(matching_points):
                                # Extract key information
                                doc_id = point.payload.get('metadata', {}).get('document_id', point.payload.get('document_id', 'unknown'))
                                title = point.payload.get('metadata', {}).get('document_title', point.payload.get('title', 'unknown'))
                                content_preview = point.payload.get('content', '')[:100] if point.payload.get('content') else ''
                                
                                self.logger.info(f"[QDRANT] REMOVING #{idx+1}: Point ID={point.id}")
                                self.logger.info(f"[QDRANT]   Document ID: {doc_id}")
                                self.logger.info(f"[QDRANT]   Title: {title}")
                                self.logger.info(f"[QDRANT]   Content: {content_preview}...")
                                
                            # Now count them for verification
                            count_response = qdrant_manager.client.count(collection_name=col, count_filter=f)
                            self.logger.info(f"[QDRANT] Count verification: {count_response.count} documents to be removed from {col}")
                            
                            # Perform the deletion
                            resp = qdrant_manager.client.delete(collection_name=col, points_selector=models.FilterSelector(filter=f))
                            self.logger.info(f"[QDRANT] 🗑️ REMOVE response: {resp}")
                            self.logger.info(f"[QDRANT] ✅ Successfully removed {len(matching_points)} documents from collection {col}")
                            executed.append(col)
                        else:
                            self.logger.warning(f"[QDRANT] ⚠️ No documents found matching filter in {col}")
                    except Exception as scroll_err:
                        self.logger.warning(f"[QDRANT] ⚠️ Could not retrieve documents before removal: {scroll_err}")
                        
                        # Fall back to just counting
                        try:
                            count_response = qdrant_manager.client.count(collection_name=col, count_filter=f)
                            self.logger.info(f"[QDRANT] Found {count_response.count} documents matching removal filter in {col}")
                            
                            # Perform the deletion
                            resp = qdrant_manager.client.delete(collection_name=col, points_selector=models.FilterSelector(filter=f))
                            self.logger.info(f"[QDRANT] REMOVE response: {resp}")
                            self.logger.info(f"🗑️ [QDRANT] Successfully removed documents from collection {col}")
                            executed.append(col)
                        except Exception as count_err:
                            self.logger.warning(f"[QDRANT] Could not count documents before removal: {count_err}")
                            
                            # Last resort: just try to delete
                            resp = qdrant_manager.client.delete(collection_name=col, points_selector=models.FilterSelector(filter=f))
                            self.logger.info(f"[QDRANT] REMOVE response: {resp}")
                            self.logger.info(f"🗑️ [QDRANT] Attempted document removal from collection {col}")
                            executed.append(col)
                except Exception as e:
                    self.logger.error(f"[QDRANT] REMOVE error in {col}: {e}")
                    self.logger.error(f"❌ [QDRANT] Failed to remove documents. Error details: {str(e)}")
                    self.logger.error(f"❌ [QDRANT] Error traceback: {traceback.format_exc()}")
            else:
                self.logger.warning(f"[QDRANT] No valid filter conditions for removal in collection {col}")
                self.logger.warning(f"[QDRANT] Available fields in action: {list(action.keys())}")
                # Log the key fields that would be used to build a filter
                self.logger.warning(f"[QDRANT] document_id: {action.get('document_id', 'not found')}")
                self.logger.warning(f"[QDRANT] target_document: {action.get('target_document', 'not found')}")
                
        # Create result
        result = self._success(action, f"Removed matching documents from collections: {executed}")
        
        # Validate the result using improved handler
        from improved_removal_handler import validate_document_removal
        
        # Check if no documents were found and handle appropriately
        if not executed:
            # No documents were removed
            document_id = action.get('document_id', '') or action.get('target_document', '')
            if document_id:
                if document_id == "MANUAL_REVIEW_REQUIRED":
                    result["success"] = False
                    result["details"] = "MANUAL_REVIEW_REQUIRED documents cannot be removed automatically"
                else:
                    # Check if this is a non-existent document
                    result["success"] = False
                    result["details"] = f"No matching documents found for ID: {document_id}"
        
        # Final validation
        result = validate_document_removal(action, result)
        self.logger.info(f"🔍 [QDRANT] REMOVE operation completed with result: {json.dumps(result, default=str)}")
        return result

    def _add(self, action: dict, notification_data: dict, chunk_metadata: dict = None) -> dict:
        cols = action['collection_name'] if isinstance(action['collection_name'], list) else [action['collection_name']]
        original_pid = action.get('target_document')
        
        # Enhanced UUID generation with chunk index and timestamp for uniqueness
        chunk_index = None
        timestamp = None
        
        if chunk_metadata:
            chunk_index = chunk_metadata.get('chunk_index')
        
        # Also check if chunk_index is in the action payload
        if chunk_index is None and action.get('payload', {}).get('chunk_index') is not None:
            chunk_index = action.get('payload', {}).get('chunk_index')
        
        # Get timestamp from action or generate new one
        timestamp = action.get('timestamp') or datetime.now().isoformat()
        
        # Generate unique UUID with chunk index and timestamp
        pid = self._generate_point_id(original_pid, chunk_index, timestamp) if original_pid else str(uuid.uuid4())
        
        payload = action.get('payload', {})
        self.logger.info(f"[QDRANT] ADD: Collections={cols}, PID={pid}, Document={original_pid}, Chunk={chunk_index}")
        
        # Ensure metadata field exists and store the queryable document ID
        if 'metadata' not in payload:
            payload['metadata'] = {}
        if original_pid:
            payload['metadata']['document_id'] = original_pid
        
        #  Unnest metadata but keep a copy for vector generation
        metadata_copy = payload.get('metadata', {}).copy()  # Keep copy for qdrant_utils
        
        # Unnest all metadata fields to top level for direct querying
        for key, value in payload.get('metadata', {}).items():
            payload[key] = value
        
        # Delete the nested metadata after unnesting
        if 'metadata' in payload:
            del payload['metadata']
        
        # ✅ CRITICAL: Add back a minimal metadata for qdrant_utils vector generation
        payload['metadata'] = {
            'title': metadata_copy.get('title', ''),
            'document_id': metadata_copy.get('document_id', original_pid),
            'content': payload.get('content', ''),  # Ensure content is available
            'page_content': payload.get('page_content', '')  # Ensure page_content is available
        }

        # Store the original document ID in the payload for reference
        if original_pid:
            payload['original_document_id'] = original_pid
        
        # Add chunk metadata to payload if provided
        if chunk_metadata:
            payload['chunk_metadata'] = {
                'content_preview': str(chunk_metadata.get('content', ''))[:500],
                'chunk_index': chunk_metadata.get('chunk_index'),
                'positions': chunk_metadata.get('positions', [])[:3] if chunk_metadata.get('positions') else []
            }
        
        # Add notification metadata
        if notification_data:
            payload['notification_metadata'] = {
                'title': notification_data.get('Title'),
                'year': notification_data.get('Year', notification_data.get('corrected_year')),
                'pdf_link': notification_data.get('PDF Link'),
                'notification_codes': notification_data.get('notification_codes', {}),
                'target_document_id': action.get('target_document_id')
            }
        
        #  Try to use enhanced qdrant_manager first
        try:
            # Collect text sources for vector generation
            text_sources = []
            if 'content' in payload:
                text_sources.append(str(payload['content']))
            if 'page_content' in payload:
                text_sources.append(str(payload['page_content']))
            if chunk_metadata and 'content' in chunk_metadata:
                text_sources.append(str(chunk_metadata['content']))
            if chunk_metadata and 'text' in chunk_metadata:
                text_sources.append(str(chunk_metadata['text']))
            if action.get('details'):
                text_sources.append(str(action['details']))
            if action.get('target_document'):
                text_sources.append(str(action['target_document']))
            
            combined_text = ' '.join(text_sources)[:8000] if text_sources else ""
            
            if combined_text:
                self.logger.info(f"✅ Using qdrant_manager.upsert_point for {original_pid} with text: {len(combined_text)} chars")
                # self.logger.info(f"🔍 Available payload fields: {list(payload.keys())}")
                # self.logger.info(f"🔍 Metadata content: {payload.get('metadata', {})}")
                
                # Use the enhanced qdrant manager
                success = qdrant_manager.upsert_point(
                    collection_name=cols[0],  # Use first collection
                    point_id=pid,
                    payload=payload,
                    text_for_embedding=combined_text  # ✅ This will generate vectors properly
                )
                
                if success:
                    self.logger.info(f"✅ Successfully used qdrant_manager.upsert_point for {original_pid} (ID: {pid})")
                    return self._success(action, f"Added document {original_pid} (ID: {pid}) to collections: {cols}", 
                                    {'document_id': original_pid, 'uuid': pid})
                else:
                    self.logger.warning(f"⚠️ qdrant_manager.upsert_point failed, falling back to manual method")
            else:
                self.logger.warning(f"⚠️ No text content available, falling back to manual method")
                
        except Exception as e:
            self.logger.error(f"❌ Error using qdrant_manager.upsert_point: {e}")
            self.logger.warning(f"🔄 Falling back to manual vector generation")
        
        # ✅ FALLBACK: Manual vector generation (keep existing logic)
        # MODIFIED: Get both dense and sparse vectors from action or generate them
        dense_vector = action.get('vector')
        sparse_vector = action.get('sparse_vector')
        
        # Generate dense vector if not provided
        if dense_vector is None and combined_text:
            try:
                from openai_utils import en_embeddings
                dense_vector = en_embeddings.embed_query(combined_text)
                
                if dense_vector and len(dense_vector) > 0:
                    self.logger.info(f"Generated dense vector for document {original_pid} (ID: {pid}), dim: {len(dense_vector)}")
                else:
                    self.logger.warning(f"Dense embedding returned empty vector for {original_pid}, using dummy vector")
                    dense_vector = [0.1] * 3072
                    
            except Exception as e:
                self.logger.warning(f"Failed to generate dense vector for {original_pid}: {e}")
                dense_vector = [0.1] * 3072
        elif dense_vector is None:
            self.logger.warning(f"No text content available for dense vector generation for {original_pid}")
            dense_vector = [0.1] * 3072

        # NEW: Generate sparse vector if not provided
        if sparse_vector is None and combined_text:
            sparse_vector = self._generate_sparse_vector(combined_text)
            if sparse_vector:
                self.logger.info(f"Generated sparse vector for document {original_pid} (ID: {pid}), non-zero elements: {len(sparse_vector)}")
            else:
                self.logger.warning(f"Failed to generate sparse vector for {original_pid}")
        elif sparse_vector is None:
            self.logger.warning(f"No text content available for sparse vector generation for {original_pid}")
            sparse_vector = {}

        # ✅ FALLBACK: If qdrant_manager method succeeded, we already returned above
        # This code only runs if we're using the old method or qdrant_manager failed
        executed_collections = []
        errors = []
        
        for col in cols:
            try:
                self.logger.info(f"[QDRANT] ADD: Attempting to add document {original_pid} (UUID: {pid}) to collection: {col}")
                
                # Check collection's vector configuration
                collection_info = qdrant_manager.client.get_collection(collection_name=col)
                
                # MODIFIED: Prepare vectors based on collection configuration
                point_vector = None
                vectors_config = collection_info.config.params.vectors
                
                if isinstance(vectors_config, dict):
                    # Named vectors configuration - build vector dict
                    point_vector = {}
                    
                    # Add dense vector (unnamed vector "")
                    if "" in vectors_config:
                        point_vector[""] = dense_vector
                        self.logger.info(f"Added dense vector to point (dim: {len(dense_vector)})")
                    
                    # NEW: Add sparse vector (named "splade" or "fast-sparse-bm25")
                    sparse_key = None
                    if "splade" in vectors_config:
                        sparse_key = "splade"
                    elif "fast-sparse-bm25" in vectors_config:
                        sparse_key = "fast-sparse-bm25"
                    
                    if sparse_key and sparse_vector:
                        point_vector[sparse_key] = sparse_vector
                        self.logger.info(f"Added sparse vector to point as '{sparse_key}' (non-zero: {len(sparse_vector)})")
                    
                    if not point_vector:
                        # Fallback to dense only if no named vectors matched
                        point_vector = dense_vector
                        self.logger.warning(f"No matching named vectors found, using dense vector only")
                else:
                    # Single vector configuration - use dense only
                    point_vector = dense_vector
                    self.logger.info(f"Using single dense vector (dim: {len(dense_vector)})")

                point = PointStruct(
                    id=pid, 
                    payload=payload, 
                    vector=point_vector
                )
                
                resp = qdrant_manager.client.upsert(collection_name=col, points=[point], wait=True)
                self.logger.info(f"[QDRANT] ADD response: {resp}")
                executed_collections.append(col)
                self.logger.info(f"✅ Successfully added document {original_pid} to collection: {col}")
            except Exception as e:
                error_msg = f"Failed to add to {col}: {str(e)}"
                errors.append(error_msg)
                self.logger.error(f"[QDRANT] ADD error in {col}: {e}")
        
        if executed_collections:
            # Include ID prominently in the success message for better tracking
            success_msg = f"Added document {original_pid} (ID: {pid}) to collections: {executed_collections}"
            return self._success(action, success_msg, {'document_id': original_pid, 'uuid': pid})
        else:
            return self._error(action, f"Failed to add document to any collection. Errors: {'; '.join(errors)}")

    def _update(self, action: dict, notification_data: dict, chunk_metadata: dict = None) -> dict:
        cols = action['collection_name'] if isinstance(action['collection_name'], list) else [action['collection_name']]
        new_payload_from_action = action.get('payload', {})
        
        executed = []
        errors = []
        
        # Check if we should use vector search to find documents to update
        use_vector_search = action.get('use_vector_search', True)
        document_id = action.get('document_id', '') or action.get('target_document', '')
        document_number = action.get('document_number', '')
        
        # If we have document identifiers but no filter, try to use vector search
        if use_vector_search and (document_id or document_number) and not action.get('filter_fields') and self.has_metadata_search:
            self.logger.info(f"[QDRANT] Trying to find documents to update using vector search")
            try:
                # Use our vector search to find matching documents
                if document_id and document_number:
                    matches = self.metadata_search.search_named_vector("document_id", document_id, top_k=5)
                    # Add matches from document number if needed
                    if len(matches) < 3:
                        num_matches = self.metadata_search.search_named_vector("document_number", document_number, top_k=5)
                        # Combine results, removing duplicates
                        existing_ids = {hit.id for hit in matches}
                        matches.extend([hit for hit in num_matches if hit.id not in existing_ids])
                elif document_id:
                    matches = self.metadata_search.search_named_vector("document_id", document_id, top_k=5)
                elif document_number:
                    matches = self.metadata_search.search_named_vector("document_number", document_number, top_k=5)
                
                # If we found matches, use them to build a filter
                if matches:
                    self.logger.info(f"[QDRANT] Found {len(matches)} potential documents to update via vector search")
                    # Use the IDs directly in the filter
                    action['filter_fields'] = {'_id': [hit.id for hit in matches]}
                    # Also log what we found
                    for hit in matches:
                        self.logger.info(f"[QDRANT] Match: ID={hit.id}, Score={hit.score:.4f}, Document={hit.payload.get('document_id', 'unknown')}")
            except Exception as e:
                self.logger.warning(f"[QDRANT] Vector search for update failed: {e}")
        
        for col in cols:
            try:
                f = self._build_filter(action)
                self.logger.info(f"[QDRANT] UPDATE: Collection={col}, Filter={f}, Payload={new_payload_from_action}")
                if f is not None:
                    matching_points, _ = qdrant_manager.client.scroll(
                        collection_name=col,
                        scroll_filter=f,
                        limit=100,
                        with_payload=True,
                        with_vectors=True
                    )
                    self.logger.info(f"[QDRANT] UPDATE: Found {len(matching_points)} matching docs in {col}")
                    if matching_points:
                        updated_points = []
                        for point in matching_points:
                            # Merge payloads: start with old, update with new
                            merged_payload = point.payload.copy()
                            merged_payload.update(new_payload_from_action)
                            
                            # Re-apply chunk metadata if provided
                            if chunk_metadata:
                                merged_payload['chunk_metadata'] = {
                                    'content_preview': str(chunk_metadata.get('content', ''))[:500],
                                    'chunk_index': chunk_metadata.get('chunk_index'),
                                    'positions': chunk_metadata.get('positions', [])[:3] if chunk_metadata.get('positions') else []
                                }
                            
                            # Re-apply notification metadata
                            if notification_data:
                                merged_payload['notification_metadata'] = {
                                    'title': notification_data.get('Title'),
                                    'year': notification_data.get('Year', notification_data.get('corrected_year')),
                                    'pdf_link': notification_data.get('PDF Link'),
                                    'notification_codes': notification_data.get('notification_codes', {})
                                }

                            updated_points.append(PointStruct(
                                id=point.id,
                                payload=merged_payload,
                                vector=point.vector # Preserve BOTH dense and sparse vectors
                            ))
                        
                        # Upsert the updated points, which overwrites them completely
                        resp = qdrant_manager.client.upsert(
                            collection_name=col,
                            points=updated_points,
                            wait=True
                        )
                        self.logger.info(f"[QDRANT] UPDATE response: {resp}")
                        executed.append(col)
                        self.logger.info(f"✅ Successfully updated {len(matching_points)} documents in collection: {col}")
                    else:
                        self.logger.warning(f"[QDRANT] UPDATE: No documents found matching filter in collection: {col}")
                        errors.append(f"No matching documents in {col}")
                else:
                    self.logger.warning(f"[QDRANT] UPDATE: No filter conditions found for update in collection {col}")
                    errors.append(f"No filter conditions for {col}")
            except Exception as e:
                error_msg = f"Failed to update in {col}: {str(e)}"
                errors.append(error_msg)
                self.logger.error(f"[QDRANT] UPDATE error in {col}: {e}")
        
        if executed:
            return self._success(action, f"Updated documents in collections: {executed}")
        else:
            return self._error(action, f"Failed to update documents in any collection. Errors: {'; '.join(errors)}")

    def _note(self, action: dict, notification_data: dict, chunk_metadata: dict = None) -> dict:
        cols = action.get('collection_name', ['temp_notes'])
        original_nid = f"note_{int(datetime.utcnow().timestamp())}"
        nid = self._generate_point_id(original_nid)
        f = self._build_filter(action)
        data = {
            'original_note_id': original_nid,
            'in_reply_to': {p: action.get(k) for k, p in self.METADATA_MAP.items() if k in action},
            'content': action.get('note_content'),
            'created_at': datetime.utcnow().isoformat()
        }
        for col in cols:
            try:
                self.logger.info(f"[QDRANT] NOTE: Adding note to {col}, Data: {data}")
                point = PointStruct(id=nid, payload=data)
                resp = qdrant_manager.client.upsert(collection_name=col, points=[point])
                self.logger.info(f"[QDRANT] NOTE response: {resp}")
            except Exception as e:
                self.logger.error(f"[QDRANT] NOTE error in {col}: {e}")
        return self._success(action, f"Note {original_nid} (ID: {nid}) added to collections: {cols}")

    def _error(self, action: dict, msg: str) -> dict:
        return {'action': action.get('action_type'), 'success': False, 'details': msg,
                'timestamp': datetime.utcnow().isoformat()}

    def _success(self, action: dict, details: str, extra: dict = None) -> dict:
        # Create base response with action info
        res = {
            'action': action.get('action_type'), 
            'success': True,
            'details': details, 
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # For ADD_DOCUMENT actions, always include document_id and uuid if available
        if action.get('action_type') == 'ADD_DOCUMENT':
            if not extra:
                extra = {}
                
            if 'document_id' not in extra and action.get('target_document'):
                extra['document_id'] = action.get('target_document')
                
            if 'collection' not in extra and action.get('collection_name'):
                coll_name = action.get('collection_name')
                if isinstance(coll_name, list) and coll_name:
                    extra['collection'] = coll_name[0]
                else:
                    extra['collection'] = coll_name
        
        # Add any additional data
        if extra:
            res.update(extra)
            
        return res
