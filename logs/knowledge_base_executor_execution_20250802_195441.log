2025-08-02 19:54:41,558 - knowledge_base_executor - <PERSON><PERSON><PERSON> - <PERSON><PERSON> configured to write logs to: logs/knowledge_base_executor_execution_20250802_195441.log
2025-08-02 19:54:41,558 - knowledge_base_executor - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-02 19:54:45,295 - knowledge_base_executor - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-02 19:55:06,332 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:55:06,332 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:06,332 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:06,333 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:55:06,333 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_circular'], PID=85bdd850-23cb-581c-a557-f7f038cf5ba2, Document=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25, Chunk=None
2025-08-02 19:55:06,333 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 with text: 8000 chars
2025-08-02 19:55:09,225 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 (ID: 85bdd850-23cb-581c-a557-f7f038cf5ba2)
2025-08-02 19:55:09,225 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 (ID: 85bdd850-23cb-581c-a557-f7f038cf5ba2) to collections: ['rbi_master_circular']", "timestamp": "2025-08-02T14:25:09.225212", "document_id": "RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25", "uuid": "85bdd850-23cb-581c-a557-f7f038cf5ba2", "collection": "rbi_master_circular"}
2025-08-02 19:55:09,225 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:09,225 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:09,226 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:22,891 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:55:22,891 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:22,891 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:22,892 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:55:22,892 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:55:23,034 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:55:23,034 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9741a628-999e-4b3f-aa8d-da299fe6f6c1, Score=0.8026, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-02 19:55:23,034 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3d495fa6-6797-461c-bf81-7de0eb59a24a, Score=0.8026, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-02 19:55:23,034 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2105f02f-10ac-520c-82c1-d91e04509d6d, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:55:23,035 - knowledge_base_executor - INFO - [QDRANT] Match: ID=d2b23822-c5a2-5527-8507-5b3d29d5794c, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:55:23,035 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d04a677-f401-5734-88ac-14846a6cf5b3, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:55:23,035 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:23,035 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3'])] must_not=None, Payload={}
2025-08-02 19:55:23,069 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 3 matching docs in rbi_master_direction
2025-08-02 19:55:23,090 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3172 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-02 19:55:23,090 - knowledge_base_executor - INFO - ✅ Successfully updated 3 documents in collection: rbi_master_direction
2025-08-02 19:55:23,090 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:23,090 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:23,090 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:55:23,090 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:23,091 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:55:23,091 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:55:23,091 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:55:23,091 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:55:23,091 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:23,091 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3'])] must_not=None, Payload={}
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-02 19:55:23,098 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3'])] must_not=None, Payload={}
2025-08-02 19:55:23,112 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 2 matching docs in rbi_circular
2025-08-02 19:55:23,128 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=12984 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-02 19:55:23,128 - knowledge_base_executor - INFO - ✅ Successfully updated 2 documents in collection: rbi_circular
2025-08-02 19:55:23,128 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction', 'rbi_circular']", "timestamp": "2025-08-02T14:25:23.128494"}
2025-08-02 19:55:23,128 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:23,128 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:23,130 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:23,131 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:55:23,131 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:23,131 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:23,131 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:55:23,131 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=37f15250-7c7f-4831-ad6b-b1a973f43640, Document=None, Chunk=0
2025-08-02 19:55:23,131 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 2513 chars
2025-08-02 19:55:26,234 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 37f15250-7c7f-4831-ad6b-b1a973f43640)
2025-08-02 19:55:26,234 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 37f15250-7c7f-4831-ad6b-b1a973f43640) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:25:26.234665", "document_id": null, "uuid": "37f15250-7c7f-4831-ad6b-b1a973f43640", "collection": "rbi_circular"}
2025-08-02 19:55:26,234 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:26,234 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:26,235 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:39,304 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:55:39,304 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:39,304 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:39,304 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:55:39,304 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] Match: ID=521d0371-918a-4608-a972-d5d632952cc8, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] Match: ID=7940f618-ba47-45d9-aa0d-98ebba0fb222, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ccd83ffc-2347-403f-92cc-2aeb36cd3027, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] Match: ID=c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs' → 'MasterCircular-Guarantees,Co-acceptances&LettersofCredit-UCBs'
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: MasterCircular-Guarantees,Co-acceptances&LettersofCredit-UCBs
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1'])] must_not=None, Payload={}
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-02 19:55:39,421 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1'])] must_not=None, Payload={}
2025-08-02 19:55:39,445 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_circular
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8280 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_circular
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1'])] must_not=None, Payload={}
2025-08-02 19:55:39,487 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-02 19:55:39,487 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-02 19:55:39,487 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular']", "timestamp": "2025-08-02T14:25:39.487449"}
2025-08-02 19:55:39,487 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:39,487 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:39,488 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:39,488 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:55:39,488 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:39,488 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:39,488 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:55:39,488 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=1b52add7-0399-472c-ba6e-98a51adb1391, Document=None, Chunk=1
2025-08-02 19:55:39,488 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-02 19:55:41,541 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 1b52add7-0399-472c-ba6e-98a51adb1391)
2025-08-02 19:55:41,541 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 1b52add7-0399-472c-ba6e-98a51adb1391) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:25:41.541755", "document_id": null, "uuid": "1b52add7-0399-472c-ba6e-98a51adb1391", "collection": "rbi_circular"}
2025-08-02 19:55:41,541 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:41,541 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:41,542 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:57,823 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:55:57,824 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:57,824 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:57,824 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:55:57,824 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5dd89363-59cc-4134-ada4-eeda877a51b5, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] Match: ID=729eb4c6-a6c3-4aed-9a25-aa597db91127, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9affc01a-813a-4ab4-afdc-e864c70dd44f, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ac5ffabf-b12b-4fc4-93be-9684c9047105, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] Match: ID=96f706da-38a1-4bc3-bd09-648ab7d128db, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.Plan.(PCB)49/09.27.00/96-97
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:55:57,956 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:55:57,956 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:55:57,956 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:55:57,956 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:57,956 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db'])] must_not=None, Payload={}
2025-08-02 19:55:57,961 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-02 19:55:57,961 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-02 19:55:57,961 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:57,961 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:57,961 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:55:57,961 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:57,961 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:55:57,961 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:55:57,961 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:55:57,962 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:55:57,962 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:57,962 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db'])] must_not=None, Payload={}
2025-08-02 19:55:57,994 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_circular
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8281 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_circular
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,047 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db'])] must_not=None, Payload={}
2025-08-02 19:55:58,049 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-02 19:55:58,049 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-02 19:55:58,050 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular']", "timestamp": "2025-08-02T14:25:58.049844"}
2025-08-02 19:55:58,050 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:58,050 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:58,051 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:58,051 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:55:58,051 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:58,051 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:58,051 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:55:58,051 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] Match: ID=166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a, Score=0.6521, Document=FIDD.CO.Plan.BC.23/04.09.01/2015-16
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] Match: ID=c0891098-62c7-4c42-8d31-7c970dda399f, Score=0.6521, Document=FIDD.CO.Plan.BC.23/04.09.01/2015-16
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] Match: ID=040e1215-100c-44b2-9c5a-325db2ecaf87, Score=0.6521, Document=FIDD.CO.Plan.BC.23/04.09.01/2015-16
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ac5ffabf-b12b-4fc4-93be-9684c9047105, Score=0.6509, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] Match: ID=729eb4c6-a6c3-4aed-9a25-aa597db91127, Score=0.6509, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.Plan.Cir.SUB.1/09.27.00/94-95
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '729eb4c6-a6c3-4aed-9a25-aa597db91127']}
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c0891098-62c7-4c42-8d31-7c970dda399f
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 040e1215-100c-44b2-9c5a-325db2ecaf87
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87']...
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '729eb4c6-a6c3-4aed-9a25-aa597db91127'])] must_not=None, Payload={}
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-02 19:55:58,102 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '729eb4c6-a6c3-4aed-9a25-aa597db91127']}
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c0891098-62c7-4c42-8d31-7c970dda399f
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 040e1215-100c-44b2-9c5a-325db2ecaf87
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87']...
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '729eb4c6-a6c3-4aed-9a25-aa597db91127'])] must_not=None, Payload={}
2025-08-02 19:55:58,111 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 2 matching docs in rbi_master_circular
2025-08-02 19:55:58,130 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8282 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-02 19:55:58,130 - knowledge_base_executor - INFO - ✅ Successfully updated 2 documents in collection: rbi_master_circular
2025-08-02 19:55:58,130 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,130 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:58,131 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '729eb4c6-a6c3-4aed-9a25-aa597db91127']}
2025-08-02 19:55:58,131 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,131 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a
2025-08-02 19:55:58,131 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c0891098-62c7-4c42-8d31-7c970dda399f
2025-08-02 19:55:58,131 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 040e1215-100c-44b2-9c5a-325db2ecaf87
2025-08-02 19:55:58,131 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87']...
2025-08-02 19:55:58,131 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,131 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '729eb4c6-a6c3-4aed-9a25-aa597db91127'])] must_not=None, Payload={}
2025-08-02 19:55:58,142 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 3 matching docs in rbi_circular
2025-08-02 19:55:58,156 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=12987 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-02 19:55:58,156 - knowledge_base_executor - INFO - ✅ Successfully updated 3 documents in collection: rbi_circular
2025-08-02 19:55:58,156 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-02T14:25:58.156655"}
2025-08-02 19:55:58,156 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:58,156 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:58,157 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:58,157 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:55:58,157 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:58,157 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:58,157 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:55:58,157 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:55:58,198 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:55:58,198 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9a0db77f-d109-46ff-9dd5-42e6866482c2, Score=0.5587, Document=DBOD.PSBD.BC.88/16.13.100/2005-06
2025-08-02 19:55:58,198 - knowledge_base_executor - INFO - [QDRANT] Match: ID=529482b7-61a4-4dbc-85f6-082d3171e0ea, Score=0.5521, Document=Ref.No.UBD.DS. 2 /13.02.00/2002-03
2025-08-02 19:55:58,198 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b7aff217-917a-46f2-93ba-ecbc1a1eb565, Score=0.5448, Document=BP.BC.11/21.01.040/99-00
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] Match: ID=998c8368-4404-41db-a8ac-d02d9c525c94, Score=0.5448, Document=BP.BC.11/21.01.040/99-00
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b4358508-03e0-4c1e-b893-bcede5fbbb79, Score=0.5448, Document=BP.BC.11/21.01.040/99-00
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.POT.1/UB.58-92/3
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79']}
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9a0db77f-d109-46ff-9dd5-42e6866482c2
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 529482b7-61a4-4dbc-85f6-082d3171e0ea
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b7aff217-917a-46f2-93ba-ecbc1a1eb565
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565']...
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79'])] must_not=None, Payload={}
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-02 19:55:58,201 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79']}
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9a0db77f-d109-46ff-9dd5-42e6866482c2
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 529482b7-61a4-4dbc-85f6-082d3171e0ea
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b7aff217-917a-46f2-93ba-ecbc1a1eb565
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565']...
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79'])] must_not=None, Payload={}
2025-08-02 19:55:58,203 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-02 19:55:58,203 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-02 19:55:58,203 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,203 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:58,204 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79']}
2025-08-02 19:55:58,204 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,204 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9a0db77f-d109-46ff-9dd5-42e6866482c2
2025-08-02 19:55:58,204 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 529482b7-61a4-4dbc-85f6-082d3171e0ea
2025-08-02 19:55:58,204 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b7aff217-917a-46f2-93ba-ecbc1a1eb565
2025-08-02 19:55:58,204 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565']...
2025-08-02 19:55:58,204 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,204 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79'])] must_not=None, Payload={}
2025-08-02 19:55:58,229 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-02 19:55:58,271 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=12988 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-02 19:55:58,271 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-02 19:55:58,272 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-02T14:25:58.271952"}
2025-08-02 19:55:58,272 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:58,272 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:58,272 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:58,273 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:55:58,273 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:58,273 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:58,273 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:55:58,273 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3d5b542d-4758-4b8b-9adf-56b5ae78f253, Score=0.6118, Document=RPCD.No.RRB.BC.33/03.05.33(E)/2005-06
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ac35390c-19c4-4447-b048-bb917acfa27f, Score=0.6118, Document=RPCD.No.RRB.BC.33/03.05.33(E)/2005-06
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] Match: ID=bfa6e812-0474-4eba-b9d1-25382c83aa14, Score=0.6081, Document=RPCD.No.RRB.BC.76/03.05.33(C)/2005-06
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] Match: ID=c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae, Score=0.6081, Document=RPCD.No.RRB.BC.76/03.05.33(C)/2005-06
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] Match: ID=05e1db56-5a24-45dd-8de5-570e08a4eea9, Score=0.6046, Document=RPCD.No.RRB.BC.76/03.05.34/96-97
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.I&L/PCB/9/12.05.00/95-96
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9']}
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d5b542d-4758-4b8b-9adf-56b5ae78f253
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ac35390c-19c4-4447-b048-bb917acfa27f
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = bfa6e812-0474-4eba-b9d1-25382c83aa14
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14']...
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9'])] must_not=None, Payload={}
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-02 19:55:58,290 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9']}
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d5b542d-4758-4b8b-9adf-56b5ae78f253
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ac35390c-19c4-4447-b048-bb917acfa27f
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = bfa6e812-0474-4eba-b9d1-25382c83aa14
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14']...
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9'])] must_not=None, Payload={}
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-02 19:55:58,292 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9']}
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d5b542d-4758-4b8b-9adf-56b5ae78f253
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ac35390c-19c4-4447-b048-bb917acfa27f
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = bfa6e812-0474-4eba-b9d1-25382c83aa14
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14']...
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9'])] must_not=None, Payload={}
2025-08-02 19:55:58,311 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-02 19:55:58,335 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=12989 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-02 19:55:58,335 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-02 19:55:58,336 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-02T14:25:58.336009"}
2025-08-02 19:55:58,336 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:58,336 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:58,336 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:58,337 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:55:58,337 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:58,337 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:58,337 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:55:58,337 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=2d7f10b4-ea32-4160-9397-227437d55cdf, Document=None, Chunk=2
2025-08-02 19:55:58,337 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-02 19:56:00,846 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 2d7f10b4-ea32-4160-9397-227437d55cdf)
2025-08-02 19:56:00,846 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 2d7f10b4-ea32-4160-9397-227437d55cdf) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:26:00.846130", "document_id": null, "uuid": "2d7f10b4-ea32-4160-9397-227437d55cdf", "collection": "rbi_circular"}
2025-08-02 19:56:00,846 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:56:00,846 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:56:00,846 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
