2025-08-02 19:43:25,279 - knowledge_base_executor - <PERSON><PERSON><PERSON> - <PERSON><PERSON> configured to write logs to: logs/knowledge_base_executor_execution_20250802_194325.log
2025-08-02 19:43:25,279 - knowledge_base_executor - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-02 19:43:29,286 - knowledge_base_executor - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-02 19:43:47,339 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:43:47,340 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:43:47,340 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:43:47,340 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:43:47,341 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_circular'], PID=486c4de4-6d35-5b75-bd33-af8ce7ebc57b, Document=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25, Chunk=None
2025-08-02 19:43:47,341 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 with text: 8000 chars
2025-08-02 19:43:49,373 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 (ID: 486c4de4-6d35-5b75-bd33-af8ce7ebc57b)
2025-08-02 19:43:49,373 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 (ID: 486c4de4-6d35-5b75-bd33-af8ce7ebc57b) to collections: ['rbi_master_circular']", "timestamp": "2025-08-02T14:13:49.373305", "document_id": "RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25", "uuid": "486c4de4-6d35-5b75-bd33-af8ce7ebc57b", "collection": "rbi_master_circular"}
2025-08-02 19:43:49,373 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:43:49,373 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:43:49,375 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:44:01,563 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:44:01,563 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:01,563 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:01,563 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:44:01,563 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:44:01,687 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:44:01,687 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9741a628-999e-4b3f-aa8d-da299fe6f6c1, Score=0.8026, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-02 19:44:01,687 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3d495fa6-6797-461c-bf81-7de0eb59a24a, Score=0.8026, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-02 19:44:01,687 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2105f02f-10ac-520c-82c1-d91e04509d6d, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:44:01,687 - knowledge_base_executor - INFO - [QDRANT] Match: ID=d2b23822-c5a2-5527-8507-5b3d29d5794c, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:44:01,687 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d04a677-f401-5734-88ac-14846a6cf5b3, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:44:01,687 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:01,687 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:44:01,688 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:44:01,688 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:44:01,688 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:01,688 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:44:01,688 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:44:01,688 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:44:01,688 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:44:01,688 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:44:01,689 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:44:01,689 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:14:01.689704"}
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:44:01,689 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:44:01,690 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:44:01,690 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:44:01,691 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:01,691 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:01,691 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:44:01,691 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=867aea7e-14f9-4b0a-a0bb-9307ab4c7f0a, Document=None, Chunk=0
2025-08-02 19:44:01,691 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 2513 chars
2025-08-02 19:44:04,687 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 867aea7e-14f9-4b0a-a0bb-9307ab4c7f0a)
2025-08-02 19:44:04,687 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 867aea7e-14f9-4b0a-a0bb-9307ab4c7f0a) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:14:04.687648", "document_id": null, "uuid": "867aea7e-14f9-4b0a-a0bb-9307ab4c7f0a", "collection": "rbi_circular"}
2025-08-02 19:44:04,687 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:44:04,687 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:44:04,688 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:44:14,415 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:44:14,415 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:14,415 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:14,415 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:44:14,415 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:44:14,526 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:44:14,526 - knowledge_base_executor - INFO - [QDRANT] Match: ID=521d0371-918a-4608-a972-d5d632952cc8, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:44:14,526 - knowledge_base_executor - INFO - [QDRANT] Match: ID=7940f618-ba47-45d9-aa0d-98ebba0fb222, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:44:14,526 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ccd83ffc-2347-403f-92cc-2aeb36cd3027, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:44:14,526 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] Match: ID=c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs' → 'MasterCircular-Guarantees,Co-acceptances&LettersofCredit-UCBs'
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: MasterCircular-Guarantees,Co-acceptances&LettersofCredit-UCBs
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:44:14,527 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:44:14,527 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:44:14,527 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:44:14,528 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:44:14,528 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:44:14,528 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:14,528 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:14:14.528100"}
2025-08-02 19:44:14,528 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:44:14,528 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:44:14,528 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:44:14,529 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:44:14,529 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:14,529 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:14,529 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:44:14,529 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=e0b7a635-8b6a-41db-985b-bda1bbce09f7, Document=None, Chunk=1
2025-08-02 19:44:14,529 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-02 19:44:17,787 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: e0b7a635-8b6a-41db-985b-bda1bbce09f7)
2025-08-02 19:44:17,787 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: e0b7a635-8b6a-41db-985b-bda1bbce09f7) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:14:17.787417", "document_id": null, "uuid": "e0b7a635-8b6a-41db-985b-bda1bbce09f7", "collection": "rbi_circular"}
2025-08-02 19:44:17,787 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:44:17,787 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:44:17,788 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:44:33,458 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:44:33,459 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:33,459 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:33,459 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:44:33,460 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:44:33,681 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:44:33,681 - knowledge_base_executor - INFO - [QDRANT] Match: ID=bab617b6-f064-4159-bd1f-75044e7fb7ff, Score=0.7074, Document=RPCD. No. Plan. BC.  92 / 04.09.01/ 2004-05
2025-08-02 19:44:33,681 - knowledge_base_executor - INFO - [QDRANT] Match: ID=7db38ac8-bc6d-4f0d-a6c0-b601a2360bf7, Score=0.7074, Document=RPCD. No. Plan. BC.  92 / 04.09.01/ 2004-05
2025-08-02 19:44:33,681 - knowledge_base_executor - INFO - [QDRANT] Match: ID=33e6ee9d-96f6-4ea9-869e-dc1fdab21815, Score=0.7074, Document=RPCD. No. Plan. BC.  92 / 04.09.01/ 2004-05
2025-08-02 19:44:33,681 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ac5ffabf-b12b-4fc4-93be-9684c9047105, Score=0.6994, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:44:33,681 - knowledge_base_executor - INFO - [QDRANT] Match: ID=729eb4c6-a6c3-4aed-9a25-aa597db91127, Score=0.6994, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:44:33,681 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:33,681 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:44:33,681 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.Plan.PCB.CIR.07/09.27.00/99-2000
2025-08-02 19:44:33,681 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['bab617b6-f064-4159-bd1f-75044e7fb7ff', '7db38ac8-bc6d-4f0d-a6c0-b601a2360bf7', '33e6ee9d-96f6-4ea9-869e-dc1fdab21815', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '729eb4c6-a6c3-4aed-9a25-aa597db91127']}
2025-08-02 19:44:33,681 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:33,681 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = bab617b6-f064-4159-bd1f-75044e7fb7ff
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7db38ac8-bc6d-4f0d-a6c0-b601a2360bf7
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 33e6ee9d-96f6-4ea9-869e-dc1fdab21815
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['bab617b6-f064-4159-bd1f-75044e7fb7ff', '7db38ac8-bc6d-4f0d-a6c0-b601a2360bf7', '33e6ee9d-96f6-4ea9-869e-dc1fdab21815']...
2025-08-02 19:44:33,682 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['bab617b6-f064-4159-bd1f-75044e7fb7ff', '7db38ac8-bc6d-4f0d-a6c0-b601a2360bf7', '33e6ee9d-96f6-4ea9-869e-dc1fdab21815', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '729eb4c6-a6c3-4aed-9a25-aa597db91127']}
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = bab617b6-f064-4159-bd1f-75044e7fb7ff
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7db38ac8-bc6d-4f0d-a6c0-b601a2360bf7
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 33e6ee9d-96f6-4ea9-869e-dc1fdab21815
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['bab617b6-f064-4159-bd1f-75044e7fb7ff', '7db38ac8-bc6d-4f0d-a6c0-b601a2360bf7', '33e6ee9d-96f6-4ea9-869e-dc1fdab21815']...
2025-08-02 19:44:33,682 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['bab617b6-f064-4159-bd1f-75044e7fb7ff', '7db38ac8-bc6d-4f0d-a6c0-b601a2360bf7', '33e6ee9d-96f6-4ea9-869e-dc1fdab21815', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '729eb4c6-a6c3-4aed-9a25-aa597db91127']}
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = bab617b6-f064-4159-bd1f-75044e7fb7ff
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7db38ac8-bc6d-4f0d-a6c0-b601a2360bf7
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 33e6ee9d-96f6-4ea9-869e-dc1fdab21815
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['bab617b6-f064-4159-bd1f-75044e7fb7ff', '7db38ac8-bc6d-4f0d-a6c0-b601a2360bf7', '33e6ee9d-96f6-4ea9-869e-dc1fdab21815']...
2025-08-02 19:44:33,682 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:14:33.682639"}
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:44:33,682 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:44:33,684 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:44:33,684 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:44:33,684 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:33,684 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:33,684 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:44:33,684 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:44:33,731 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:44:33,731 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5dd89363-59cc-4134-ada4-eeda877a51b5, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:44:33,731 - knowledge_base_executor - INFO - [QDRANT] Match: ID=729eb4c6-a6c3-4aed-9a25-aa597db91127, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:44:33,731 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9affc01a-813a-4ab4-afdc-e864c70dd44f, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:44:33,731 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ac5ffabf-b12b-4fc4-93be-9684c9047105, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:44:33,731 - knowledge_base_executor - INFO - [QDRANT] Match: ID=96f706da-38a1-4bc3-bd09-648ab7d128db, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:44:33,731 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:33,731 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:44:33,731 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.Plan.(PCB)49/09.27.00/96-97
2025-08-02 19:44:33,731 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:44:33,731 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:33,731 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:44:33,731 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:44:33,731 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:44:33,731 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:44:33,731 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:44:33,732 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:44:33,732 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:14:33.732516"}
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:44:33,732 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:44:33,733 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:44:33,734 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:44:33,734 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:33,734 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:33,734 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:44:33,734 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:44:33,780 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:44:33,780 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9a0db77f-d109-46ff-9dd5-42e6866482c2, Score=0.5587, Document=DBOD.PSBD.BC.88/16.13.100/2005-06
2025-08-02 19:44:33,780 - knowledge_base_executor - INFO - [QDRANT] Match: ID=529482b7-61a4-4dbc-85f6-082d3171e0ea, Score=0.5521, Document=Ref.No.UBD.DS. 2 /13.02.00/2002-03
2025-08-02 19:44:33,780 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b7aff217-917a-46f2-93ba-ecbc1a1eb565, Score=0.5448, Document=BP.BC.11/21.01.040/99-00
2025-08-02 19:44:33,780 - knowledge_base_executor - INFO - [QDRANT] Match: ID=998c8368-4404-41db-a8ac-d02d9c525c94, Score=0.5448, Document=BP.BC.11/21.01.040/99-00
2025-08-02 19:44:33,780 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b4358508-03e0-4c1e-b893-bcede5fbbb79, Score=0.5448, Document=BP.BC.11/21.01.040/99-00
2025-08-02 19:44:33,780 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:33,780 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:44:33,781 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.POT.1/UB.58-92/3
2025-08-02 19:44:33,781 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79']}
2025-08-02 19:44:33,781 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:33,781 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9a0db77f-d109-46ff-9dd5-42e6866482c2
2025-08-02 19:44:33,781 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 529482b7-61a4-4dbc-85f6-082d3171e0ea
2025-08-02 19:44:33,781 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b7aff217-917a-46f2-93ba-ecbc1a1eb565
2025-08-02 19:44:33,781 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565']...
2025-08-02 19:44:33,781 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:33,781 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:33,781 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:44:33,781 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79']}
2025-08-02 19:44:33,781 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:33,781 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9a0db77f-d109-46ff-9dd5-42e6866482c2
2025-08-02 19:44:33,782 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 529482b7-61a4-4dbc-85f6-082d3171e0ea
2025-08-02 19:44:33,782 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b7aff217-917a-46f2-93ba-ecbc1a1eb565
2025-08-02 19:44:33,782 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565']...
2025-08-02 19:44:33,782 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:33,782 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:33,782 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:44:33,782 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79']}
2025-08-02 19:44:33,782 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:33,782 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9a0db77f-d109-46ff-9dd5-42e6866482c2
2025-08-02 19:44:33,782 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 529482b7-61a4-4dbc-85f6-082d3171e0ea
2025-08-02 19:44:33,782 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b7aff217-917a-46f2-93ba-ecbc1a1eb565
2025-08-02 19:44:33,782 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565']...
2025-08-02 19:44:33,782 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:33,782 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:14:33.782453"}
2025-08-02 19:44:33,782 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:44:33,782 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:44:33,783 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:44:33,783 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:44:33,783 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:33,783 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:33,783 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:44:33,783 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:44:33,830 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:44:33,830 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5dd89363-59cc-4134-ada4-eeda877a51b5, Score=0.7338, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:44:33,830 - knowledge_base_executor - INFO - [QDRANT] Match: ID=729eb4c6-a6c3-4aed-9a25-aa597db91127, Score=0.7338, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:44:33,830 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9affc01a-813a-4ab4-afdc-e864c70dd44f, Score=0.7338, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:44:33,830 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ac5ffabf-b12b-4fc4-93be-9684c9047105, Score=0.7338, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:44:33,830 - knowledge_base_executor - INFO - [QDRANT] Match: ID=96f706da-38a1-4bc3-bd09-648ab7d128db, Score=0.7338, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:44:33,830 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:33,830 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:44:33,830 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.Plan.42/09.27.00-93/94
2025-08-02 19:44:33,830 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:44:33,831 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:44:33,831 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:44:33,831 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:14:33.831650"}
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:44:33,831 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:44:33,832 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:44:33,832 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:44:33,833 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:33,833 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:44:33,833 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:44:33,833 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=d0ce751c-5036-4326-afae-40c495df222e, Document=None, Chunk=2
2025-08-02 19:44:33,833 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-02 19:44:37,090 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: d0ce751c-5036-4326-afae-40c495df222e)
2025-08-02 19:44:37,090 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: d0ce751c-5036-4326-afae-40c495df222e) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:14:37.090975", "document_id": null, "uuid": "d0ce751c-5036-4326-afae-40c495df222e", "collection": "rbi_circular"}
2025-08-02 19:44:37,091 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:44:37,091 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:44:37,091 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
