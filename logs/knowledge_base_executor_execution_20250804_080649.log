2025-08-04 08:06:49,771 - knowledge_base_executor - <PERSON><PERSON><PERSON> - <PERSON><PERSON> configured to write logs to: logs/knowledge_base_executor_execution_20250804_080649.log
2025-08-04 08:06:49,771 - knowledge_base_executor - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-04 08:06:54,228 - knowledge_base_executor - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-04 08:07:13,251 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:07:13,251 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 08:07:13,251 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 08:07:13,251 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:07:13,252 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=1c368f28-1b03-5a01-9cfd-a5ace87ce1df, Document=RBI/2024-25/112, Chunk=None
2025-08-04 08:07:13,252 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/112 with text: 6475 chars
2025-08-04 08:07:15,353 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/112 (ID: 1c368f28-1b03-5a01-9cfd-a5ace87ce1df)
2025-08-04 08:07:15,353 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/112 (ID: 1c368f28-1b03-5a01-9cfd-a5ace87ce1df) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:37:15.353622", "document_id": "RBI/2024-25/112", "uuid": "1c368f28-1b03-5a01-9cfd-a5ace87ce1df", "collection": "rbi_other"}
2025-08-04 08:07:15,353 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:07:15,353 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:07:15,355 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:07:31,764 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:07:31,764 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 08:07:31,764 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 08:07:31,764 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:07:31,765 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=7746353b-0ee0-5b0b-a99e-5727978220c7, Document=RBI/2024-25/112, Chunk=0
2025-08-04 08:07:31,765 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/112 with text: 3340 chars
2025-08-04 08:07:35,671 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/112 (ID: 7746353b-0ee0-5b0b-a99e-5727978220c7)
2025-08-04 08:07:35,671 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/112 (ID: 7746353b-0ee0-5b0b-a99e-5727978220c7) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:37:35.671764", "document_id": "RBI/2024-25/112", "uuid": "7746353b-0ee0-5b0b-a99e-5727978220c7", "collection": "rbi_master_direction"}
2025-08-04 08:07:35,671 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:07:35,671 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:07:35,673 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:07:35,673 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:07:35,673 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 08:07:35,673 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 08:07:35,673 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:07:35,673 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=aaca94c9-8540-4b79-81d3-a022e7cb16c7, Document=None, Chunk=0
2025-08-04 08:07:35,673 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 3229 chars
2025-08-04 08:07:38,494 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: aaca94c9-8540-4b79-81d3-a022e7cb16c7)
2025-08-04 08:07:38,494 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: aaca94c9-8540-4b79-81d3-a022e7cb16c7) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:37:38.494141", "document_id": null, "uuid": "aaca94c9-8540-4b79-81d3-a022e7cb16c7", "collection": "rbi_circular"}
2025-08-04 08:07:38,494 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:07:38,494 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:07:38,494 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:07:48,817 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:07:48,817 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 08:07:48,817 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 08:07:48,817 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:07:48,817 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=cbdf28b9-f6cc-5c6b-83f6-1b5561cb1511, Document=RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26, Chunk=None
2025-08-04 08:07:48,817 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26 with text: 8000 chars
2025-08-04 08:07:51,198 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26 (ID: cbdf28b9-f6cc-5c6b-83f6-1b5561cb1511)
2025-08-04 08:07:51,198 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26 (ID: cbdf28b9-f6cc-5c6b-83f6-1b5561cb1511) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:37:51.198123", "document_id": "RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26", "uuid": "cbdf28b9-f6cc-5c6b-83f6-1b5561cb1511", "collection": "rbi_other"}
2025-08-04 08:07:51,198 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:07:51,198 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:07:51,198 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:08:00,701 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:08:00,701 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 08:08:00,701 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 08:08:00,701 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:08:00,702 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:08:00,813 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:08:00,813 - knowledge_base_executor - INFO - [QDRANT] Match: ID=aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4, Score=0.9121, Document=DoR.LIC.No.S2196/16.13.215/2025-26
2025-08-04 08:08:00,813 - knowledge_base_executor - INFO - [QDRANT] Match: ID=869f3b04-ec2f-595b-a89d-b1954df61f84, Score=0.9121, Document=DoR.LIC.No.S2196/16.13.215/2025-26
2025-08-04 08:08:00,813 - knowledge_base_executor - INFO - [QDRANT] Match: ID=1fb27723-a8c3-5847-8444-2413ace14ab0, Score=0.9121, Document=DoR.LIC.No.S2196/16.13.215/2025-26
2025-08-04 08:08:00,813 - knowledge_base_executor - INFO - [QDRANT] Match: ID=55c9fe74-886e-5477-8ed6-60c7dfda12b8, Score=0.9121, Document=DoR.LIC.No.S2196/16.13.215/2025-26
2025-08-04 08:08:00,813 - knowledge_base_executor - INFO - [QDRANT] Match: ID=a6aac802-03c2-508c-b910-f3a658bc3425, Score=0.9121, Document=DoR.LIC.No.S2196/16.13.215/2025-26
2025-08-04 08:08:00,813 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:08:00,813 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:08:00,815 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DoR.LIC.No.S1134/16.13.216/2025-26
2025-08-04 08:08:00,815 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', 'a6aac802-03c2-508c-b910-f3a658bc3425']}
2025-08-04 08:08:00,815 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:08:00,815 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4
2025-08-04 08:08:00,815 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 869f3b04-ec2f-595b-a89d-b1954df61f84
2025-08-04 08:08:00,815 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 1fb27723-a8c3-5847-8444-2413ace14ab0
2025-08-04 08:08:00,815 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0']...
2025-08-04 08:08:00,815 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:08:00,815 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', 'a6aac802-03c2-508c-b910-f3a658bc3425'])] must_not=None, Payload={}
2025-08-04 08:08:00,825 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-04 08:08:00,845 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3181 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:08:00,845 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-04 08:08:00,845 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:08:00,845 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:08:00,845 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', 'a6aac802-03c2-508c-b910-f3a658bc3425']}
2025-08-04 08:08:00,845 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:08:00,845 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4
2025-08-04 08:08:00,845 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 869f3b04-ec2f-595b-a89d-b1954df61f84
2025-08-04 08:08:00,845 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 1fb27723-a8c3-5847-8444-2413ace14ab0
2025-08-04 08:08:00,845 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0']...
2025-08-04 08:08:00,845 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:08:00,845 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', 'a6aac802-03c2-508c-b910-f3a658bc3425'])] must_not=None, Payload={}
2025-08-04 08:08:00,848 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:08:00,849 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:08:00,849 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:08:00,849 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:08:00,849 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', 'a6aac802-03c2-508c-b910-f3a658bc3425']}
2025-08-04 08:08:00,849 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:08:00,849 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4
2025-08-04 08:08:00,849 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 869f3b04-ec2f-595b-a89d-b1954df61f84
2025-08-04 08:08:00,849 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 1fb27723-a8c3-5847-8444-2413ace14ab0
2025-08-04 08:08:00,849 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0']...
2025-08-04 08:08:00,849 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:08:00,849 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', 'a6aac802-03c2-508c-b910-f3a658bc3425'])] must_not=None, Payload={}
2025-08-04 08:08:00,853 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:08:00,853 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:08:00,853 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T02:38:00.853136"}
2025-08-04 08:08:00,853 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:08:00,853 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:08:00,854 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:08:00,854 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:08:00,854 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 08:08:00,854 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 08:08:00,854 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:08:00,854 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=ac6ac9c1-4086-4253-a219-b157ab6f1e42, Document=None, Chunk=0
2025-08-04 08:08:00,854 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 5161 chars
2025-08-04 08:08:04,418 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: ac6ac9c1-4086-4253-a219-b157ab6f1e42)
2025-08-04 08:08:04,418 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: ac6ac9c1-4086-4253-a219-b157ab6f1e42) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:38:04.418823", "document_id": null, "uuid": "ac6ac9c1-4086-4253-a219-b157ab6f1e42", "collection": "rbi_circular"}
2025-08-04 08:08:04,418 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:08:04,418 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:08:04,419 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:08:13,887 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:08:13,888 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:08:13,888 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:08:13,889 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:08:13,889 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=f34b8504-0365-517b-be4b-166f6d153ccd, Document=RBI/FED/2025-26/29 - A.P. (DIR Series) Circular No. 17, Chunk=None
2025-08-04 08:08:13,889 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/FED/2025-26/29 - A.P. (DIR Series) Circular No. 17 with text: 8000 chars
2025-08-04 08:08:17,718 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/FED/2025-26/29 - A.P. (DIR Series) Circular No. 17 (ID: f34b8504-0365-517b-be4b-166f6d153ccd)
2025-08-04 08:08:17,718 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/FED/2025-26/29 - A.P. (DIR Series) Circular No. 17 (ID: f34b8504-0365-517b-be4b-166f6d153ccd) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:38:17.718947", "document_id": "RBI/FED/2025-26/29 - A.P. (DIR Series) Circular No. 17", "uuid": "f34b8504-0365-517b-be4b-166f6d153ccd", "collection": "rbi_other"}
2025-08-04 08:08:17,719 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:08:17,719 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:08:17,719 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:08:25,174 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:08:25,175 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:08:25,175 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:08:25,175 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:08:25,175 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] Match: ID=4700aab3-5bc9-5aac-a983-e08bd853f0bb, Score=0.5129, Document=FEMA 23(R)/2015-RB
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06f77fc3-7228-514f-8562-24519f5d9d4a, Score=0.5129, Document=FEMA 23(R)/2015-RB
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d0e725b-aeac-5e6b-80ff-92fe03d7caf2, Score=0.5129, Document=FEMA 23(R)/2015-RB
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] Match: ID=81ccb53b-4614-5c94-a95b-f02af834fc91, Score=0.5129, Document=FEMA 23(R)/2015-RB
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] Match: ID=709094ad-5569-562d-bfef-a7c7fe49438c, Score=0.5129, Document=FEMA 23(R)/2015-RB
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'FEMA 1999 Directions' → 'FEMA1999Directions'
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: FEMA1999Directions
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2']...
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:08:25,270 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:08:25,284 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-04 08:08:25,302 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3182 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:08:25,302 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-04 08:08:25,302 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:08:25,302 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:08:25,302 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:08:25,302 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:08:25,302 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:08:25,302 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:08:25,302 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:08:25,302 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2']...
2025-08-04 08:08:25,302 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:08:25,302 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:08:25,305 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:08:25,305 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:08:25,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:08:25,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:08:25,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:08:25,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:08:25,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:08:25,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:08:25,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:08:25,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2']...
2025-08-04 08:08:25,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:08:25,305 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:08:25,307 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:08:25,307 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:08:25,307 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T02:38:25.307107"}
2025-08-04 08:08:25,307 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:08:25,307 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:08:25,308 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:08:25,308 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:08:25,308 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:08:25,308 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:08:25,308 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:08:25,308 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=b9470b5d-ca8f-4338-abe2-2260e9a2e4ed, Document=None, Chunk=0
2025-08-04 08:08:25,308 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 1537 chars
2025-08-04 08:08:27,922 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: b9470b5d-ca8f-4338-abe2-2260e9a2e4ed)
2025-08-04 08:08:27,922 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: b9470b5d-ca8f-4338-abe2-2260e9a2e4ed) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:38:27.922605", "document_id": null, "uuid": "b9470b5d-ca8f-4338-abe2-2260e9a2e4ed", "collection": "rbi_circular"}
2025-08-04 08:08:27,922 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:08:27,922 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:08:27,923 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:08:40,541 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:08:40,542 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:08:40,542 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:08:40,543 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:08:40,543 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:08:40,641 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:08:40,641 - knowledge_base_executor - INFO - [QDRANT] Match: ID=f7e9c5a4-9d26-40a5-9b97-4698177fce16, Score=0.9461, Document=A.P. (DIR Series) Circular No.25
2025-08-04 08:08:40,641 - knowledge_base_executor - INFO - [QDRANT] Match: ID=f5227b9a-8ad4-4475-abb1-cad740204786, Score=0.9461, Document=A.P. (DIR Series) Circular No.25
2025-08-04 08:08:40,641 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2dace538-c7dd-4d91-8a0c-98d413124556, Score=0.9450, Document=A.P.(DIR Series) Circular No.17
2025-08-04 08:08:40,641 - knowledge_base_executor - INFO - [QDRANT] Match: ID=08eb16af-79fe-4148-8a1e-296a970e3fb1, Score=0.9450, Document=A.P.(DIR Series) Circular No.17
2025-08-04 08:08:40,642 - knowledge_base_executor - INFO - [QDRANT] Match: ID=414991ea-c107-4d22-b4f8-1d3c9e37a606, Score=0.9450, Document=A.P.(DIR Series) Circular No.17
2025-08-04 08:08:40,642 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:08:40,642 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:08:40,642 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'A.P. (DIR Series) Circular No. 17/2024-25' → 'A.P.(DIRSeries)CircularNo.17/2024-25'
2025-08-04 08:08:40,642 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: A.P.(DIRSeries)CircularNo.17/2024-25
2025-08-04 08:08:40,642 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '414991ea-c107-4d22-b4f8-1d3c9e37a606']}
2025-08-04 08:08:40,642 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:08:40,642 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f7e9c5a4-9d26-40a5-9b97-4698177fce16
2025-08-04 08:08:40,642 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f5227b9a-8ad4-4475-abb1-cad740204786
2025-08-04 08:08:40,642 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2dace538-c7dd-4d91-8a0c-98d413124556
2025-08-04 08:08:40,642 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556']...
2025-08-04 08:08:40,642 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:08:40,642 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '414991ea-c107-4d22-b4f8-1d3c9e37a606'])] must_not=None, Payload={}
2025-08-04 08:08:40,645 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:08:40,645 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:08:40,645 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:08:40,645 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:08:40,645 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '414991ea-c107-4d22-b4f8-1d3c9e37a606']}
2025-08-04 08:08:40,645 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:08:40,645 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f7e9c5a4-9d26-40a5-9b97-4698177fce16
2025-08-04 08:08:40,645 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f5227b9a-8ad4-4475-abb1-cad740204786
2025-08-04 08:08:40,645 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2dace538-c7dd-4d91-8a0c-98d413124556
2025-08-04 08:08:40,645 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556']...
2025-08-04 08:08:40,645 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:08:40,645 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '414991ea-c107-4d22-b4f8-1d3c9e37a606'])] must_not=None, Payload={}
2025-08-04 08:08:40,648 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:08:40,648 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:08:40,648 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:08:40,648 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:08:40,648 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '414991ea-c107-4d22-b4f8-1d3c9e37a606']}
2025-08-04 08:08:40,648 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:08:40,648 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f7e9c5a4-9d26-40a5-9b97-4698177fce16
2025-08-04 08:08:40,648 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f5227b9a-8ad4-4475-abb1-cad740204786
2025-08-04 08:08:40,648 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2dace538-c7dd-4d91-8a0c-98d413124556
2025-08-04 08:08:40,648 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556']...
2025-08-04 08:08:40,648 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:08:40,648 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '414991ea-c107-4d22-b4f8-1d3c9e37a606'])] must_not=None, Payload={}
2025-08-04 08:08:40,660 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:08:40,685 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13017 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:08:40,685 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:08:40,685 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T02:38:40.685265"}
2025-08-04 08:08:40,685 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:08:40,685 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:08:40,686 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:08:40,686 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:08:40,686 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:08:40,686 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:08:40,686 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:08:40,686 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=03480982-3d6b-48de-8f87-df28c28ad1df, Document=None, Chunk=1
2025-08-04 08:08:40,686 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-04 08:08:45,071 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 03480982-3d6b-48de-8f87-df28c28ad1df)
2025-08-04 08:08:45,072 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 03480982-3d6b-48de-8f87-df28c28ad1df) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:38:45.071994", "document_id": null, "uuid": "03480982-3d6b-48de-8f87-df28c28ad1df", "collection": "rbi_circular"}
2025-08-04 08:08:45,072 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:08:45,072 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:08:45,072 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:08:55,252 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:08:55,252 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-04 08:08:55,252 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-04 08:08:55,252 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:08:55,252 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=e4c161fd-2086-5afb-8e12-e5da3b87a499, Document=RBI/2024-2025/125 - A.P. (DIR Series) Circular No. 22, Chunk=None
2025-08-04 08:08:55,253 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-2025/125 - A.P. (DIR Series) Circular No. 22 with text: 8000 chars
2025-08-04 08:08:57,106 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-2025/125 - A.P. (DIR Series) Circular No. 22 (ID: e4c161fd-2086-5afb-8e12-e5da3b87a499)
2025-08-04 08:08:57,106 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-2025/125 - A.P. (DIR Series) Circular No. 22 (ID: e4c161fd-2086-5afb-8e12-e5da3b87a499) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:38:57.106743", "document_id": "RBI/2024-2025/125 - A.P. (DIR Series) Circular No. 22", "uuid": "e4c161fd-2086-5afb-8e12-e5da3b87a499", "collection": "rbi_other"}
2025-08-04 08:08:57,106 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:08:57,107 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:08:57,107 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:09:03,575 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:09:03,575 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-04 08:09:03,576 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-04 08:09:03,576 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:09:03,576 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=2aa27d25-3357-5aba-9a72-2ae8743e71fe, Document=A.P. (DIR Series) Circular No. 22, Chunk=0
2025-08-04 08:09:03,576 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for A.P. (DIR Series) Circular No. 22 with text: 5237 chars
2025-08-04 08:09:06,356 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for A.P. (DIR Series) Circular No. 22 (ID: 2aa27d25-3357-5aba-9a72-2ae8743e71fe)
2025-08-04 08:09:06,356 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document A.P. (DIR Series) Circular No. 22 (ID: 2aa27d25-3357-5aba-9a72-2ae8743e71fe) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:39:06.356589", "document_id": "A.P. (DIR Series) Circular No. 22", "uuid": "2aa27d25-3357-5aba-9a72-2ae8743e71fe", "collection": "rbi_master_direction"}
2025-08-04 08:09:06,356 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:09:06,356 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:09:06,357 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:09:06,357 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:09:06,357 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-04 08:09:06,357 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-04 08:09:06,357 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:09:06,357 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=c8c021d4-7ae0-4012-902b-76fa20f1079d, Document=None, Chunk=0
2025-08-04 08:09:06,357 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 5089 chars
2025-08-04 08:09:09,230 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: c8c021d4-7ae0-4012-902b-76fa20f1079d)
2025-08-04 08:09:09,231 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: c8c021d4-7ae0-4012-902b-76fa20f1079d) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:39:09.231027", "document_id": null, "uuid": "c8c021d4-7ae0-4012-902b-76fa20f1079d", "collection": "rbi_circular"}
2025-08-04 08:09:09,231 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:09:09,231 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:09:09,231 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:09:19,873 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:09:19,874 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:09:19,874 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:09:19,874 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:09:19,874 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=657fee29-f7c0-5963-bed8-00f8aec78f2b, Document=RBI/2024-25/127 - ACC.REC.No.67/21.04.018/2024-25, Chunk=None
2025-08-04 08:09:19,875 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/127 - ACC.REC.No.67/21.04.018/2024-25 with text: 8000 chars
2025-08-04 08:09:23,056 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/127 - ACC.REC.No.67/21.04.018/2024-25 (ID: 657fee29-f7c0-5963-bed8-00f8aec78f2b)
2025-08-04 08:09:23,056 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/127 - ACC.REC.No.67/21.04.018/2024-25 (ID: 657fee29-f7c0-5963-bed8-00f8aec78f2b) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:39:23.056556", "document_id": "RBI/2024-25/127 - ACC.REC.No.67/21.04.018/2024-25", "uuid": "657fee29-f7c0-5963-bed8-00f8aec78f2b", "collection": "rbi_other"}
2025-08-04 08:09:23,056 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:09:23,056 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:09:23,057 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:09:30,718 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:09:30,718 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:09:30,718 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:09:30,718 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:09:30,718 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:09:30,813 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:09:30,813 - knowledge_base_executor - INFO - [QDRANT] Match: ID=d675122a-f2f8-41f1-8672-a9afc3650b80, Score=0.9491, Document=RBI/2023-24/21
2025-08-04 08:09:30,813 - knowledge_base_executor - INFO - [QDRANT] Match: ID=a4084c05-a76e-45a7-91e8-e4f9c9f1cb65, Score=0.9471, Document=RBI/2023-24/81
2025-08-04 08:09:30,813 - knowledge_base_executor - INFO - [QDRANT] Match: ID=8953ae04-2722-42bc-b33e-e7b31633fdca, Score=0.9432, Document=RBI/2023-24/93
2025-08-04 08:09:30,813 - knowledge_base_executor - INFO - [QDRANT] Match: ID=a6de671a-3e3b-43be-84d8-9a293bbf9a0b, Score=0.9374, Document=RBI/2022-23/44
2025-08-04 08:09:30,813 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2db9b912-a821-46d7-805e-758a4f71d6f9, Score=0.9359, Document=RBI/2022-2023/47
2025-08-04 08:09:30,813 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:09:30,813 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:09:30,813 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: RBI/2023-24/456
2025-08-04 08:09:30,813 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', '2db9b912-a821-46d7-805e-758a4f71d6f9']}
2025-08-04 08:09:30,813 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:09:30,813 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = d675122a-f2f8-41f1-8672-a9afc3650b80
2025-08-04 08:09:30,813 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a4084c05-a76e-45a7-91e8-e4f9c9f1cb65
2025-08-04 08:09:30,813 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8953ae04-2722-42bc-b33e-e7b31633fdca
2025-08-04 08:09:30,813 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca']...
2025-08-04 08:09:30,814 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:09:30,814 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', '2db9b912-a821-46d7-805e-758a4f71d6f9'])] must_not=None, Payload={}
2025-08-04 08:09:30,817 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:09:30,817 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:09:30,817 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:09:30,817 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:09:30,817 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', '2db9b912-a821-46d7-805e-758a4f71d6f9']}
2025-08-04 08:09:30,817 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:09:30,817 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = d675122a-f2f8-41f1-8672-a9afc3650b80
2025-08-04 08:09:30,817 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a4084c05-a76e-45a7-91e8-e4f9c9f1cb65
2025-08-04 08:09:30,817 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8953ae04-2722-42bc-b33e-e7b31633fdca
2025-08-04 08:09:30,817 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca']...
2025-08-04 08:09:30,817 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:09:30,817 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', '2db9b912-a821-46d7-805e-758a4f71d6f9'])] must_not=None, Payload={}
2025-08-04 08:09:30,820 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:09:30,820 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:09:30,820 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:09:30,821 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:09:30,821 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', '2db9b912-a821-46d7-805e-758a4f71d6f9']}
2025-08-04 08:09:30,821 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:09:30,821 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = d675122a-f2f8-41f1-8672-a9afc3650b80
2025-08-04 08:09:30,821 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a4084c05-a76e-45a7-91e8-e4f9c9f1cb65
2025-08-04 08:09:30,821 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8953ae04-2722-42bc-b33e-e7b31633fdca
2025-08-04 08:09:30,821 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca']...
2025-08-04 08:09:30,821 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:09:30,821 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', '2db9b912-a821-46d7-805e-758a4f71d6f9'])] must_not=None, Payload={}
2025-08-04 08:09:30,842 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:09:30,867 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13020 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:09:30,868 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:09:30,868 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T02:39:30.868124"}
2025-08-04 08:09:30,868 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:09:30,868 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:09:30,869 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:09:30,869 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:09:30,869 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:09:30,869 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:09:30,869 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:09:30,869 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=054010f9-665e-4987-8675-92d4d538230f, Document=None, Chunk=0
2025-08-04 08:09:30,869 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 99 chars
2025-08-04 08:09:33,309 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 054010f9-665e-4987-8675-92d4d538230f)
2025-08-04 08:09:33,309 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 054010f9-665e-4987-8675-92d4d538230f) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:39:33.309525", "document_id": null, "uuid": "054010f9-665e-4987-8675-92d4d538230f", "collection": "rbi_circular"}
2025-08-04 08:09:33,309 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:09:33,309 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:09:33,310 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:09:51,907 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: NO_ACTION
2025-08-04 08:09:51,907 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:09:51,907 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:09:51,907 - knowledge_base_executor - INFO - [QDRANT] Action details: NO_ACTION
2025-08-04 08:09:51,907 - knowledge_base_executor - INFO - [QDRANT] Action NO_ACTION executed with result: {"action": "NO_ACTION", "success": true, "details": "No action executed.", "timestamp": "2025-08-04T02:39:51.907653"}
2025-08-04 08:09:51,907 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:09:51,907 - knowledge_base_executor - INFO - [QDRANT] Action 1: NO_ACTION - ✅ SUCCESS
2025-08-04 08:09:51,910 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:09:51,912 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:09:51,912 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:09:51,912 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:09:51,913 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:09:51,913 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] Match: ID=a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e, Score=0.5672, Document=RBI/2017-18/203  FIDD.CO.Plan.BC.22/04.09.01/2017-18
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ce1d93f5-7444-4a0c-b4e4-c8b3057c085d, Score=0.5672, Document=RBI/2017-18/203 FIDD.CO.Plan.BC.22/04.09.01/2017-18
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] Match: ID=759a4091-ddd1-4f1d-8c67-d0fe16e111db, Score=0.5672, Document=RBI/2017-18/203 FIDD.CO.Plan.BC.22/04.09.01/2017-18
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] Match: ID=8261f81f-e857-46c4-b9c6-0e3ddca0bc68, Score=0.5672, Document=RBI/2017-18/203  FIDD.CO.Plan.BC.22/04.09.01/2017-18
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] Match: ID=09e5bde4-abd1-49fa-a1fe-36bc3475f196, Score=0.5534, Document=RBI/2007-2008/159 RPCD.CO.Plan.BC.  30 /04.09.01/2007-08
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'Reserve Bank of India (Financial Statements - Presentation and Disclosures) Directions, 2021' → 'ReserveBankofIndia(FinancialStatements-PresentationandDisclosures)Directions,2021'
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: ReserveBankofIndia(FinancialStatements-PresentationandDisclosures)Directions,2021
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', '09e5bde4-abd1-49fa-a1fe-36bc3475f196']}
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ce1d93f5-7444-4a0c-b4e4-c8b3057c085d
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 759a4091-ddd1-4f1d-8c67-d0fe16e111db
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db']...
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:09:51,998 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', '09e5bde4-abd1-49fa-a1fe-36bc3475f196'])] must_not=None, Payload={}
2025-08-04 08:09:52,000 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:09:52,000 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:09:52,000 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:09:52,000 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:09:52,000 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', '09e5bde4-abd1-49fa-a1fe-36bc3475f196']}
2025-08-04 08:09:52,000 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:09:52,000 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e
2025-08-04 08:09:52,000 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ce1d93f5-7444-4a0c-b4e4-c8b3057c085d
2025-08-04 08:09:52,000 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 759a4091-ddd1-4f1d-8c67-d0fe16e111db
2025-08-04 08:09:52,000 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db']...
2025-08-04 08:09:52,000 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:09:52,001 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', '09e5bde4-abd1-49fa-a1fe-36bc3475f196'])] must_not=None, Payload={}
2025-08-04 08:09:52,002 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:09:52,002 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:09:52,002 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:09:52,002 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:09:52,003 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', '09e5bde4-abd1-49fa-a1fe-36bc3475f196']}
2025-08-04 08:09:52,003 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:09:52,003 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e
2025-08-04 08:09:52,003 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ce1d93f5-7444-4a0c-b4e4-c8b3057c085d
2025-08-04 08:09:52,003 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 759a4091-ddd1-4f1d-8c67-d0fe16e111db
2025-08-04 08:09:52,003 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db']...
2025-08-04 08:09:52,003 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:09:52,003 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', '09e5bde4-abd1-49fa-a1fe-36bc3475f196'])] must_not=None, Payload={}
2025-08-04 08:09:52,011 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:09:52,027 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13022 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:09:52,027 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:09:52,027 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T02:39:52.027654"}
2025-08-04 08:09:52,027 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:09:52,027 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:09:52,028 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:09:52,028 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:09:52,028 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:09:52,028 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:09:52,028 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:09:52,028 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=31e1af32-e54c-4945-ac63-23257d6419ff, Document=None, Chunk=1
2025-08-04 08:09:52,029 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-04 08:09:56,343 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 31e1af32-e54c-4945-ac63-23257d6419ff)
2025-08-04 08:09:56,343 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 31e1af32-e54c-4945-ac63-23257d6419ff) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:39:56.343967", "document_id": null, "uuid": "31e1af32-e54c-4945-ac63-23257d6419ff", "collection": "rbi_circular"}
2025-08-04 08:09:56,344 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:09:56,344 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:09:56,344 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:10:11,441 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:10:11,442 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-04 08:10:11,442 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-04 08:10:11,442 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:10:11,442 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=f317ee5b-1f85-50d9-9e38-207de6cde07f, Document=RBI/2024-25/124, Chunk=None
2025-08-04 08:10:11,442 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/124 with text: 8000 chars
2025-08-04 08:10:13,487 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/124 (ID: f317ee5b-1f85-50d9-9e38-207de6cde07f)
2025-08-04 08:10:13,488 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/124 (ID: f317ee5b-1f85-50d9-9e38-207de6cde07f) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:40:13.487990", "document_id": "RBI/2024-25/124", "uuid": "f317ee5b-1f85-50d9-9e38-207de6cde07f", "collection": "rbi_other"}
2025-08-04 08:10:13,488 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:10:13,488 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:10:13,488 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:10:20,895 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:10:20,895 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-04 08:10:20,895 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-04 08:10:20,895 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:10:20,895 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=a448ef12-4834-56ef-85db-4e826d79a158, Document=RBI/2024-25/124, Chunk=0
2025-08-04 08:10:20,895 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/124 with text: 4506 chars
2025-08-04 08:10:24,201 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/124 (ID: a448ef12-4834-56ef-85db-4e826d79a158)
2025-08-04 08:10:24,201 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/124 (ID: a448ef12-4834-56ef-85db-4e826d79a158) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:40:24.201195", "document_id": "RBI/2024-25/124", "uuid": "a448ef12-4834-56ef-85db-4e826d79a158", "collection": "rbi_master_direction"}
2025-08-04 08:10:24,201 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:10:24,201 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:10:24,201 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:10:24,202 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:10:24,202 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-04 08:10:24,202 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-04 08:10:24,202 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:10:24,202 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=08cefc2e-715b-46d7-a168-fcdd973cc7fc, Document=None, Chunk=0
2025-08-04 08:10:24,202 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 4397 chars
2025-08-04 08:10:26,033 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 08cefc2e-715b-46d7-a168-fcdd973cc7fc)
2025-08-04 08:10:26,033 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 08cefc2e-715b-46d7-a168-fcdd973cc7fc) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:40:26.033481", "document_id": null, "uuid": "08cefc2e-715b-46d7-a168-fcdd973cc7fc", "collection": "rbi_circular"}
2025-08-04 08:10:26,033 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:10:26,033 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:10:26,034 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:10:33,432 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:10:33,433 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-04 08:10:33,433 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-04 08:10:33,433 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:10:33,433 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=e46b6e7b-a5cd-5a70-924d-cd0831dafca0, Document=RBI/2025-26/28, Chunk=None
2025-08-04 08:10:33,433 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/28 with text: 8000 chars
2025-08-04 08:10:35,426 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/28 (ID: e46b6e7b-a5cd-5a70-924d-cd0831dafca0)
2025-08-04 08:10:35,426 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/28 (ID: e46b6e7b-a5cd-5a70-924d-cd0831dafca0) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:40:35.426389", "document_id": "RBI/2025-26/28", "uuid": "e46b6e7b-a5cd-5a70-924d-cd0831dafca0", "collection": "rbi_circular"}
2025-08-04 08:10:35,426 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:10:35,426 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:10:35,427 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:10:41,777 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:10:41,777 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-04 08:10:41,777 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-04 08:10:41,777 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:10:41,777 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=623959a7-905c-5f0a-8a39-0577a960145b, Document=RBI/2025-26/28, Chunk=0
2025-08-04 08:10:41,777 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/28 with text: 4737 chars
2025-08-04 08:10:45,358 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/28 (ID: 623959a7-905c-5f0a-8a39-0577a960145b)
2025-08-04 08:10:45,358 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/28 (ID: 623959a7-905c-5f0a-8a39-0577a960145b) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:40:45.358441", "document_id": "RBI/2025-26/28", "uuid": "623959a7-905c-5f0a-8a39-0577a960145b", "collection": "rbi_master_direction"}
2025-08-04 08:10:45,358 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:10:45,358 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:10:45,359 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:10:45,359 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:10:45,359 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-04 08:10:45,359 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-04 08:10:45,359 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:10:45,359 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=b3ba385b-3d91-4e51-ae52-5b444a99d572, Document=None, Chunk=0
2025-08-04 08:10:45,359 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 4621 chars
2025-08-04 08:10:47,914 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: b3ba385b-3d91-4e51-ae52-5b444a99d572)
2025-08-04 08:10:47,914 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: b3ba385b-3d91-4e51-ae52-5b444a99d572) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:40:47.914407", "document_id": null, "uuid": "b3ba385b-3d91-4e51-ae52-5b444a99d572", "collection": "rbi_circular"}
2025-08-04 08:10:47,914 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:10:47,914 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:10:47,915 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:10:55,172 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:10:55,172 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-04 08:10:55,172 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-04 08:10:55,172 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:10:55,172 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=ad1f9bd1-c2d6-5820-8900-60653bde348a, Document=RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25, Chunk=None
2025-08-04 08:10:55,172 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 with text: 8000 chars
2025-08-04 08:10:56,958 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 (ID: ad1f9bd1-c2d6-5820-8900-60653bde348a)
2025-08-04 08:10:56,958 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 (ID: ad1f9bd1-c2d6-5820-8900-60653bde348a) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:40:56.958603", "document_id": "RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25", "uuid": "ad1f9bd1-c2d6-5820-8900-60653bde348a", "collection": "rbi_other"}
2025-08-04 08:10:56,958 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:10:56,958 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:10:56,959 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:11:03,793 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:11:03,793 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-04 08:11:03,800 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-04 08:11:03,801 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:11:03,801 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:11:03,875 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:11:03,875 - knowledge_base_executor - INFO - [QDRANT] Match: ID=45e1d982-dbae-4bda-9493-52ab74735756, Score=0.9639, Document=DoR.RET.REC.12/12.01.001/2024-25
2025-08-04 08:11:03,875 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2b03a8d3-091a-479e-a7bd-db44ec4f4400, Score=0.8551, Document=DOR.CRE.REC.No.06/08.12.001/2023-24
2025-08-04 08:11:03,875 - knowledge_base_executor - INFO - [QDRANT] Match: ID=78e63424-31da-4ff2-a793-3897c8a0ce69, Score=0.8551, Document=DOR.CRE.REC.No.06/08.12.001/2023-24
2025-08-04 08:11:03,875 - knowledge_base_executor - INFO - [QDRANT] Match: ID=8076eaa7-c954-4306-962c-9ee9d870752d, Score=0.8551, Document=DOR.CRE.REC.No.06/08.12.001/2023-24
2025-08-04 08:11:03,875 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3814cb01-95e2-465c-aef2-6ae7ebedce7b, Score=0.8551, Document=DOR.CRE.REC.No.06/08.12.001/2023-24
2025-08-04 08:11:03,875 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:11:03,875 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:11:03,875 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DOR.RET.REC.101/12.01.001/2022-23
2025-08-04 08:11:03,875 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69', '8076eaa7-c954-4306-962c-9ee9d870752d', '3814cb01-95e2-465c-aef2-6ae7ebedce7b']}
2025-08-04 08:11:03,875 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:11:03,875 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 45e1d982-dbae-4bda-9493-52ab74735756
2025-08-04 08:11:03,875 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2b03a8d3-091a-479e-a7bd-db44ec4f4400
2025-08-04 08:11:03,875 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 78e63424-31da-4ff2-a793-3897c8a0ce69
2025-08-04 08:11:03,876 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69']...
2025-08-04 08:11:03,876 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:11:03,876 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69', '8076eaa7-c954-4306-962c-9ee9d870752d', '3814cb01-95e2-465c-aef2-6ae7ebedce7b'])] must_not=None, Payload={}
2025-08-04 08:11:03,879 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:11:03,879 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:11:03,879 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:11:03,879 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:11:03,879 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69', '8076eaa7-c954-4306-962c-9ee9d870752d', '3814cb01-95e2-465c-aef2-6ae7ebedce7b']}
2025-08-04 08:11:03,879 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:11:03,879 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 45e1d982-dbae-4bda-9493-52ab74735756
2025-08-04 08:11:03,879 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2b03a8d3-091a-479e-a7bd-db44ec4f4400
2025-08-04 08:11:03,879 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 78e63424-31da-4ff2-a793-3897c8a0ce69
2025-08-04 08:11:03,879 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69']...
2025-08-04 08:11:03,879 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:11:03,879 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69', '8076eaa7-c954-4306-962c-9ee9d870752d', '3814cb01-95e2-465c-aef2-6ae7ebedce7b'])] must_not=None, Payload={}
2025-08-04 08:11:03,900 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 4 matching docs in rbi_master_circular
2025-08-04 08:11:03,923 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8286 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:11:03,923 - knowledge_base_executor - INFO - ✅ Successfully updated 4 documents in collection: rbi_master_circular
2025-08-04 08:11:03,923 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:11:03,923 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:11:03,923 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69', '8076eaa7-c954-4306-962c-9ee9d870752d', '3814cb01-95e2-465c-aef2-6ae7ebedce7b']}
2025-08-04 08:11:03,923 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:11:03,923 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 45e1d982-dbae-4bda-9493-52ab74735756
2025-08-04 08:11:03,923 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2b03a8d3-091a-479e-a7bd-db44ec4f4400
2025-08-04 08:11:03,923 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 78e63424-31da-4ff2-a793-3897c8a0ce69
2025-08-04 08:11:03,924 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69']...
2025-08-04 08:11:03,924 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:11:03,924 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69', '8076eaa7-c954-4306-962c-9ee9d870752d', '3814cb01-95e2-465c-aef2-6ae7ebedce7b'])] must_not=None, Payload={}
2025-08-04 08:11:03,927 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 1 matching docs in rbi_circular
2025-08-04 08:11:03,936 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13027 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:11:03,936 - knowledge_base_executor - INFO - ✅ Successfully updated 1 documents in collection: rbi_circular
2025-08-04 08:11:03,936 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:41:03.936304"}
2025-08-04 08:11:03,936 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:11:03,936 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:11:03,937 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:11:03,937 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:11:03,937 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-04 08:11:03,937 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-04 08:11:03,937 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:11:03,937 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=275784fb-4f8e-48a2-a67c-7bf6649930d8, Document=None, Chunk=0
2025-08-04 08:11:03,937 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 5846 chars
2025-08-04 08:11:07,053 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 275784fb-4f8e-48a2-a67c-7bf6649930d8)
2025-08-04 08:11:07,053 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 275784fb-4f8e-48a2-a67c-7bf6649930d8) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:41:07.053741", "document_id": null, "uuid": "275784fb-4f8e-48a2-a67c-7bf6649930d8", "collection": "rbi_circular"}
2025-08-04 08:11:07,053 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:11:07,053 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:11:07,054 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:11:19,053 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:11:19,054 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-04 08:11:19,054 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-04 08:11:19,055 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:11:19,055 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=6ea15a2f-d8aa-5ce9-b99a-8f38b18059ae, Document=RBI/2024-25/129, Chunk=None
2025-08-04 08:11:19,055 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/129 with text: 8000 chars
2025-08-04 08:11:22,398 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/129 (ID: 6ea15a2f-d8aa-5ce9-b99a-8f38b18059ae)
2025-08-04 08:11:22,398 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/129 (ID: 6ea15a2f-d8aa-5ce9-b99a-8f38b18059ae) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:41:22.398303", "document_id": "RBI/2024-25/129", "uuid": "6ea15a2f-d8aa-5ce9-b99a-8f38b18059ae", "collection": "rbi_other"}
2025-08-04 08:11:22,398 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:11:22,398 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:11:22,399 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:11:27,965 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:11:27,965 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-04 08:11:27,965 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-04 08:11:27,966 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:11:27,966 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] Match: ID=35918048-dd38-4dc2-897c-c169f5aefc30, Score=0.7327, Document=RBI/2024-25/112 DOR.CO.SOG(Leg) No.59/09.08.024/2024-25
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3d495fa6-6797-461c-bf81-7de0eb59a24a, Score=0.7195, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9741a628-999e-4b3f-aa8d-da299fe6f6c1, Score=0.7195, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2d0ef267-a504-424c-b287-4a442abd8753, Score=0.6894, Document=LEG.BC.114/09.06.002/2000-01
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0f2f98c6-e889-421f-806a-dd3947721bf8, Score=0.6806, Document=RBI/2023-24/137  DOR.SOG (LEG).REC/84/09.08.024/2023-24
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DOR.CO.SOG(Leg)No.59/09.08.02/2024-25
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['35918048-dd38-4dc2-897c-c169f5aefc30', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '2d0ef267-a504-424c-b287-4a442abd8753', '0f2f98c6-e889-421f-806a-dd3947721bf8']}
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 35918048-dd38-4dc2-897c-c169f5aefc30
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['35918048-dd38-4dc2-897c-c169f5aefc30', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '9741a628-999e-4b3f-aa8d-da299fe6f6c1']...
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:11:28,070 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['35918048-dd38-4dc2-897c-c169f5aefc30', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '2d0ef267-a504-424c-b287-4a442abd8753', '0f2f98c6-e889-421f-806a-dd3947721bf8'])] must_not=None, Payload={}
2025-08-04 08:11:28,074 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:11:28,074 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:11:28,074 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:11:28,074 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:11:28,074 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['35918048-dd38-4dc2-897c-c169f5aefc30', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '2d0ef267-a504-424c-b287-4a442abd8753', '0f2f98c6-e889-421f-806a-dd3947721bf8']}
2025-08-04 08:11:28,074 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:11:28,074 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 35918048-dd38-4dc2-897c-c169f5aefc30
2025-08-04 08:11:28,074 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-04 08:11:28,074 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-04 08:11:28,074 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['35918048-dd38-4dc2-897c-c169f5aefc30', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '9741a628-999e-4b3f-aa8d-da299fe6f6c1']...
2025-08-04 08:11:28,074 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:11:28,074 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['35918048-dd38-4dc2-897c-c169f5aefc30', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '2d0ef267-a504-424c-b287-4a442abd8753', '0f2f98c6-e889-421f-806a-dd3947721bf8'])] must_not=None, Payload={}
2025-08-04 08:11:28,077 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:11:28,077 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:11:28,077 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:11:28,077 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:11:28,077 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['35918048-dd38-4dc2-897c-c169f5aefc30', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '2d0ef267-a504-424c-b287-4a442abd8753', '0f2f98c6-e889-421f-806a-dd3947721bf8']}
2025-08-04 08:11:28,078 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:11:28,078 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 35918048-dd38-4dc2-897c-c169f5aefc30
2025-08-04 08:11:28,078 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-04 08:11:28,078 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-04 08:11:28,078 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['35918048-dd38-4dc2-897c-c169f5aefc30', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '9741a628-999e-4b3f-aa8d-da299fe6f6c1']...
2025-08-04 08:11:28,078 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:11:28,078 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['35918048-dd38-4dc2-897c-c169f5aefc30', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '2d0ef267-a504-424c-b287-4a442abd8753', '0f2f98c6-e889-421f-806a-dd3947721bf8'])] must_not=None, Payload={}
2025-08-04 08:11:28,087 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:11:28,103 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13029 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:11:28,103 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:11:28,104 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T02:41:28.103883"}
2025-08-04 08:11:28,104 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:11:28,104 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:11:28,105 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:11:28,105 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:11:28,105 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-04 08:11:28,105 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-04 08:11:28,105 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:11:28,106 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=daad5b5a-7dcc-4397-92fe-c4fe7ec22384, Document=None, Chunk=0
2025-08-04 08:11:28,106 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 6049 chars
2025-08-04 08:11:30,400 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: daad5b5a-7dcc-4397-92fe-c4fe7ec22384)
2025-08-04 08:11:30,400 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: daad5b5a-7dcc-4397-92fe-c4fe7ec22384) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:41:30.400504", "document_id": null, "uuid": "daad5b5a-7dcc-4397-92fe-c4fe7ec22384", "collection": "rbi_circular"}
2025-08-04 08:11:30,400 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:11:30,400 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:11:30,401 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:11:43,098 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:11:43,099 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:11:43,099 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:11:43,099 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:11:43,099 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=9b40fd7b-b6dd-59d5-b4ff-73a79efcc4d7, Document=RBI/2024-25/104 - No.Leg.BC.21/09.07.006/2015-16, Chunk=None
2025-08-04 08:11:43,099 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/104 - No.Leg.BC.21/09.07.006/2015-16 with text: 8000 chars
2025-08-04 08:11:45,895 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/104 - No.Leg.BC.21/09.07.006/2015-16 (ID: 9b40fd7b-b6dd-59d5-b4ff-73a79efcc4d7)
2025-08-04 08:11:45,895 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/104 - No.Leg.BC.21/09.07.006/2015-16 (ID: 9b40fd7b-b6dd-59d5-b4ff-73a79efcc4d7) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:41:45.895557", "document_id": "RBI/2024-25/104 - No.Leg.BC.21/09.07.006/2015-16", "uuid": "9b40fd7b-b6dd-59d5-b4ff-73a79efcc4d7", "collection": "rbi_other"}
2025-08-04 08:11:45,895 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:11:45,895 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:11:45,896 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:11:59,933 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:11:59,933 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:11:59,933 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:11:59,933 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:11:59,934 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:12:00,020 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:12:00,020 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c, Score=0.9869, Document=DBR.No.Leg.BC.78/09.07.005/2017-18
2025-08-04 08:12:00,020 - knowledge_base_executor - INFO - [QDRANT] Match: ID=cf214b82-7743-4bec-869b-256ad6b1bba9, Score=0.9869, Document=DBR.No.Leg.BC.78/09.07.005/2017-18
2025-08-04 08:12:00,020 - knowledge_base_executor - INFO - [QDRANT] Match: ID=fe594df3-1d72-4ca9-9efd-0b9d0e666759, Score=0.9863, Document=DBR.No.Leg.BC.78/09.07.005/2015-16
2025-08-04 08:12:00,020 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0549d859-1d9f-4ecd-a168-9a178f9c1748, Score=0.8894, Document=DBOD No.Leg.BC. 22 /09.07.006/2013-14
2025-08-04 08:12:00,020 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0a67d3d1-70f1-4f31-8422-f7828240e3b1, Score=0.8894, Document=DBOD No.Leg.BC. 22 /09.07.006/2013-14
2025-08-04 08:12:00,020 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:12:00,020 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:12:00,020 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DBR.No.Leg.BC.21/09.07.006/2015-16
2025-08-04 08:12:00,021 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '0549d859-1d9f-4ecd-a168-9a178f9c1748', '0a67d3d1-70f1-4f31-8422-f7828240e3b1']}
2025-08-04 08:12:00,021 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:12:00,021 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c
2025-08-04 08:12:00,021 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = cf214b82-7743-4bec-869b-256ad6b1bba9
2025-08-04 08:12:00,021 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fe594df3-1d72-4ca9-9efd-0b9d0e666759
2025-08-04 08:12:00,021 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759']...
2025-08-04 08:12:00,021 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:12:00,021 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '0549d859-1d9f-4ecd-a168-9a178f9c1748', '0a67d3d1-70f1-4f31-8422-f7828240e3b1'])] must_not=None, Payload={}
2025-08-04 08:12:00,023 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:12:00,023 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:12:00,024 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:12:00,024 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:12:00,024 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '0549d859-1d9f-4ecd-a168-9a178f9c1748', '0a67d3d1-70f1-4f31-8422-f7828240e3b1']}
2025-08-04 08:12:00,024 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:12:00,024 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c
2025-08-04 08:12:00,024 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = cf214b82-7743-4bec-869b-256ad6b1bba9
2025-08-04 08:12:00,024 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fe594df3-1d72-4ca9-9efd-0b9d0e666759
2025-08-04 08:12:00,024 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759']...
2025-08-04 08:12:00,024 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:12:00,024 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '0549d859-1d9f-4ecd-a168-9a178f9c1748', '0a67d3d1-70f1-4f31-8422-f7828240e3b1'])] must_not=None, Payload={}
2025-08-04 08:12:00,040 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 2 matching docs in rbi_master_circular
2025-08-04 08:12:00,058 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8287 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:12:00,058 - knowledge_base_executor - INFO - ✅ Successfully updated 2 documents in collection: rbi_master_circular
2025-08-04 08:12:00,058 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:12:00,058 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:12:00,058 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '0549d859-1d9f-4ecd-a168-9a178f9c1748', '0a67d3d1-70f1-4f31-8422-f7828240e3b1']}
2025-08-04 08:12:00,058 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:12:00,058 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c
2025-08-04 08:12:00,058 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = cf214b82-7743-4bec-869b-256ad6b1bba9
2025-08-04 08:12:00,058 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fe594df3-1d72-4ca9-9efd-0b9d0e666759
2025-08-04 08:12:00,058 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759']...
2025-08-04 08:12:00,058 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:12:00,058 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '0549d859-1d9f-4ecd-a168-9a178f9c1748', '0a67d3d1-70f1-4f31-8422-f7828240e3b1'])] must_not=None, Payload={}
2025-08-04 08:12:00,066 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 3 matching docs in rbi_circular
2025-08-04 08:12:00,079 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13031 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:12:00,079 - knowledge_base_executor - INFO - ✅ Successfully updated 3 documents in collection: rbi_circular
2025-08-04 08:12:00,079 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:42:00.079774"}
2025-08-04 08:12:00,079 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:12:00,079 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:12:00,080 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:12:00,081 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:12:00,081 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:12:00,081 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:12:00,081 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:12:00,081 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:12:00,098 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:12:00,098 - knowledge_base_executor - INFO - [QDRANT] Match: ID=fb5bf018-4e33-4adb-bc1d-704f39e21c39, Score=0.8130, Document=DCBR. CO. BPD.BC. No. 16/16.05.000/2015-16
2025-08-04 08:12:00,098 - knowledge_base_executor - INFO - [QDRANT] Match: ID=54c5602e-8d0c-4fae-90e4-9686d4e25ec1, Score=0.7138, Document=RPCD.CO.RRB. No. BC. 35 / 03.05.28(B)/2007-08
2025-08-04 08:12:00,098 - knowledge_base_executor - INFO - [QDRANT] Match: ID=6145d9f4-bcf7-41ef-aa0b-c2557b36b026, Score=0.7087, Document=RPCD.CO.RRB.BC.No.38 /03.05.28(B)/2009-10
2025-08-04 08:12:00,098 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0cb7fefb-5ba7-4b14-99e6-02c6109d43f5, Score=0.7058, Document=RPCD.CO.RRB. No. BC. 61/03.05.28(B)/2007-08
2025-08-04 08:12:00,099 - knowledge_base_executor - INFO - [QDRANT] Match: ID=4e513ba5-f8d8-45cc-b25e-4e2378df9c72, Score=0.7053, Document=RPCD.CO. RRB. No. BC. 32 /03.05.33(C) / 2007-08
2025-08-04 08:12:00,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:12:00,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:12:00,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DCBR.CO.BPD.(PCB).MC.No.15/12.05.001/2015-16
2025-08-04 08:12:00,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72']}
2025-08-04 08:12:00,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:12:00,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fb5bf018-4e33-4adb-bc1d-704f39e21c39
2025-08-04 08:12:00,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 54c5602e-8d0c-4fae-90e4-9686d4e25ec1
2025-08-04 08:12:00,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 6145d9f4-bcf7-41ef-aa0b-c2557b36b026
2025-08-04 08:12:00,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026']...
2025-08-04 08:12:00,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:12:00,099 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72'])] must_not=None, Payload={}
2025-08-04 08:12:00,102 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:12:00,102 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:12:00,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:12:00,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:12:00,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72']}
2025-08-04 08:12:00,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:12:00,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fb5bf018-4e33-4adb-bc1d-704f39e21c39
2025-08-04 08:12:00,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 54c5602e-8d0c-4fae-90e4-9686d4e25ec1
2025-08-04 08:12:00,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 6145d9f4-bcf7-41ef-aa0b-c2557b36b026
2025-08-04 08:12:00,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026']...
2025-08-04 08:12:00,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:12:00,102 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72'])] must_not=None, Payload={}
2025-08-04 08:12:00,104 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:12:00,104 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:12:00,104 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:12:00,104 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:12:00,104 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72']}
2025-08-04 08:12:00,104 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:12:00,104 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fb5bf018-4e33-4adb-bc1d-704f39e21c39
2025-08-04 08:12:00,104 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 54c5602e-8d0c-4fae-90e4-9686d4e25ec1
2025-08-04 08:12:00,104 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 6145d9f4-bcf7-41ef-aa0b-c2557b36b026
2025-08-04 08:12:00,104 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026']...
2025-08-04 08:12:00,104 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:12:00,104 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72'])] must_not=None, Payload={}
2025-08-04 08:12:00,112 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:12:00,129 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13032 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:12:00,129 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:12:00,129 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T02:42:00.129712"}
2025-08-04 08:12:00,129 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:12:00,129 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:12:00,130 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:12:00,131 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:12:00,131 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:12:00,131 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:12:00,131 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:12:00,131 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06da554e-d1df-4798-806d-64095c33026e, Score=0.7672, Document=RBI/DNBR/2016-17/42  Master Direction DNBR.PD.004/03.10.119/2016-17
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b, Score=0.7672, Document=RBI/DNBR/2016-17/42  Master Direction DNBR.PD.004/03.10.119/2016-17
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] Match: ID=070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92, Score=0.7672, Document=RBI/DNBR/2016-17/42  Master Direction DNBR.PD.004/03.10.119/2016-17
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] Match: ID=070d6bfc-e68f-478b-b770-e41eb204f88e, Score=0.7672, Document=RBI/DNBR/2016-17/42 Master Direction DNBR.PD.004/03.10.119/2016-17
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] Match: ID=07158b66-777a-4efa-84bc-b4f4d3e3b7ab, Score=0.7672, Document=RBI/DNBR/2016-17/42  Master Direction DNBR.PD.004/03.10.119/2016-17
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DNBR.PD.002/03.10.119/2016-17
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92', '070d6bfc-e68f-478b-b770-e41eb204f88e', '07158b66-777a-4efa-84bc-b4f4d3e3b7ab']}
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06da554e-d1df-4798-806d-64095c33026e
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92']...
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:12:00,143 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92', '070d6bfc-e68f-478b-b770-e41eb204f88e', '07158b66-777a-4efa-84bc-b4f4d3e3b7ab'])] must_not=None, Payload={}
2025-08-04 08:12:00,153 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-04 08:12:00,178 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3186 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:12:00,178 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-04 08:12:00,179 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:12:00,179 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:12:00,179 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92', '070d6bfc-e68f-478b-b770-e41eb204f88e', '07158b66-777a-4efa-84bc-b4f4d3e3b7ab']}
2025-08-04 08:12:00,179 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:12:00,179 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06da554e-d1df-4798-806d-64095c33026e
2025-08-04 08:12:00,179 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b
2025-08-04 08:12:00,179 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92
2025-08-04 08:12:00,179 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92']...
2025-08-04 08:12:00,179 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:12:00,179 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92', '070d6bfc-e68f-478b-b770-e41eb204f88e', '07158b66-777a-4efa-84bc-b4f4d3e3b7ab'])] must_not=None, Payload={}
2025-08-04 08:12:00,181 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:12:00,181 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:12:00,181 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:12:00,181 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:12:00,181 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92', '070d6bfc-e68f-478b-b770-e41eb204f88e', '07158b66-777a-4efa-84bc-b4f4d3e3b7ab']}
2025-08-04 08:12:00,181 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:12:00,181 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06da554e-d1df-4798-806d-64095c33026e
2025-08-04 08:12:00,181 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b
2025-08-04 08:12:00,181 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92
2025-08-04 08:12:00,181 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92']...
2025-08-04 08:12:00,181 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:12:00,181 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92', '070d6bfc-e68f-478b-b770-e41eb204f88e', '07158b66-777a-4efa-84bc-b4f4d3e3b7ab'])] must_not=None, Payload={}
2025-08-04 08:12:00,183 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:12:00,183 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:12:00,183 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T02:42:00.183220"}
2025-08-04 08:12:00,183 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:12:00,183 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:12:00,184 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:12:00,184 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:12:00,184 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:12:00,184 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:12:00,184 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:12:00,184 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=83918ad4-418a-46c7-bf0a-2bcfc0ed3b4f, Document=None, Chunk=0
2025-08-04 08:12:00,184 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 7884 chars
2025-08-04 08:12:04,365 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 83918ad4-418a-46c7-bf0a-2bcfc0ed3b4f)
2025-08-04 08:12:04,365 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 83918ad4-418a-46c7-bf0a-2bcfc0ed3b4f) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:42:04.365927", "document_id": null, "uuid": "83918ad4-418a-46c7-bf0a-2bcfc0ed3b4f", "collection": "rbi_circular"}
2025-08-04 08:12:04,365 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:12:04,366 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:12:04,366 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:12:07,754 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:12:07,754 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:12:07,754 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:12:07,754 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:12:07,755 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=dd6d3f1e-2e00-4de0-85ce-ccf03b58a072, Document=None, Chunk=1
2025-08-04 08:12:07,755 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 259 chars
2025-08-04 08:12:10,326 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: dd6d3f1e-2e00-4de0-85ce-ccf03b58a072)
2025-08-04 08:12:10,327 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: dd6d3f1e-2e00-4de0-85ce-ccf03b58a072) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:42:10.327052", "document_id": null, "uuid": "dd6d3f1e-2e00-4de0-85ce-ccf03b58a072", "collection": "rbi_circular"}
2025-08-04 08:12:10,327 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:12:10,327 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:12:10,327 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:12:22,878 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:12:22,879 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exim Bank_s GOI-supported Line of Credit _LOC_ for USD 700 million to the Govt. of Mongolia _GO-MNG__ for financing construction of Crude Oil Refinery Plant in Mongolia.pdf
2025-08-04 08:12:22,879 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exim Bank_s GOI-supported Line of Credit _LOC_ for USD 700 million to the Govt. of Mongolia _GO-MNG__ for financing construction of Crude Oil Refinery Plant in Mongolia.pdf
2025-08-04 08:12:22,879 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:12:22,879 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=b48df07b-26d1-52dd-bd5b-179d38de2967, Document=RBI/2025-2026/37 - A.P. (DIR Series) Circular No. 05, Chunk=None
2025-08-04 08:12:22,880 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-2026/37 - A.P. (DIR Series) Circular No. 05 with text: 8000 chars
2025-08-04 08:12:26,385 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-2026/37 - A.P. (DIR Series) Circular No. 05 (ID: b48df07b-26d1-52dd-bd5b-179d38de2967)
2025-08-04 08:12:26,385 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-2026/37 - A.P. (DIR Series) Circular No. 05 (ID: b48df07b-26d1-52dd-bd5b-179d38de2967) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:42:26.385426", "document_id": "RBI/2025-2026/37 - A.P. (DIR Series) Circular No. 05", "uuid": "b48df07b-26d1-52dd-bd5b-179d38de2967", "collection": "rbi_other"}
2025-08-04 08:12:26,385 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:12:26,385 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:12:26,386 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:12:36,007 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:12:36,007 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exim Bank_s GOI-supported Line of Credit _LOC_ for USD 700 million to the Govt. of Mongolia _GO-MNG__ for financing construction of Crude Oil Refinery Plant in Mongolia.pdf
2025-08-04 08:12:36,007 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exim Bank_s GOI-supported Line of Credit _LOC_ for USD 700 million to the Govt. of Mongolia _GO-MNG__ for financing construction of Crude Oil Refinery Plant in Mongolia.pdf
2025-08-04 08:12:36,007 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:12:36,007 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=2f6bdb7c-7f5f-514d-b0ed-f37ca0f5feff, Document=RBI/2025-2026/37, Chunk=0
2025-08-04 08:12:36,007 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-2026/37 with text: 8000 chars
2025-08-04 08:12:39,330 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-2026/37 (ID: 2f6bdb7c-7f5f-514d-b0ed-f37ca0f5feff)
2025-08-04 08:12:39,330 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-2026/37 (ID: 2f6bdb7c-7f5f-514d-b0ed-f37ca0f5feff) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:42:39.330717", "document_id": "RBI/2025-2026/37", "uuid": "2f6bdb7c-7f5f-514d-b0ed-f37ca0f5feff", "collection": "rbi_master_direction"}
2025-08-04 08:12:39,330 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:12:39,330 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:12:39,331 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:12:39,331 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:12:39,331 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exim Bank_s GOI-supported Line of Credit _LOC_ for USD 700 million to the Govt. of Mongolia _GO-MNG__ for financing construction of Crude Oil Refinery Plant in Mongolia.pdf
2025-08-04 08:12:39,331 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exim Bank_s GOI-supported Line of Credit _LOC_ for USD 700 million to the Govt. of Mongolia _GO-MNG__ for financing construction of Crude Oil Refinery Plant in Mongolia.pdf
2025-08-04 08:12:39,331 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:12:39,331 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=acb122e6-f957-4252-9b22-30c111c62c40, Document=None, Chunk=0
2025-08-04 08:12:39,332 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-04 08:12:42,684 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: acb122e6-f957-4252-9b22-30c111c62c40)
2025-08-04 08:12:42,684 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: acb122e6-f957-4252-9b22-30c111c62c40) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:42:42.684534", "document_id": null, "uuid": "acb122e6-f957-4252-9b22-30c111c62c40", "collection": "rbi_circular"}
2025-08-04 08:12:42,684 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:12:42,684 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:12:42,685 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:12:52,904 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:12:52,905 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Dispensation of _100 and _200 denomination banknotes through ATMs.pdf
2025-08-04 08:12:52,905 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Dispensation of _100 and _200 denomination banknotes through ATMs.pdf
2025-08-04 08:12:52,905 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:12:52,905 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=ff9671f7-6112-5577-8b3d-0cad88945b73, Document=RBI/2025-26/33, Chunk=None
2025-08-04 08:12:52,906 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/33 with text: 8000 chars
2025-08-04 08:12:55,939 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/33 (ID: ff9671f7-6112-5577-8b3d-0cad88945b73)
2025-08-04 08:12:55,939 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/33 (ID: ff9671f7-6112-5577-8b3d-0cad88945b73) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:42:55.939723", "document_id": "RBI/2025-26/33", "uuid": "ff9671f7-6112-5577-8b3d-0cad88945b73", "collection": "rbi_other"}
2025-08-04 08:12:55,939 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:12:55,939 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:12:55,940 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:13:02,438 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:13:02,439 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Dispensation of _100 and _200 denomination banknotes through ATMs.pdf
2025-08-04 08:13:02,439 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Dispensation of _100 and _200 denomination banknotes through ATMs.pdf
2025-08-04 08:13:02,439 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:13:02,439 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=4b29ff27-075f-588d-82b6-6e77fab0dd62, Document=RBI/2025-26/33, Chunk=0
2025-08-04 08:13:02,439 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/33 with text: 5623 chars
2025-08-04 08:13:05,762 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/33 (ID: 4b29ff27-075f-588d-82b6-6e77fab0dd62)
2025-08-04 08:13:05,762 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/33 (ID: 4b29ff27-075f-588d-82b6-6e77fab0dd62) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:43:05.762743", "document_id": "RBI/2025-26/33", "uuid": "4b29ff27-075f-588d-82b6-6e77fab0dd62", "collection": "rbi_master_direction"}
2025-08-04 08:13:05,762 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:13:05,762 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:13:05,763 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:13:05,763 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:13:05,763 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Dispensation of _100 and _200 denomination banknotes through ATMs.pdf
2025-08-04 08:13:05,763 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Dispensation of _100 and _200 denomination banknotes through ATMs.pdf
2025-08-04 08:13:05,763 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:13:05,763 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=2c4f2299-9abb-4938-8359-b58b2a4bb490, Document=None, Chunk=0
2025-08-04 08:13:05,763 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 5529 chars
2025-08-04 08:13:08,437 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 2c4f2299-9abb-4938-8359-b58b2a4bb490)
2025-08-04 08:13:08,437 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 2c4f2299-9abb-4938-8359-b58b2a4bb490) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:43:08.437920", "document_id": null, "uuid": "2c4f2299-9abb-4938-8359-b58b2a4bb490", "collection": "rbi_circular"}
2025-08-04 08:13:08,437 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:13:08,438 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:13:08,438 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:13:21,998 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:13:22,000 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:13:22,000 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:13:22,000 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:13:22,000 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=f0ddd7e1-d328-5f83-827f-0f4e594eeb1b, Document=RBI/2025-26/27 - DOR.LRG.REC.18/03.10.001/2025-26, Chunk=None
2025-08-04 08:13:22,001 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/27 - DOR.LRG.REC.18/03.10.001/2025-26 with text: 8000 chars
2025-08-04 08:13:26,887 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/27 - DOR.LRG.REC.18/03.10.001/2025-26 (ID: f0ddd7e1-d328-5f83-827f-0f4e594eeb1b)
2025-08-04 08:13:26,887 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/27 - DOR.LRG.REC.18/03.10.001/2025-26 (ID: f0ddd7e1-d328-5f83-827f-0f4e594eeb1b) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:43:26.887803", "document_id": "RBI/2025-26/27 - DOR.LRG.REC.18/03.10.001/2025-26", "uuid": "f0ddd7e1-d328-5f83-827f-0f4e594eeb1b", "collection": "rbi_other"}
2025-08-04 08:13:26,887 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:13:26,887 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:13:26,888 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:13:39,651 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:13:39,651 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:13:39,651 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:13:39,651 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:13:39,651 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] Match: ID=d2b6bf29-995b-45c5-8f5e-d8363f6af517, Score=0.9729, Document=DBOD.BP.BC.No.49/21.04.018/2013-14
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] Match: ID=dbfe29c4-0da0-48a1-85de-4f51cf3680d2, Score=0.9729, Document=DBOD.BP.BC.No.49/21.04.018/2013-14
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] Match: ID=cfbacc74-5540-44bf-8df1-4943955c52f7, Score=0.9670, Document=DBOD.BP.BC.No.98 / 21.04.132 / 2013-14
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] Match: ID=49b6247d-9e47-4fa9-b763-2cb1ecf8b669, Score=0.9670, Document=DBOD.BP.BC.No.98 / 21.04.132 / 2013-14
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] Match: ID=933f4f32-dff9-4df6-9430-19088abd25e2, Score=0.9655, Document=DBOD. No.BP.BC.121/21.04.018/2013-14
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DBOD.BP.BC.No.120/21.04.098/2013-14
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7', '49b6247d-9e47-4fa9-b763-2cb1ecf8b669', '933f4f32-dff9-4df6-9430-19088abd25e2']}
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = d2b6bf29-995b-45c5-8f5e-d8363f6af517
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = dbfe29c4-0da0-48a1-85de-4f51cf3680d2
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = cfbacc74-5540-44bf-8df1-4943955c52f7
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7']...
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:13:39,720 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7', '49b6247d-9e47-4fa9-b763-2cb1ecf8b669', '933f4f32-dff9-4df6-9430-19088abd25e2'])] must_not=None, Payload={}
2025-08-04 08:13:39,723 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:13:39,723 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:13:39,723 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:13:39,723 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:13:39,723 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7', '49b6247d-9e47-4fa9-b763-2cb1ecf8b669', '933f4f32-dff9-4df6-9430-19088abd25e2']}
2025-08-04 08:13:39,723 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:13:39,723 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = d2b6bf29-995b-45c5-8f5e-d8363f6af517
2025-08-04 08:13:39,723 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = dbfe29c4-0da0-48a1-85de-4f51cf3680d2
2025-08-04 08:13:39,723 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = cfbacc74-5540-44bf-8df1-4943955c52f7
2025-08-04 08:13:39,723 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7']...
2025-08-04 08:13:39,723 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:13:39,723 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7', '49b6247d-9e47-4fa9-b763-2cb1ecf8b669', '933f4f32-dff9-4df6-9430-19088abd25e2'])] must_not=None, Payload={}
2025-08-04 08:13:39,726 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:13:39,726 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:13:39,726 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:13:39,726 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:13:39,726 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7', '49b6247d-9e47-4fa9-b763-2cb1ecf8b669', '933f4f32-dff9-4df6-9430-19088abd25e2']}
2025-08-04 08:13:39,726 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:13:39,726 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = d2b6bf29-995b-45c5-8f5e-d8363f6af517
2025-08-04 08:13:39,726 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = dbfe29c4-0da0-48a1-85de-4f51cf3680d2
2025-08-04 08:13:39,726 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = cfbacc74-5540-44bf-8df1-4943955c52f7
2025-08-04 08:13:39,726 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7']...
2025-08-04 08:13:39,726 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:13:39,726 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7', '49b6247d-9e47-4fa9-b763-2cb1ecf8b669', '933f4f32-dff9-4df6-9430-19088abd25e2'])] must_not=None, Payload={}
2025-08-04 08:13:39,747 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:13:39,769 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13037 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:13:39,769 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:13:39,770 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T02:43:39.769950"}
2025-08-04 08:13:39,770 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:13:39,770 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:13:39,770 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:13:39,771 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:13:39,771 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:13:39,771 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:13:39,771 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:13:39,771 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] Match: ID=c43d28fb-0d2b-430c-b74b-60aa4ad89746, Score=0.7714, Document=FMD.MOAG No. 77 /01.01.001/2012-13
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9100b6e0-bf9a-4a15-b597-1d3e6080e99c, Score=0.7714, Document=FMD.MOAG No. 77 /01.01.001/2012-13
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] Match: ID=afb3640c-5046-468a-a13c-47ea9f1c0ee4, Score=0.7409, Document=FMD. MSRG. No. 16/02.02.001/2007-08
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b1b34f65-013c-404e-a219-9bdb6ec6944b, Score=0.7409, Document=FMD. MSRG. No. 16/02.02.001/2007-08
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] Match: ID=79213005-04f6-49a6-a5a3-85acbfed61d9, Score=0.7409, Document=FMD. MSRG. No. 16/02.02.001/2007-08
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: FMOD.MAOG.No.125/01.01.001/2017-18
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['c43d28fb-0d2b-430c-b74b-60aa4ad89746', '9100b6e0-bf9a-4a15-b597-1d3e6080e99c', 'afb3640c-5046-468a-a13c-47ea9f1c0ee4', 'b1b34f65-013c-404e-a219-9bdb6ec6944b', '79213005-04f6-49a6-a5a3-85acbfed61d9']}
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c43d28fb-0d2b-430c-b74b-60aa4ad89746
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9100b6e0-bf9a-4a15-b597-1d3e6080e99c
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = afb3640c-5046-468a-a13c-47ea9f1c0ee4
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['c43d28fb-0d2b-430c-b74b-60aa4ad89746', '9100b6e0-bf9a-4a15-b597-1d3e6080e99c', 'afb3640c-5046-468a-a13c-47ea9f1c0ee4']...
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:13:39,785 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['c43d28fb-0d2b-430c-b74b-60aa4ad89746', '9100b6e0-bf9a-4a15-b597-1d3e6080e99c', 'afb3640c-5046-468a-a13c-47ea9f1c0ee4', 'b1b34f65-013c-404e-a219-9bdb6ec6944b', '79213005-04f6-49a6-a5a3-85acbfed61d9'])] must_not=None, Payload={}
2025-08-04 08:13:39,787 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:13:39,787 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:13:39,787 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:13:39,787 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:13:39,787 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['c43d28fb-0d2b-430c-b74b-60aa4ad89746', '9100b6e0-bf9a-4a15-b597-1d3e6080e99c', 'afb3640c-5046-468a-a13c-47ea9f1c0ee4', 'b1b34f65-013c-404e-a219-9bdb6ec6944b', '79213005-04f6-49a6-a5a3-85acbfed61d9']}
2025-08-04 08:13:39,788 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:13:39,788 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c43d28fb-0d2b-430c-b74b-60aa4ad89746
2025-08-04 08:13:39,788 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9100b6e0-bf9a-4a15-b597-1d3e6080e99c
2025-08-04 08:13:39,788 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = afb3640c-5046-468a-a13c-47ea9f1c0ee4
2025-08-04 08:13:39,788 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['c43d28fb-0d2b-430c-b74b-60aa4ad89746', '9100b6e0-bf9a-4a15-b597-1d3e6080e99c', 'afb3640c-5046-468a-a13c-47ea9f1c0ee4']...
2025-08-04 08:13:39,788 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:13:39,788 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['c43d28fb-0d2b-430c-b74b-60aa4ad89746', '9100b6e0-bf9a-4a15-b597-1d3e6080e99c', 'afb3640c-5046-468a-a13c-47ea9f1c0ee4', 'b1b34f65-013c-404e-a219-9bdb6ec6944b', '79213005-04f6-49a6-a5a3-85acbfed61d9'])] must_not=None, Payload={}
2025-08-04 08:13:39,795 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 3 matching docs in rbi_master_circular
2025-08-04 08:13:39,808 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8288 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:13:39,808 - knowledge_base_executor - INFO - ✅ Successfully updated 3 documents in collection: rbi_master_circular
2025-08-04 08:13:39,808 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:13:39,808 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:13:39,808 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['c43d28fb-0d2b-430c-b74b-60aa4ad89746', '9100b6e0-bf9a-4a15-b597-1d3e6080e99c', 'afb3640c-5046-468a-a13c-47ea9f1c0ee4', 'b1b34f65-013c-404e-a219-9bdb6ec6944b', '79213005-04f6-49a6-a5a3-85acbfed61d9']}
2025-08-04 08:13:39,808 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:13:39,808 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c43d28fb-0d2b-430c-b74b-60aa4ad89746
2025-08-04 08:13:39,808 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9100b6e0-bf9a-4a15-b597-1d3e6080e99c
2025-08-04 08:13:39,808 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = afb3640c-5046-468a-a13c-47ea9f1c0ee4
2025-08-04 08:13:39,808 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['c43d28fb-0d2b-430c-b74b-60aa4ad89746', '9100b6e0-bf9a-4a15-b597-1d3e6080e99c', 'afb3640c-5046-468a-a13c-47ea9f1c0ee4']...
2025-08-04 08:13:39,808 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:13:39,809 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['c43d28fb-0d2b-430c-b74b-60aa4ad89746', '9100b6e0-bf9a-4a15-b597-1d3e6080e99c', 'afb3640c-5046-468a-a13c-47ea9f1c0ee4', 'b1b34f65-013c-404e-a219-9bdb6ec6944b', '79213005-04f6-49a6-a5a3-85acbfed61d9'])] must_not=None, Payload={}
2025-08-04 08:13:39,815 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 2 matching docs in rbi_circular
2025-08-04 08:13:39,824 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13038 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:13:39,824 - knowledge_base_executor - INFO - ✅ Successfully updated 2 documents in collection: rbi_circular
2025-08-04 08:13:39,824 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:43:39.824613"}
2025-08-04 08:13:39,824 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:13:39,824 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:13:39,825 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:13:39,826 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:13:39,826 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:13:39,826 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:13:39,826 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:13:39,826 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=d6ca0f63-08fb-4fdb-99f3-bdf2c79ede6f, Document=None, Chunk=0
2025-08-04 08:13:39,826 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 7963 chars
2025-08-04 08:13:42,970 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: d6ca0f63-08fb-4fdb-99f3-bdf2c79ede6f)
2025-08-04 08:13:42,970 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: d6ca0f63-08fb-4fdb-99f3-bdf2c79ede6f) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:43:42.970278", "document_id": null, "uuid": "d6ca0f63-08fb-4fdb-99f3-bdf2c79ede6f", "collection": "rbi_circular"}
2025-08-04 08:13:42,970 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:13:42,970 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:13:42,970 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:13:51,219 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:13:51,219 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:13:51,219 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:13:51,219 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:13:51,219 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:13:51,285 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:13:51,285 - knowledge_base_executor - INFO - [QDRANT] Match: ID=8e6d907a-b693-45a0-b2d9-1f660175239d, Score=1.0000, Document=DBR.BP.BC.No.86/21.04.098/2015-16
2025-08-04 08:13:51,285 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87, Score=0.9759, Document=DBR.BP.BC.No.08/21.04.098/2018-19
2025-08-04 08:13:51,285 - knowledge_base_executor - INFO - [QDRANT] Match: ID=7b2fef0c-fe1a-439c-b47a-337b826dfd03, Score=0.9759, Document=DBR.BP.BC.No.08/21.04.098/2018-19
2025-08-04 08:13:51,286 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b2b924ef-1354-4cb9-99f1-dd60059d29cb, Score=0.9649, Document=DBR.BP.BC.No.46/21.04.098/2014-15
2025-08-04 08:13:51,286 - knowledge_base_executor - INFO - [QDRANT] Match: ID=e9cef7a1-91cd-4046-8cc2-9240daf5d554, Score=0.9649, Document=DBR.BP.BC.No.46/21.04.098/2014-15
2025-08-04 08:13:51,286 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:13:51,286 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:13:51,286 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DBR.BP.BC.No.86/21.04.098/2015-16
2025-08-04 08:13:51,286 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554']}
2025-08-04 08:13:51,286 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:13:51,286 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8e6d907a-b693-45a0-b2d9-1f660175239d
2025-08-04 08:13:51,286 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87
2025-08-04 08:13:51,286 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7b2fef0c-fe1a-439c-b47a-337b826dfd03
2025-08-04 08:13:51,286 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03']...
2025-08-04 08:13:51,286 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:13:51,286 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554'])] must_not=None, Payload={}
2025-08-04 08:13:51,289 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:13:51,289 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:13:51,289 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:13:51,289 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:13:51,289 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554']}
2025-08-04 08:13:51,289 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:13:51,289 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8e6d907a-b693-45a0-b2d9-1f660175239d
2025-08-04 08:13:51,289 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87
2025-08-04 08:13:51,289 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7b2fef0c-fe1a-439c-b47a-337b826dfd03
2025-08-04 08:13:51,289 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03']...
2025-08-04 08:13:51,289 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:13:51,289 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554'])] must_not=None, Payload={}
2025-08-04 08:13:51,292 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:13:51,292 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:13:51,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:13:51,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:13:51,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554']}
2025-08-04 08:13:51,293 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:13:51,293 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8e6d907a-b693-45a0-b2d9-1f660175239d
2025-08-04 08:13:51,293 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87
2025-08-04 08:13:51,293 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7b2fef0c-fe1a-439c-b47a-337b826dfd03
2025-08-04 08:13:51,293 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03']...
2025-08-04 08:13:51,293 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:13:51,293 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554'])] must_not=None, Payload={}
2025-08-04 08:13:51,310 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:13:51,331 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13040 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:13:51,331 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:13:51,331 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T02:43:51.331531"}
2025-08-04 08:13:51,331 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:13:51,332 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:13:51,332 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:13:51,333 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:13:51,333 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:13:51,333 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:13:51,333 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:13:51,333 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=1c5c64c9-9582-494c-b7f1-0b18e96795ab, Document=None, Chunk=1
2025-08-04 08:13:51,333 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 7709 chars
2025-08-04 08:13:56,026 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 1c5c64c9-9582-494c-b7f1-0b18e96795ab)
2025-08-04 08:13:56,026 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 1c5c64c9-9582-494c-b7f1-0b18e96795ab) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:43:56.026443", "document_id": null, "uuid": "1c5c64c9-9582-494c-b7f1-0b18e96795ab", "collection": "rbi_circular"}
2025-08-04 08:13:56,026 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:13:56,026 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:13:56,027 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:14:06,476 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:14:06,477 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:06,477 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:06,477 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:14:06,477 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:14:06,528 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:14:06,528 - knowledge_base_executor - INFO - [QDRANT] Match: ID=8e6d907a-b693-45a0-b2d9-1f660175239d, Score=1.0000, Document=DBR.BP.BC.No.86/21.04.098/2015-16
2025-08-04 08:14:06,528 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87, Score=0.9759, Document=DBR.BP.BC.No.08/21.04.098/2018-19
2025-08-04 08:14:06,528 - knowledge_base_executor - INFO - [QDRANT] Match: ID=7b2fef0c-fe1a-439c-b47a-337b826dfd03, Score=0.9759, Document=DBR.BP.BC.No.08/21.04.098/2018-19
2025-08-04 08:14:06,528 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b2b924ef-1354-4cb9-99f1-dd60059d29cb, Score=0.9649, Document=DBR.BP.BC.No.46/21.04.098/2014-15
2025-08-04 08:14:06,528 - knowledge_base_executor - INFO - [QDRANT] Match: ID=e9cef7a1-91cd-4046-8cc2-9240daf5d554, Score=0.9649, Document=DBR.BP.BC.No.46/21.04.098/2014-15
2025-08-04 08:14:06,528 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:14:06,528 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:14:06,528 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DBR.BP.BC.No.86/21.04.098/2015-16
2025-08-04 08:14:06,528 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554']}
2025-08-04 08:14:06,529 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:14:06,529 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8e6d907a-b693-45a0-b2d9-1f660175239d
2025-08-04 08:14:06,529 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87
2025-08-04 08:14:06,529 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7b2fef0c-fe1a-439c-b47a-337b826dfd03
2025-08-04 08:14:06,529 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03']...
2025-08-04 08:14:06,529 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:14:06,529 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554'])] must_not=None, Payload={}
2025-08-04 08:14:06,532 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:14:06,532 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:14:06,532 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:14:06,532 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:14:06,532 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554']}
2025-08-04 08:14:06,532 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:14:06,532 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8e6d907a-b693-45a0-b2d9-1f660175239d
2025-08-04 08:14:06,533 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87
2025-08-04 08:14:06,533 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7b2fef0c-fe1a-439c-b47a-337b826dfd03
2025-08-04 08:14:06,533 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03']...
2025-08-04 08:14:06,533 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:14:06,533 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554'])] must_not=None, Payload={}
2025-08-04 08:14:06,536 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:14:06,536 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:14:06,536 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:14:06,536 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:14:06,536 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554']}
2025-08-04 08:14:06,536 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:14:06,536 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8e6d907a-b693-45a0-b2d9-1f660175239d
2025-08-04 08:14:06,536 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87
2025-08-04 08:14:06,536 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7b2fef0c-fe1a-439c-b47a-337b826dfd03
2025-08-04 08:14:06,536 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03']...
2025-08-04 08:14:06,536 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:14:06,536 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554'])] must_not=None, Payload={}
2025-08-04 08:14:06,550 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:14:06,574 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13042 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:14:06,574 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:14:06,574 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T02:44:06.574354"}
2025-08-04 08:14:06,574 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:14:06,574 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:14:06,575 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:14:06,575 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_TEMPORARY_NOTE
2025-08-04 08:14:06,575 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:06,575 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:06,575 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_TEMPORARY_NOTE
2025-08-04 08:14:06,575 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for ADD_TEMPORARY_NOTE
2025-08-04 08:14:06,575 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name']
2025-08-04 08:14:06,576 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DOR.CAP.REC.2/21.06.201/2025-26
2025-08-04 08:14:06,576 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: No filter_fields provided
2025-08-04 08:14:06,576 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding metadata filter: metadata.document_id = DOR.CAP.REC.2/21.06.201/2025-26
2025-08-04 08:14:06,576 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Created filter with 2 conditions
2025-08-04 08:14:06,576 - knowledge_base_executor - INFO - [QDRANT] NOTE: Adding note to rbi_master_direction, Data: {'original_note_id': 'note_1754255646', 'in_reply_to': {'metadata.document_id': 'DOR.CAP.REC.2/21.06.201/2025-26'}, 'content': None, 'created_at': '2025-08-04T02:44:06.576236'}
2025-08-04 08:14:06,577 - knowledge_base_executor - ERROR - [QDRANT] NOTE error in rbi_master_direction: 1 validation error for PointStruct
vector
  Field required [type=missing, input_value={'id': '12569923-d460-530...08-04T02:44:06.576236'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-04 08:14:06,577 - knowledge_base_executor - INFO - [QDRANT] NOTE: Adding note to rbi_master_circular, Data: {'original_note_id': 'note_1754255646', 'in_reply_to': {'metadata.document_id': 'DOR.CAP.REC.2/21.06.201/2025-26'}, 'content': None, 'created_at': '2025-08-04T02:44:06.576236'}
2025-08-04 08:14:06,577 - knowledge_base_executor - ERROR - [QDRANT] NOTE error in rbi_master_circular: 1 validation error for PointStruct
vector
  Field required [type=missing, input_value={'id': '12569923-d460-530...08-04T02:44:06.576236'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-04 08:14:06,577 - knowledge_base_executor - INFO - [QDRANT] NOTE: Adding note to rbi_circular, Data: {'original_note_id': 'note_1754255646', 'in_reply_to': {'metadata.document_id': 'DOR.CAP.REC.2/21.06.201/2025-26'}, 'content': None, 'created_at': '2025-08-04T02:44:06.576236'}
2025-08-04 08:14:06,577 - knowledge_base_executor - ERROR - [QDRANT] NOTE error in rbi_circular: 1 validation error for PointStruct
vector
  Field required [type=missing, input_value={'id': '12569923-d460-530...08-04T02:44:06.576236'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-04 08:14:06,577 - knowledge_base_executor - INFO - [QDRANT] Action ADD_TEMPORARY_NOTE executed with result: {"action": "ADD_TEMPORARY_NOTE", "success": true, "details": "Note note_1754255646 (ID: 12569923-d460-5309-9309-564a5d339fe8) added to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:44:06.577394"}
2025-08-04 08:14:06,577 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:14:06,577 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_TEMPORARY_NOTE - ✅ SUCCESS
2025-08-04 08:14:06,578 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:14:06,578 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_TEMPORARY_NOTE
2025-08-04 08:14:06,578 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:06,578 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:06,578 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_TEMPORARY_NOTE
2025-08-04 08:14:06,578 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for ADD_TEMPORARY_NOTE
2025-08-04 08:14:06,578 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name']
2025-08-04 08:14:06,578 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DBR.FSD.No.101/24.01.041/2015-16
2025-08-04 08:14:06,579 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: No filter_fields provided
2025-08-04 08:14:06,579 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding metadata filter: metadata.document_id = DBR.FSD.No.101/24.01.041/2015-16
2025-08-04 08:14:06,579 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Created filter with 2 conditions
2025-08-04 08:14:06,579 - knowledge_base_executor - INFO - [QDRANT] NOTE: Adding note to rbi_master_direction, Data: {'original_note_id': 'note_1754255646', 'in_reply_to': {'metadata.document_id': 'DBR.FSD.No.101/24.01.041/2015-16'}, 'content': None, 'created_at': '2025-08-04T02:44:06.579132'}
2025-08-04 08:14:06,579 - knowledge_base_executor - ERROR - [QDRANT] NOTE error in rbi_master_direction: 1 validation error for PointStruct
vector
  Field required [type=missing, input_value={'id': '94519336-4f0d-501...08-04T02:44:06.579132'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-04 08:14:06,579 - knowledge_base_executor - INFO - [QDRANT] NOTE: Adding note to rbi_master_circular, Data: {'original_note_id': 'note_1754255646', 'in_reply_to': {'metadata.document_id': 'DBR.FSD.No.101/24.01.041/2015-16'}, 'content': None, 'created_at': '2025-08-04T02:44:06.579132'}
2025-08-04 08:14:06,579 - knowledge_base_executor - ERROR - [QDRANT] NOTE error in rbi_master_circular: 1 validation error for PointStruct
vector
  Field required [type=missing, input_value={'id': '94519336-4f0d-501...08-04T02:44:06.579132'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-04 08:14:06,579 - knowledge_base_executor - INFO - [QDRANT] NOTE: Adding note to rbi_circular, Data: {'original_note_id': 'note_1754255646', 'in_reply_to': {'metadata.document_id': 'DBR.FSD.No.101/24.01.041/2015-16'}, 'content': None, 'created_at': '2025-08-04T02:44:06.579132'}
2025-08-04 08:14:06,579 - knowledge_base_executor - ERROR - [QDRANT] NOTE error in rbi_circular: 1 validation error for PointStruct
vector
  Field required [type=missing, input_value={'id': '94519336-4f0d-501...08-04T02:44:06.579132'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-04 08:14:06,579 - knowledge_base_executor - INFO - [QDRANT] Action ADD_TEMPORARY_NOTE executed with result: {"action": "ADD_TEMPORARY_NOTE", "success": true, "details": "Note note_1754255646 (ID: 94519336-4f0d-501d-adef-c39d00935c81) added to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:44:06.579449"}
2025-08-04 08:14:06,579 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:14:06,579 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_TEMPORARY_NOTE - ✅ SUCCESS
2025-08-04 08:14:06,580 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:14:06,580 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:14:06,580 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:06,580 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:06,580 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:14:06,580 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=6a75e471-021e-4072-af80-1fbc6f52d84d, Document=None, Chunk=2
2025-08-04 08:14:06,580 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 3573 chars
2025-08-04 08:14:09,728 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 6a75e471-021e-4072-af80-1fbc6f52d84d)
2025-08-04 08:14:09,728 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 6a75e471-021e-4072-af80-1fbc6f52d84d) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:44:09.728569", "document_id": null, "uuid": "6a75e471-021e-4072-af80-1fbc6f52d84d", "collection": "rbi_circular"}
2025-08-04 08:14:09,728 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:14:09,728 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:14:09,729 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:14:27,473 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:14:27,474 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:27,474 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:27,474 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:14:27,474 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] Match: ID=d2b6bf29-995b-45c5-8f5e-d8363f6af517, Score=0.9729, Document=DBOD.BP.BC.No.49/21.04.018/2013-14
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] Match: ID=dbfe29c4-0da0-48a1-85de-4f51cf3680d2, Score=0.9729, Document=DBOD.BP.BC.No.49/21.04.018/2013-14
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] Match: ID=cfbacc74-5540-44bf-8df1-4943955c52f7, Score=0.9670, Document=DBOD.BP.BC.No.98 / 21.04.132 / 2013-14
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] Match: ID=49b6247d-9e47-4fa9-b763-2cb1ecf8b669, Score=0.9670, Document=DBOD.BP.BC.No.98 / 21.04.132 / 2013-14
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] Match: ID=933f4f32-dff9-4df6-9430-19088abd25e2, Score=0.9655, Document=DBOD. No.BP.BC.121/21.04.018/2013-14
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DBOD.BP.BC.No.120/21.04.098/2013-14
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7', '49b6247d-9e47-4fa9-b763-2cb1ecf8b669', '933f4f32-dff9-4df6-9430-19088abd25e2']}
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = d2b6bf29-995b-45c5-8f5e-d8363f6af517
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = dbfe29c4-0da0-48a1-85de-4f51cf3680d2
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = cfbacc74-5540-44bf-8df1-4943955c52f7
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7']...
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:14:27,556 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7', '49b6247d-9e47-4fa9-b763-2cb1ecf8b669', '933f4f32-dff9-4df6-9430-19088abd25e2'])] must_not=None, Payload={}
2025-08-04 08:14:27,559 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:14:27,559 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:14:27,559 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:14:27,559 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:14:27,559 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7', '49b6247d-9e47-4fa9-b763-2cb1ecf8b669', '933f4f32-dff9-4df6-9430-19088abd25e2']}
2025-08-04 08:14:27,559 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:14:27,560 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = d2b6bf29-995b-45c5-8f5e-d8363f6af517
2025-08-04 08:14:27,560 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = dbfe29c4-0da0-48a1-85de-4f51cf3680d2
2025-08-04 08:14:27,560 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = cfbacc74-5540-44bf-8df1-4943955c52f7
2025-08-04 08:14:27,560 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7']...
2025-08-04 08:14:27,560 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:14:27,560 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7', '49b6247d-9e47-4fa9-b763-2cb1ecf8b669', '933f4f32-dff9-4df6-9430-19088abd25e2'])] must_not=None, Payload={}
2025-08-04 08:14:27,563 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:14:27,563 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:14:27,563 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:14:27,563 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:14:27,563 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7', '49b6247d-9e47-4fa9-b763-2cb1ecf8b669', '933f4f32-dff9-4df6-9430-19088abd25e2']}
2025-08-04 08:14:27,564 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:14:27,564 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = d2b6bf29-995b-45c5-8f5e-d8363f6af517
2025-08-04 08:14:27,564 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = dbfe29c4-0da0-48a1-85de-4f51cf3680d2
2025-08-04 08:14:27,564 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = cfbacc74-5540-44bf-8df1-4943955c52f7
2025-08-04 08:14:27,564 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7']...
2025-08-04 08:14:27,564 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:14:27,564 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['d2b6bf29-995b-45c5-8f5e-d8363f6af517', 'dbfe29c4-0da0-48a1-85de-4f51cf3680d2', 'cfbacc74-5540-44bf-8df1-4943955c52f7', '49b6247d-9e47-4fa9-b763-2cb1ecf8b669', '933f4f32-dff9-4df6-9430-19088abd25e2'])] must_not=None, Payload={}
2025-08-04 08:14:27,577 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:14:27,599 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13044 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:14:27,599 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:14:27,599 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T02:44:27.599148"}
2025-08-04 08:14:27,599 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:14:27,599 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:14:27,600 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:14:27,600 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_TEMPORARY_NOTE
2025-08-04 08:14:27,600 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:27,600 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:27,600 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_TEMPORARY_NOTE
2025-08-04 08:14:27,600 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for ADD_TEMPORARY_NOTE
2025-08-04 08:14:27,600 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name']
2025-08-04 08:14:27,600 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'FMOD.MAOG No.125/01.01.001/2017-18' → 'FMOD.MAOGNo.125/01.01.001/2017-18'
2025-08-04 08:14:27,600 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: FMOD.MAOGNo.125/01.01.001/2017-18
2025-08-04 08:14:27,600 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: No filter_fields provided
2025-08-04 08:14:27,600 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding metadata filter: metadata.document_id = FMOD.MAOGNo.125/01.01.001/2017-18
2025-08-04 08:14:27,601 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Created filter with 2 conditions
2025-08-04 08:14:27,601 - knowledge_base_executor - INFO - [QDRANT] NOTE: Adding note to rbi_master_direction, Data: {'original_note_id': 'note_1754255667', 'in_reply_to': {'metadata.document_id': 'FMOD.MAOGNo.125/01.01.001/2017-18'}, 'content': None, 'created_at': '2025-08-04T02:44:27.601060'}
2025-08-04 08:14:27,601 - knowledge_base_executor - ERROR - [QDRANT] NOTE error in rbi_master_direction: 1 validation error for PointStruct
vector
  Field required [type=missing, input_value={'id': '64a6c9ed-1ef2-5e9...08-04T02:44:27.601060'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-04 08:14:27,601 - knowledge_base_executor - INFO - [QDRANT] NOTE: Adding note to rbi_master_circular, Data: {'original_note_id': 'note_1754255667', 'in_reply_to': {'metadata.document_id': 'FMOD.MAOGNo.125/01.01.001/2017-18'}, 'content': None, 'created_at': '2025-08-04T02:44:27.601060'}
2025-08-04 08:14:27,601 - knowledge_base_executor - ERROR - [QDRANT] NOTE error in rbi_master_circular: 1 validation error for PointStruct
vector
  Field required [type=missing, input_value={'id': '64a6c9ed-1ef2-5e9...08-04T02:44:27.601060'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-04 08:14:27,601 - knowledge_base_executor - INFO - [QDRANT] NOTE: Adding note to rbi_circular, Data: {'original_note_id': 'note_1754255667', 'in_reply_to': {'metadata.document_id': 'FMOD.MAOGNo.125/01.01.001/2017-18'}, 'content': None, 'created_at': '2025-08-04T02:44:27.601060'}
2025-08-04 08:14:27,601 - knowledge_base_executor - ERROR - [QDRANT] NOTE error in rbi_circular: 1 validation error for PointStruct
vector
  Field required [type=missing, input_value={'id': '64a6c9ed-1ef2-5e9...08-04T02:44:27.601060'}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-04 08:14:27,601 - knowledge_base_executor - INFO - [QDRANT] Action ADD_TEMPORARY_NOTE executed with result: {"action": "ADD_TEMPORARY_NOTE", "success": true, "details": "Note note_1754255667 (ID: 64a6c9ed-1ef2-5e9f-b5ce-64b282bb63a9) added to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:44:27.601260"}
2025-08-04 08:14:27,601 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:14:27,601 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_TEMPORARY_NOTE - ✅ SUCCESS
2025-08-04 08:14:27,601 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:14:27,602 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:14:27,602 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:27,602 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:27,602 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:14:27,602 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=c9d71767-6319-4866-9ec3-3f320b9f8d01, Document=None, Chunk=3
2025-08-04 08:14:27,602 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-04 08:14:29,774 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: c9d71767-6319-4866-9ec3-3f320b9f8d01)
2025-08-04 08:14:29,774 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: c9d71767-6319-4866-9ec3-3f320b9f8d01) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:44:29.774442", "document_id": null, "uuid": "c9d71767-6319-4866-9ec3-3f320b9f8d01", "collection": "rbi_circular"}
2025-08-04 08:14:29,774 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:14:29,774 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:14:29,775 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:14:41,634 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:14:41,634 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:41,634 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:41,635 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:14:41,635 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b4875008-2a85-47bb-b08b-3561d0cadf3d, Score=0.8188, Document=DOR.CAP.BC.No.34/21.06.201/2020-21
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ca9b9dc0-5937-4790-9bcb-021c9d9d75e7, Score=0.8188, Document=DOR.CAP.BC.No.34/21.06.201/2020-21
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2105f02f-10ac-520c-82c1-d91e04509d6d, Score=0.8107, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] Match: ID=d2b23822-c5a2-5527-8507-5b3d29d5794c, Score=0.8107, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d04a677-f401-5734-88ac-14846a6cf5b3, Score=0.8107, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DOR.CAP.REC.2/21.06.201/2025-26
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['b4875008-2a85-47bb-b08b-3561d0cadf3d', 'ca9b9dc0-5937-4790-9bcb-021c9d9d75e7', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b4875008-2a85-47bb-b08b-3561d0cadf3d
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ca9b9dc0-5937-4790-9bcb-021c9d9d75e7
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['b4875008-2a85-47bb-b08b-3561d0cadf3d', 'ca9b9dc0-5937-4790-9bcb-021c9d9d75e7', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:14:41,810 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['b4875008-2a85-47bb-b08b-3561d0cadf3d', 'ca9b9dc0-5937-4790-9bcb-021c9d9d75e7', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3'])] must_not=None, Payload={}
2025-08-04 08:14:41,823 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 3 matching docs in rbi_master_direction
2025-08-04 08:14:41,837 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3189 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:14:41,837 - knowledge_base_executor - INFO - ✅ Successfully updated 3 documents in collection: rbi_master_direction
2025-08-04 08:14:41,837 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:14:41,837 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:14:41,837 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['b4875008-2a85-47bb-b08b-3561d0cadf3d', 'ca9b9dc0-5937-4790-9bcb-021c9d9d75e7', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-04 08:14:41,837 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:14:41,837 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b4875008-2a85-47bb-b08b-3561d0cadf3d
2025-08-04 08:14:41,837 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ca9b9dc0-5937-4790-9bcb-021c9d9d75e7
2025-08-04 08:14:41,837 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-04 08:14:41,837 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['b4875008-2a85-47bb-b08b-3561d0cadf3d', 'ca9b9dc0-5937-4790-9bcb-021c9d9d75e7', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-04 08:14:41,838 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:14:41,838 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['b4875008-2a85-47bb-b08b-3561d0cadf3d', 'ca9b9dc0-5937-4790-9bcb-021c9d9d75e7', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3'])] must_not=None, Payload={}
2025-08-04 08:14:41,840 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:14:41,840 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:14:41,840 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:14:41,840 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:14:41,840 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['b4875008-2a85-47bb-b08b-3561d0cadf3d', 'ca9b9dc0-5937-4790-9bcb-021c9d9d75e7', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-04 08:14:41,840 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:14:41,840 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b4875008-2a85-47bb-b08b-3561d0cadf3d
2025-08-04 08:14:41,840 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ca9b9dc0-5937-4790-9bcb-021c9d9d75e7
2025-08-04 08:14:41,840 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-04 08:14:41,840 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['b4875008-2a85-47bb-b08b-3561d0cadf3d', 'ca9b9dc0-5937-4790-9bcb-021c9d9d75e7', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-04 08:14:41,840 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:14:41,840 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['b4875008-2a85-47bb-b08b-3561d0cadf3d', 'ca9b9dc0-5937-4790-9bcb-021c9d9d75e7', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3'])] must_not=None, Payload={}
2025-08-04 08:14:41,846 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 2 matching docs in rbi_circular
2025-08-04 08:14:41,856 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13046 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:14:41,856 - knowledge_base_executor - INFO - ✅ Successfully updated 2 documents in collection: rbi_circular
2025-08-04 08:14:41,856 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction', 'rbi_circular']", "timestamp": "2025-08-04T02:44:41.856195"}
2025-08-04 08:14:41,856 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:14:41,856 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:14:41,857 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:14:41,858 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:14:41,858 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:41,858 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:41,858 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:14:41,858 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:14:41,873 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:14:41,873 - knowledge_base_executor - INFO - [QDRANT] Match: ID=6c18c45f-a0ea-4be6-a8fc-7cdcd96d6447, Score=0.8991, Document=DBR.No.FSD.BC. 58/24.01.007/2015-16
2025-08-04 08:14:41,873 - knowledge_base_executor - INFO - [QDRANT] Match: ID=e598aaf9-16d8-47fd-b4f0-f53f25861576, Score=0.8400, Document=DBOD. No. FSD. 5046 / 24.01.028/ 2006-07
2025-08-04 08:14:41,874 - knowledge_base_executor - INFO - [QDRANT] Match: ID=11be8739-4682-4e0a-9c5b-69eee676f705, Score=0.8400, Document=DBOD. No. FSD. 5046 / 24.01.028/ 2006-07
2025-08-04 08:14:41,874 - knowledge_base_executor - INFO - [QDRANT] Match: ID=12543b47-112b-4fc2-8433-86cf6fb06d44, Score=0.8400, Document=DBOD. No. FSD. 5046 / 24.01.028/ 2006-07
2025-08-04 08:14:41,874 - knowledge_base_executor - INFO - [QDRANT] Match: ID=91eadb37-b3d9-4cdf-a5f4-97912800b298, Score=0.8400, Document=DBOD. No. FSD. 5046 / 24.01.028/ 2006-07
2025-08-04 08:14:41,874 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:14:41,874 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:14:41,874 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DBR.FSD.No.101/24.01.041/2015-16
2025-08-04 08:14:41,874 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['6c18c45f-a0ea-4be6-a8fc-7cdcd96d6447', 'e598aaf9-16d8-47fd-b4f0-f53f25861576', '11be8739-4682-4e0a-9c5b-69eee676f705', '12543b47-112b-4fc2-8433-86cf6fb06d44', '91eadb37-b3d9-4cdf-a5f4-97912800b298']}
2025-08-04 08:14:41,874 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:14:41,874 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 6c18c45f-a0ea-4be6-a8fc-7cdcd96d6447
2025-08-04 08:14:41,874 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = e598aaf9-16d8-47fd-b4f0-f53f25861576
2025-08-04 08:14:41,874 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 11be8739-4682-4e0a-9c5b-69eee676f705
2025-08-04 08:14:41,874 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['6c18c45f-a0ea-4be6-a8fc-7cdcd96d6447', 'e598aaf9-16d8-47fd-b4f0-f53f25861576', '11be8739-4682-4e0a-9c5b-69eee676f705']...
2025-08-04 08:14:41,874 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:14:41,874 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['6c18c45f-a0ea-4be6-a8fc-7cdcd96d6447', 'e598aaf9-16d8-47fd-b4f0-f53f25861576', '11be8739-4682-4e0a-9c5b-69eee676f705', '12543b47-112b-4fc2-8433-86cf6fb06d44', '91eadb37-b3d9-4cdf-a5f4-97912800b298'])] must_not=None, Payload={}
2025-08-04 08:14:41,876 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:14:41,876 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:14:41,876 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:14:41,876 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:14:41,876 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['6c18c45f-a0ea-4be6-a8fc-7cdcd96d6447', 'e598aaf9-16d8-47fd-b4f0-f53f25861576', '11be8739-4682-4e0a-9c5b-69eee676f705', '12543b47-112b-4fc2-8433-86cf6fb06d44', '91eadb37-b3d9-4cdf-a5f4-97912800b298']}
2025-08-04 08:14:41,876 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:14:41,876 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 6c18c45f-a0ea-4be6-a8fc-7cdcd96d6447
2025-08-04 08:14:41,876 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = e598aaf9-16d8-47fd-b4f0-f53f25861576
2025-08-04 08:14:41,876 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 11be8739-4682-4e0a-9c5b-69eee676f705
2025-08-04 08:14:41,876 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['6c18c45f-a0ea-4be6-a8fc-7cdcd96d6447', 'e598aaf9-16d8-47fd-b4f0-f53f25861576', '11be8739-4682-4e0a-9c5b-69eee676f705']...
2025-08-04 08:14:41,876 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:14:41,876 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['6c18c45f-a0ea-4be6-a8fc-7cdcd96d6447', 'e598aaf9-16d8-47fd-b4f0-f53f25861576', '11be8739-4682-4e0a-9c5b-69eee676f705', '12543b47-112b-4fc2-8433-86cf6fb06d44', '91eadb37-b3d9-4cdf-a5f4-97912800b298'])] must_not=None, Payload={}
2025-08-04 08:14:41,878 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:14:41,878 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:14:41,878 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:14:41,878 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:14:41,878 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['6c18c45f-a0ea-4be6-a8fc-7cdcd96d6447', 'e598aaf9-16d8-47fd-b4f0-f53f25861576', '11be8739-4682-4e0a-9c5b-69eee676f705', '12543b47-112b-4fc2-8433-86cf6fb06d44', '91eadb37-b3d9-4cdf-a5f4-97912800b298']}
2025-08-04 08:14:41,878 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:14:41,878 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 6c18c45f-a0ea-4be6-a8fc-7cdcd96d6447
2025-08-04 08:14:41,878 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = e598aaf9-16d8-47fd-b4f0-f53f25861576
2025-08-04 08:14:41,878 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 11be8739-4682-4e0a-9c5b-69eee676f705
2025-08-04 08:14:41,878 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['6c18c45f-a0ea-4be6-a8fc-7cdcd96d6447', 'e598aaf9-16d8-47fd-b4f0-f53f25861576', '11be8739-4682-4e0a-9c5b-69eee676f705']...
2025-08-04 08:14:41,878 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:14:41,879 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['6c18c45f-a0ea-4be6-a8fc-7cdcd96d6447', 'e598aaf9-16d8-47fd-b4f0-f53f25861576', '11be8739-4682-4e0a-9c5b-69eee676f705', '12543b47-112b-4fc2-8433-86cf6fb06d44', '91eadb37-b3d9-4cdf-a5f4-97912800b298'])] must_not=None, Payload={}
2025-08-04 08:14:41,891 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:14:41,909 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13047 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:14:41,909 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:14:41,909 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T02:44:41.909486"}
2025-08-04 08:14:41,909 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:14:41,909 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:14:41,910 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:14:41,910 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:14:41,910 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:41,910 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:41,910 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:14:41,910 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=aaec8431-7b9d-43fc-bf08-4274179466c6, Document=None, Chunk=4
2025-08-04 08:14:41,910 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-04 08:14:44,468 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: aaec8431-7b9d-43fc-bf08-4274179466c6)
2025-08-04 08:14:44,468 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: aaec8431-7b9d-43fc-bf08-4274179466c6) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:44:44.468396", "document_id": null, "uuid": "aaec8431-7b9d-43fc-bf08-4274179466c6", "collection": "rbi_circular"}
2025-08-04 08:14:44,468 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:14:44,468 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:14:44,469 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:14:52,951 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:14:52,951 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:52,952 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:52,952 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:14:52,952 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:14:53,125 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:14:53,125 - knowledge_base_executor - INFO - [QDRANT] Match: ID=8e6d907a-b693-45a0-b2d9-1f660175239d, Score=1.0000, Document=DBR.BP.BC.No.86/21.04.098/2015-16
2025-08-04 08:14:53,126 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87, Score=0.9759, Document=DBR.BP.BC.No.08/21.04.098/2018-19
2025-08-04 08:14:53,126 - knowledge_base_executor - INFO - [QDRANT] Match: ID=7b2fef0c-fe1a-439c-b47a-337b826dfd03, Score=0.9759, Document=DBR.BP.BC.No.08/21.04.098/2018-19
2025-08-04 08:14:53,126 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b2b924ef-1354-4cb9-99f1-dd60059d29cb, Score=0.9649, Document=DBR.BP.BC.No.46/21.04.098/2014-15
2025-08-04 08:14:53,126 - knowledge_base_executor - INFO - [QDRANT] Match: ID=e9cef7a1-91cd-4046-8cc2-9240daf5d554, Score=0.9649, Document=DBR.BP.BC.No.46/21.04.098/2014-15
2025-08-04 08:14:53,126 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:14:53,126 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:14:53,126 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DBR.BP.BC.No.86/21.04.098/2015-16
2025-08-04 08:14:53,126 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554']}
2025-08-04 08:14:53,126 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:14:53,126 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8e6d907a-b693-45a0-b2d9-1f660175239d
2025-08-04 08:14:53,126 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87
2025-08-04 08:14:53,126 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7b2fef0c-fe1a-439c-b47a-337b826dfd03
2025-08-04 08:14:53,126 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03']...
2025-08-04 08:14:53,126 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:14:53,126 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554'])] must_not=None, Payload={}
2025-08-04 08:14:53,129 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:14:53,130 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:14:53,130 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:14:53,130 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:14:53,130 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554']}
2025-08-04 08:14:53,130 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:14:53,130 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8e6d907a-b693-45a0-b2d9-1f660175239d
2025-08-04 08:14:53,130 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87
2025-08-04 08:14:53,130 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7b2fef0c-fe1a-439c-b47a-337b826dfd03
2025-08-04 08:14:53,130 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03']...
2025-08-04 08:14:53,130 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:14:53,130 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554'])] must_not=None, Payload={}
2025-08-04 08:14:53,133 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:14:53,134 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:14:53,134 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:14:53,134 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:14:53,134 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554']}
2025-08-04 08:14:53,134 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:14:53,134 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8e6d907a-b693-45a0-b2d9-1f660175239d
2025-08-04 08:14:53,134 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87
2025-08-04 08:14:53,134 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7b2fef0c-fe1a-439c-b47a-337b826dfd03
2025-08-04 08:14:53,134 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03']...
2025-08-04 08:14:53,134 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:14:53,134 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['8e6d907a-b693-45a0-b2d9-1f660175239d', '0493b27d-b7f1-4bf8-b0e5-4b7b03b30e87', '7b2fef0c-fe1a-439c-b47a-337b826dfd03', 'b2b924ef-1354-4cb9-99f1-dd60059d29cb', 'e9cef7a1-91cd-4046-8cc2-9240daf5d554'])] must_not=None, Payload={}
2025-08-04 08:14:53,151 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:14:53,172 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13049 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:14:53,172 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:14:53,172 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T02:44:53.172252"}
2025-08-04 08:14:53,172 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:14:53,172 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:14:53,173 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:14:53,173 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:14:53,173 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:53,173 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Basel III Framework on Liquidity Standards _ Liquidity Coverage Ratio _LCR_ _ Review of haircuts on High Quality Liquid Assets _HQLA_ and review of composition and run-off rates on certain categories of deposits.pdf
2025-08-04 08:14:53,173 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:14:53,173 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=f2062d95-4909-4124-9dd7-468b2792e39b, Document=None, Chunk=5
2025-08-04 08:14:53,173 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 5493 chars
2025-08-04 08:14:56,988 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: f2062d95-4909-4124-9dd7-468b2792e39b)
2025-08-04 08:14:56,988 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: f2062d95-4909-4124-9dd7-468b2792e39b) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:44:56.988351", "document_id": null, "uuid": "f2062d95-4909-4124-9dd7-468b2792e39b", "collection": "rbi_circular"}
2025-08-04 08:14:56,988 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:14:56,988 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:14:56,989 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:15:06,837 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:15:06,838 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Export-Import Bank of India_s GOI-supported Line of Credit of USD 120 mn to the Government of the Socialist Republic of Vietnam _GO-VNM_ for procurement of High-Speed Guard Boats in the Borrower_s Country.pdf
2025-08-04 08:15:06,838 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Export-Import Bank of India_s GOI-supported Line of Credit of USD 120 mn to the Government of the Socialist Republic of Vietnam _GO-VNM_ for procurement of High-Speed Guard Boats in the Borrower_s Country.pdf
2025-08-04 08:15:06,838 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:15:06,838 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=b8a6e61c-c916-5a6c-a872-7c7a6b954ffd, Document=A.P. (DIR Series) Circular No. 21, Chunk=None
2025-08-04 08:15:06,838 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for A.P. (DIR Series) Circular No. 21 with text: 8000 chars
2025-08-04 08:15:09,318 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for A.P. (DIR Series) Circular No. 21 (ID: b8a6e61c-c916-5a6c-a872-7c7a6b954ffd)
2025-08-04 08:15:09,318 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document A.P. (DIR Series) Circular No. 21 (ID: b8a6e61c-c916-5a6c-a872-7c7a6b954ffd) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:45:09.318664", "document_id": "A.P. (DIR Series) Circular No. 21", "uuid": "b8a6e61c-c916-5a6c-a872-7c7a6b954ffd", "collection": "rbi_other"}
2025-08-04 08:15:09,318 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:15:09,318 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:15:09,319 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:15:20,310 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:15:20,310 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Export-Import Bank of India_s GOI-supported Line of Credit of USD 120 mn to the Government of the Socialist Republic of Vietnam _GO-VNM_ for procurement of High-Speed Guard Boats in the Borrower_s Country.pdf
2025-08-04 08:15:20,310 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Export-Import Bank of India_s GOI-supported Line of Credit of USD 120 mn to the Government of the Socialist Republic of Vietnam _GO-VNM_ for procurement of High-Speed Guard Boats in the Borrower_s Country.pdf
2025-08-04 08:15:20,310 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:15:20,311 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=d79a6a5b-97e7-5462-ac8f-b325af0db62b, Document=RBI//2024-2025/114, Chunk=0
2025-08-04 08:15:20,311 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI//2024-2025/114 with text: 7730 chars
2025-08-04 08:15:24,252 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI//2024-2025/114 (ID: d79a6a5b-97e7-5462-ac8f-b325af0db62b)
2025-08-04 08:15:24,252 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI//2024-2025/114 (ID: d79a6a5b-97e7-5462-ac8f-b325af0db62b) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:45:24.252054", "document_id": "RBI//2024-2025/114", "uuid": "d79a6a5b-97e7-5462-ac8f-b325af0db62b", "collection": "rbi_master_direction"}
2025-08-04 08:15:24,252 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:15:24,252 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:15:24,252 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:15:24,253 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:15:24,253 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Export-Import Bank of India_s GOI-supported Line of Credit of USD 120 mn to the Government of the Socialist Republic of Vietnam _GO-VNM_ for procurement of High-Speed Guard Boats in the Borrower_s Country.pdf
2025-08-04 08:15:24,253 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Export-Import Bank of India_s GOI-supported Line of Credit of USD 120 mn to the Government of the Socialist Republic of Vietnam _GO-VNM_ for procurement of High-Speed Guard Boats in the Borrower_s Country.pdf
2025-08-04 08:15:24,253 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:15:24,253 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=287de810-85a0-42f2-987e-d9b6e3fb9ad2, Document=None, Chunk=0
2025-08-04 08:15:24,253 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 7638 chars
2025-08-04 08:15:27,404 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 287de810-85a0-42f2-987e-d9b6e3fb9ad2)
2025-08-04 08:15:27,404 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 287de810-85a0-42f2-987e-d9b6e3fb9ad2) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:45:27.404957", "document_id": null, "uuid": "287de810-85a0-42f2-987e-d9b6e3fb9ad2", "collection": "rbi_circular"}
2025-08-04 08:15:27,405 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:15:27,405 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:15:27,405 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:15:36,846 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:15:36,847 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Export-Import Bank of India_s GOI-supported Line of Credit of USD 180 mn to the Government of the Socialist Republic of Vietnam for procurement of 4 Offshore Patrol Vessels _OPV_ in the Borrower_s Country.pdf
2025-08-04 08:15:36,847 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Export-Import Bank of India_s GOI-supported Line of Credit of USD 180 mn to the Government of the Socialist Republic of Vietnam for procurement of 4 Offshore Patrol Vessels _OPV_ in the Borrower_s Country.pdf
2025-08-04 08:15:36,847 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:15:36,847 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=9d19d38b-95e9-5532-8985-ba28fbbc46cb, Document=A.P. (DIR Series) Circular No. 20, Chunk=None
2025-08-04 08:15:36,847 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for A.P. (DIR Series) Circular No. 20 with text: 8000 chars
2025-08-04 08:15:41,336 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for A.P. (DIR Series) Circular No. 20 (ID: 9d19d38b-95e9-5532-8985-ba28fbbc46cb)
2025-08-04 08:15:41,336 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document A.P. (DIR Series) Circular No. 20 (ID: 9d19d38b-95e9-5532-8985-ba28fbbc46cb) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:45:41.336100", "document_id": "A.P. (DIR Series) Circular No. 20", "uuid": "9d19d38b-95e9-5532-8985-ba28fbbc46cb", "collection": "rbi_other"}
2025-08-04 08:15:41,336 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:15:41,336 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:15:41,336 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:15:53,281 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:15:53,282 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Export-Import Bank of India_s GOI-supported Line of Credit of USD 180 mn to the Government of the Socialist Republic of Vietnam for procurement of 4 Offshore Patrol Vessels _OPV_ in the Borrower_s Country.pdf
2025-08-04 08:15:53,282 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Export-Import Bank of India_s GOI-supported Line of Credit of USD 180 mn to the Government of the Socialist Republic of Vietnam for procurement of 4 Offshore Patrol Vessels _OPV_ in the Borrower_s Country.pdf
2025-08-04 08:15:53,282 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:15:53,282 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=82cc3058-b7bd-5e1e-aca0-9909c0286651, Document=RBI//2024-2025/113, Chunk=0
2025-08-04 08:15:53,282 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI//2024-2025/113 with text: 7711 chars
2025-08-04 08:15:54,965 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI//2024-2025/113 (ID: 82cc3058-b7bd-5e1e-aca0-9909c0286651)
2025-08-04 08:15:54,965 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI//2024-2025/113 (ID: 82cc3058-b7bd-5e1e-aca0-9909c0286651) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:45:54.965761", "document_id": "RBI//2024-2025/113", "uuid": "82cc3058-b7bd-5e1e-aca0-9909c0286651", "collection": "rbi_master_direction"}
2025-08-04 08:15:54,965 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:15:54,965 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:15:54,966 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:15:54,967 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:15:54,967 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Export-Import Bank of India_s GOI-supported Line of Credit of USD 180 mn to the Government of the Socialist Republic of Vietnam for procurement of 4 Offshore Patrol Vessels _OPV_ in the Borrower_s Country.pdf
2025-08-04 08:15:54,967 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Export-Import Bank of India_s GOI-supported Line of Credit of USD 180 mn to the Government of the Socialist Republic of Vietnam for procurement of 4 Offshore Patrol Vessels _OPV_ in the Borrower_s Country.pdf
2025-08-04 08:15:54,967 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:15:54,967 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=395c0ab7-8c5d-4736-b7e1-b28a5be03d30, Document=None, Chunk=0
2025-08-04 08:15:54,967 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 7601 chars
2025-08-04 08:15:57,509 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 395c0ab7-8c5d-4736-b7e1-b28a5be03d30)
2025-08-04 08:15:57,509 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 395c0ab7-8c5d-4736-b7e1-b28a5be03d30) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:45:57.509347", "document_id": null, "uuid": "395c0ab7-8c5d-4736-b7e1-b28a5be03d30", "collection": "rbi_circular"}
2025-08-04 08:15:57,509 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:15:57,509 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:15:57,510 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:16:07,666 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:16:07,667 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exports through warehouses in _Bharat Mart_ in UAE _ relaxations.pdf
2025-08-04 08:16:07,667 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exports through warehouses in _Bharat Mart_ in UAE _ relaxations.pdf
2025-08-04 08:16:07,667 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:16:07,667 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=ce79a1d5-cf70-5c51-97a0-70e80a9b979b, Document=RBI/2025-26/30 - A.P. (DIR Series) Circular No. 03, Chunk=None
2025-08-04 08:16:07,667 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/30 - A.P. (DIR Series) Circular No. 03 with text: 8000 chars
2025-08-04 08:16:09,137 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/30 - A.P. (DIR Series) Circular No. 03 (ID: ce79a1d5-cf70-5c51-97a0-70e80a9b979b)
2025-08-04 08:16:09,137 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/30 - A.P. (DIR Series) Circular No. 03 (ID: ce79a1d5-cf70-5c51-97a0-70e80a9b979b) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:46:09.137562", "document_id": "RBI/2025-26/30 - A.P. (DIR Series) Circular No. 03", "uuid": "ce79a1d5-cf70-5c51-97a0-70e80a9b979b", "collection": "rbi_other"}
2025-08-04 08:16:09,137 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:16:09,137 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:16:09,138 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:16:23,901 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:16:23,901 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exports through warehouses in _Bharat Mart_ in UAE _ relaxations.pdf
2025-08-04 08:16:23,901 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exports through warehouses in _Bharat Mart_ in UAE _ relaxations.pdf
2025-08-04 08:16:23,901 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:16:23,901 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d0e725b-aeac-5e6b-80ff-92fe03d7caf2, Score=1.0000, Document=FEMA 23(R)/2015-RB
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06f77fc3-7228-514f-8562-24519f5d9d4a, Score=1.0000, Document=FEMA 23(R)/2015-RB
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] Match: ID=4700aab3-5bc9-5aac-a983-e08bd853f0bb, Score=1.0000, Document=FEMA 23(R)/2015-RB
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] Match: ID=81ccb53b-4614-5c94-a95b-f02af834fc91, Score=1.0000, Document=FEMA 23(R)/2015-RB
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] Match: ID=709094ad-5569-562d-bfef-a7c7fe49438c, Score=1.0000, Document=FEMA 23(R)/2015-RB
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'FEMA 23(R)/2015-RB' → 'FEMA23(R)/2015-RB'
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: FEMA23(R)/2015-RB
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb']...
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:16:23,974 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:16:23,987 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-04 08:16:24,011 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3192 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:16:24,011 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-04 08:16:24,012 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:16:24,012 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:16:24,012 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:16:24,012 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:16:24,012 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:16:24,012 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:16:24,012 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:16:24,012 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb']...
2025-08-04 08:16:24,012 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:16:24,012 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:16:24,014 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:16:24,014 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:16:24,014 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:16:24,014 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:16:24,014 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:16:24,015 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:16:24,015 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:16:24,015 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:16:24,015 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:16:24,015 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb']...
2025-08-04 08:16:24,015 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:16:24,015 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:16:24,016 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:16:24,016 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:16:24,017 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T02:46:24.017022"}
2025-08-04 08:16:24,017 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:16:24,017 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:16:24,019 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:16:24,019 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:16:24,019 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exports through warehouses in _Bharat Mart_ in UAE _ relaxations.pdf
2025-08-04 08:16:24,019 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exports through warehouses in _Bharat Mart_ in UAE _ relaxations.pdf
2025-08-04 08:16:24,019 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:16:24,019 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3dbb0609-58a6-4ddc-b8de-6e34905588ee, Score=0.4516, Document=Master Circular - Direct Investment in Joint Ventures/Wholly Owned Subsidiaries Abroad
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9ba928ad-1189-42ac-bef5-48c97d292353, Score=0.4516, Document=Master Circular - Direct Investment in Joint Ventures/Wholly Owned Subsidiaries Abroad
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] Match: ID=39c032aa-cda6-4aca-b9b4-8be184ca45a7, Score=0.4516, Document=Master Circular - Direct Investment in Joint Ventures/Wholly Owned Subsidiaries Abroad
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] Match: ID=6b8201eb-c85b-45f4-bf87-07a1c4e3f5ae, Score=0.4516, Document=Master Circular - Direct Investment in Joint Ventures/Wholly Owned Subsidiaries Abroad
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] Match: ID=8282904c-2d3e-4e8e-aeb9-775752e497f9, Score=0.4516, Document=Master Circular - Direct Investment in Joint Ventures/Wholly Owned Subsidiaries Abroad
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'Master Direction – Export of Goods & Services' → 'MasterDirection–ExportofGoods&Services'
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: MasterDirection–ExportofGoods&Services
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['3dbb0609-58a6-4ddc-b8de-6e34905588ee', '9ba928ad-1189-42ac-bef5-48c97d292353', '39c032aa-cda6-4aca-b9b4-8be184ca45a7', '6b8201eb-c85b-45f4-bf87-07a1c4e3f5ae', '8282904c-2d3e-4e8e-aeb9-775752e497f9']}
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3dbb0609-58a6-4ddc-b8de-6e34905588ee
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9ba928ad-1189-42ac-bef5-48c97d292353
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 39c032aa-cda6-4aca-b9b4-8be184ca45a7
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['3dbb0609-58a6-4ddc-b8de-6e34905588ee', '9ba928ad-1189-42ac-bef5-48c97d292353', '39c032aa-cda6-4aca-b9b4-8be184ca45a7']...
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:16:24,036 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['3dbb0609-58a6-4ddc-b8de-6e34905588ee', '9ba928ad-1189-42ac-bef5-48c97d292353', '39c032aa-cda6-4aca-b9b4-8be184ca45a7', '6b8201eb-c85b-45f4-bf87-07a1c4e3f5ae', '8282904c-2d3e-4e8e-aeb9-775752e497f9'])] must_not=None, Payload={}
2025-08-04 08:16:24,038 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:16:24,039 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:16:24,039 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:16:24,039 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:16:24,039 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['3dbb0609-58a6-4ddc-b8de-6e34905588ee', '9ba928ad-1189-42ac-bef5-48c97d292353', '39c032aa-cda6-4aca-b9b4-8be184ca45a7', '6b8201eb-c85b-45f4-bf87-07a1c4e3f5ae', '8282904c-2d3e-4e8e-aeb9-775752e497f9']}
2025-08-04 08:16:24,039 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:16:24,039 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3dbb0609-58a6-4ddc-b8de-6e34905588ee
2025-08-04 08:16:24,039 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9ba928ad-1189-42ac-bef5-48c97d292353
2025-08-04 08:16:24,039 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 39c032aa-cda6-4aca-b9b4-8be184ca45a7
2025-08-04 08:16:24,039 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['3dbb0609-58a6-4ddc-b8de-6e34905588ee', '9ba928ad-1189-42ac-bef5-48c97d292353', '39c032aa-cda6-4aca-b9b4-8be184ca45a7']...
2025-08-04 08:16:24,039 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:16:24,039 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['3dbb0609-58a6-4ddc-b8de-6e34905588ee', '9ba928ad-1189-42ac-bef5-48c97d292353', '39c032aa-cda6-4aca-b9b4-8be184ca45a7', '6b8201eb-c85b-45f4-bf87-07a1c4e3f5ae', '8282904c-2d3e-4e8e-aeb9-775752e497f9'])] must_not=None, Payload={}
2025-08-04 08:16:24,053 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_circular
2025-08-04 08:16:24,075 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8289 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:16:24,075 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_circular
2025-08-04 08:16:24,075 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:16:24,075 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:16:24,075 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['3dbb0609-58a6-4ddc-b8de-6e34905588ee', '9ba928ad-1189-42ac-bef5-48c97d292353', '39c032aa-cda6-4aca-b9b4-8be184ca45a7', '6b8201eb-c85b-45f4-bf87-07a1c4e3f5ae', '8282904c-2d3e-4e8e-aeb9-775752e497f9']}
2025-08-04 08:16:24,075 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:16:24,075 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3dbb0609-58a6-4ddc-b8de-6e34905588ee
2025-08-04 08:16:24,076 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9ba928ad-1189-42ac-bef5-48c97d292353
2025-08-04 08:16:24,076 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 39c032aa-cda6-4aca-b9b4-8be184ca45a7
2025-08-04 08:16:24,076 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['3dbb0609-58a6-4ddc-b8de-6e34905588ee', '9ba928ad-1189-42ac-bef5-48c97d292353', '39c032aa-cda6-4aca-b9b4-8be184ca45a7']...
2025-08-04 08:16:24,076 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:16:24,076 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['3dbb0609-58a6-4ddc-b8de-6e34905588ee', '9ba928ad-1189-42ac-bef5-48c97d292353', '39c032aa-cda6-4aca-b9b4-8be184ca45a7', '6b8201eb-c85b-45f4-bf87-07a1c4e3f5ae', '8282904c-2d3e-4e8e-aeb9-775752e497f9'])] must_not=None, Payload={}
2025-08-04 08:16:24,078 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:16:24,078 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:16:24,078 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular']", "timestamp": "2025-08-04T02:46:24.078121"}
2025-08-04 08:16:24,078 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:16:24,078 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:16:24,078 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:16:24,079 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:16:24,079 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exports through warehouses in _Bharat Mart_ in UAE _ relaxations.pdf
2025-08-04 08:16:24,079 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exports through warehouses in _Bharat Mart_ in UAE _ relaxations.pdf
2025-08-04 08:16:24,079 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:16:24,079 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=e5acc013-ef1f-4110-b44a-b18ecad6e7d9, Document=None, Chunk=0
2025-08-04 08:16:24,079 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 7684 chars
2025-08-04 08:16:27,756 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: e5acc013-ef1f-4110-b44a-b18ecad6e7d9)
2025-08-04 08:16:27,756 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: e5acc013-ef1f-4110-b44a-b18ecad6e7d9) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:46:27.756087", "document_id": null, "uuid": "e5acc013-ef1f-4110-b44a-b18ecad6e7d9", "collection": "rbi_circular"}
2025-08-04 08:16:27,756 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:16:27,756 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:16:27,756 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:16:35,688 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:16:35,688 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:16:35,688 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:16:35,689 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:16:35,689 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=7dd21da7-8eb3-4e03-a414-499168cb3506, Document=, Chunk=None
2025-08-04 08:16:35,689 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for  with text: 8000 chars
2025-08-04 08:16:38,962 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for  (ID: 7dd21da7-8eb3-4e03-a414-499168cb3506)
2025-08-04 08:16:38,962 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document  (ID: 7dd21da7-8eb3-4e03-a414-499168cb3506) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:46:38.962399", "document_id": "", "uuid": "7dd21da7-8eb3-4e03-a414-499168cb3506", "collection": "rbi_other"}
2025-08-04 08:16:38,962 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:16:38,962 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:16:38,963 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:17:18,767 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:17:18,767 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:18,768 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:18,768 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:17:18,768 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:17:19,025 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:17:19,025 - knowledge_base_executor - INFO - [QDRANT] Match: ID=4700aab3-5bc9-5aac-a983-e08bd853f0bb, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,025 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d0e725b-aeac-5e6b-80ff-92fe03d7caf2, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,025 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06f77fc3-7228-514f-8562-24519f5d9d4a, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,025 - knowledge_base_executor - INFO - [QDRANT] Match: ID=81ccb53b-4614-5c94-a95b-f02af834fc91, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,025 - knowledge_base_executor - INFO - [QDRANT] Match: ID=709094ad-5569-562d-bfef-a7c7fe49438c, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,025 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,025 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:17:19,025 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'FEMA 5(R)/2016-RB' → 'FEMA5(R)/2016-RB'
2025-08-04 08:17:19,025 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: FEMA5(R)/2016-RB
2025-08-04 08:17:19,025 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,025 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,025 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,025 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,025 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,025 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,026 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,026 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,040 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-04 08:17:19,060 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3193 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:17:19,060 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-04 08:17:19,060 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,060 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:17:19,060 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,060 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,060 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,060 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,060 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,060 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,060 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,060 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,063 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:17:19,063 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:17:19,063 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,063 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:17:19,063 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,063 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,063 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,063 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,063 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,063 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,063 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,063 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,066 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:17:19,066 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:17:19,066 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T02:47:19.066466"}
2025-08-04 08:17:19,066 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:17:19,066 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:17:19,071 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:17:19,071 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:17:19,071 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:19,071 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:19,072 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:17:19,072 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:17:19,087 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:17:19,087 - knowledge_base_executor - INFO - [QDRANT] Match: ID=4700aab3-5bc9-5aac-a983-e08bd853f0bb, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,088 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d0e725b-aeac-5e6b-80ff-92fe03d7caf2, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,088 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06f77fc3-7228-514f-8562-24519f5d9d4a, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,088 - knowledge_base_executor - INFO - [QDRANT] Match: ID=81ccb53b-4614-5c94-a95b-f02af834fc91, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,088 - knowledge_base_executor - INFO - [QDRANT] Match: ID=709094ad-5569-562d-bfef-a7c7fe49438c, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,088 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,088 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:17:19,088 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'FEMA 5(R)/2016-RB' → 'FEMA5(R)/2016-RB'
2025-08-04 08:17:19,088 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: FEMA5(R)/2016-RB
2025-08-04 08:17:19,088 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,088 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,088 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,088 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,088 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,088 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,088 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,088 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,100 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-04 08:17:19,119 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3194 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:17:19,119 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-04 08:17:19,119 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,119 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:17:19,119 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,119 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,119 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,119 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,119 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,120 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,120 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,120 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,122 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:17:19,122 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:17:19,122 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,122 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:17:19,122 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,122 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,122 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,122 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,122 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,122 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,122 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,122 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,124 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:17:19,124 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:17:19,124 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T02:47:19.124393"}
2025-08-04 08:17:19,124 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:17:19,124 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:17:19,125 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:17:19,125 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:17:19,125 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:19,125 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:19,125 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:17:19,125 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:17:19,139 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:17:19,139 - knowledge_base_executor - INFO - [QDRANT] Match: ID=4700aab3-5bc9-5aac-a983-e08bd853f0bb, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,139 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d0e725b-aeac-5e6b-80ff-92fe03d7caf2, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,139 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06f77fc3-7228-514f-8562-24519f5d9d4a, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,139 - knowledge_base_executor - INFO - [QDRANT] Match: ID=81ccb53b-4614-5c94-a95b-f02af834fc91, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,139 - knowledge_base_executor - INFO - [QDRANT] Match: ID=709094ad-5569-562d-bfef-a7c7fe49438c, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,139 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,139 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:17:19,139 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'FEMA 5(R)/2016-RB' → 'FEMA5(R)/2016-RB'
2025-08-04 08:17:19,140 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: FEMA5(R)/2016-RB
2025-08-04 08:17:19,140 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,140 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,140 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,140 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,140 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,140 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,140 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,140 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,148 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-04 08:17:19,163 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3195 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:17:19,163 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-04 08:17:19,163 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,163 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:17:19,163 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,163 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,163 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,164 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,164 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,164 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,164 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,164 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,165 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:17:19,165 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:17:19,165 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,165 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:17:19,165 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,165 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,165 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,165 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,165 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,165 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,165 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,166 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,167 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:17:19,167 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:17:19,167 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T02:47:19.167385"}
2025-08-04 08:17:19,167 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:17:19,167 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:17:19,168 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:17:19,168 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:17:19,168 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:19,168 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:19,168 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:17:19,168 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:17:19,183 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:17:19,183 - knowledge_base_executor - INFO - [QDRANT] Match: ID=4700aab3-5bc9-5aac-a983-e08bd853f0bb, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,183 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d0e725b-aeac-5e6b-80ff-92fe03d7caf2, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,184 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06f77fc3-7228-514f-8562-24519f5d9d4a, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,184 - knowledge_base_executor - INFO - [QDRANT] Match: ID=81ccb53b-4614-5c94-a95b-f02af834fc91, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,184 - knowledge_base_executor - INFO - [QDRANT] Match: ID=709094ad-5569-562d-bfef-a7c7fe49438c, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,184 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,184 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:17:19,184 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'FEMA 5(R)/2016-RB' → 'FEMA5(R)/2016-RB'
2025-08-04 08:17:19,184 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: FEMA5(R)/2016-RB
2025-08-04 08:17:19,184 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,184 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,184 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,184 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,184 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,184 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,184 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,184 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,192 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-04 08:17:19,209 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3196 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:17:19,209 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-04 08:17:19,209 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,209 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:17:19,209 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,210 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,210 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,210 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,210 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,210 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,210 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,210 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,212 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:17:19,212 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:17:19,212 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,212 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:17:19,212 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,212 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,212 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,212 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,212 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,212 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,212 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,212 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,214 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:17:19,214 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:17:19,214 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T02:47:19.214102"}
2025-08-04 08:17:19,214 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:17:19,214 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:17:19,214 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:17:19,215 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:17:19,215 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:19,215 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:19,215 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:17:19,215 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] Match: ID=4700aab3-5bc9-5aac-a983-e08bd853f0bb, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d0e725b-aeac-5e6b-80ff-92fe03d7caf2, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06f77fc3-7228-514f-8562-24519f5d9d4a, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] Match: ID=81ccb53b-4614-5c94-a95b-f02af834fc91, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] Match: ID=709094ad-5569-562d-bfef-a7c7fe49438c, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'FEMA 5(R)/2016-RB' → 'FEMA5(R)/2016-RB'
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: FEMA5(R)/2016-RB
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,231 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,238 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-04 08:17:19,254 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3197 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:17:19,254 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-04 08:17:19,254 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,254 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:17:19,254 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,254 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,254 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,254 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,254 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,254 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,254 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,254 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,256 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:17:19,256 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:17:19,256 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,256 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:17:19,256 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,256 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,256 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,256 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,256 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,256 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,256 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,256 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,257 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:17:19,257 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:17:19,258 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T02:47:19.257955"}
2025-08-04 08:17:19,258 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:17:19,258 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:17:19,258 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:17:19,259 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:17:19,259 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:19,259 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:19,259 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:17:19,259 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:17:19,272 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:17:19,272 - knowledge_base_executor - INFO - [QDRANT] Match: ID=4700aab3-5bc9-5aac-a983-e08bd853f0bb, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,272 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d0e725b-aeac-5e6b-80ff-92fe03d7caf2, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,272 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06f77fc3-7228-514f-8562-24519f5d9d4a, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,272 - knowledge_base_executor - INFO - [QDRANT] Match: ID=81ccb53b-4614-5c94-a95b-f02af834fc91, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,272 - knowledge_base_executor - INFO - [QDRANT] Match: ID=709094ad-5569-562d-bfef-a7c7fe49438c, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,272 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,272 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:17:19,272 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'FEMA 5(R)/2016-RB' → 'FEMA5(R)/2016-RB'
2025-08-04 08:17:19,272 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: FEMA5(R)/2016-RB
2025-08-04 08:17:19,272 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,272 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,273 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,280 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-04 08:17:19,297 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3198 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:17:19,297 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-04 08:17:19,297 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,297 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:17:19,297 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,297 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,297 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,297 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,297 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,297 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,297 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,297 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,299 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:17:19,299 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:17:19,299 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,299 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:17:19,299 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,299 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,299 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,299 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,299 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,299 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,299 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,299 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,300 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:17:19,301 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:17:19,301 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T02:47:19.301057"}
2025-08-04 08:17:19,301 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:17:19,301 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:17:19,301 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:17:19,302 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:17:19,302 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:19,302 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:19,302 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:17:19,302 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] Match: ID=4700aab3-5bc9-5aac-a983-e08bd853f0bb, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d0e725b-aeac-5e6b-80ff-92fe03d7caf2, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06f77fc3-7228-514f-8562-24519f5d9d4a, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] Match: ID=81ccb53b-4614-5c94-a95b-f02af834fc91, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] Match: ID=709094ad-5569-562d-bfef-a7c7fe49438c, Score=0.9070, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'FEMA 5(R)/2016-RB' → 'FEMA5(R)/2016-RB'
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: FEMA5(R)/2016-RB
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,316 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,323 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-04 08:17:19,338 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3199 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:17:19,338 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-04 08:17:19,338 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,338 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:17:19,338 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,338 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,338 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,338 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,338 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,338 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,338 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,338 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,340 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:17:19,340 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:17:19,340 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:19,340 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:17:19,340 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:17:19,340 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:19,340 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:19,340 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:17:19,340 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:19,340 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:19,340 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:19,340 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:17:19,341 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:17:19,341 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:17:19,341 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T02:47:19.341695"}
2025-08-04 08:17:19,341 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:17:19,341 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:17:19,342 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:17:19,342 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:17:19,342 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:19,342 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Deposit_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:19,342 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:17:19,342 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=17535043-a944-46ea-9136-8161c4ac51c0, Document=None, Chunk=0
2025-08-04 08:17:19,343 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 7704 chars
2025-08-04 08:17:23,973 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 17535043-a944-46ea-9136-8161c4ac51c0)
2025-08-04 08:17:23,973 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 17535043-a944-46ea-9136-8161c4ac51c0) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:47:23.973362", "document_id": null, "uuid": "17535043-a944-46ea-9136-8161c4ac51c0", "collection": "rbi_circular"}
2025-08-04 08:17:23,973 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:17:23,973 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:17:23,974 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:17:31,612 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:17:31,612 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Foreign Currency Accounts by a person resident in India_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:31,612 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Foreign Currency Accounts by a person resident in India_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:31,612 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:17:31,612 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=9dd47c57-5d7d-5b2d-b7e5-32b1ccbc36a1, Document=FEMA 10(R)/2015-RB, Chunk=None
2025-08-04 08:17:31,612 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for FEMA 10(R)/2015-RB with text: 8000 chars
2025-08-04 08:17:35,054 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for FEMA 10(R)/2015-RB (ID: 9dd47c57-5d7d-5b2d-b7e5-32b1ccbc36a1)
2025-08-04 08:17:35,054 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document FEMA 10(R)/2015-RB (ID: 9dd47c57-5d7d-5b2d-b7e5-32b1ccbc36a1) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:47:35.054514", "document_id": "FEMA 10(R)/2015-RB", "uuid": "9dd47c57-5d7d-5b2d-b7e5-32b1ccbc36a1", "collection": "rbi_other"}
2025-08-04 08:17:35,054 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:17:35,054 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:17:35,055 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:17:43,158 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:17:43,158 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Foreign Currency Accounts by a person resident in India_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:43,159 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Foreign Currency Accounts by a person resident in India_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:43,159 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:17:43,159 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:17:43,231 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:17:43,231 - knowledge_base_executor - INFO - [QDRANT] Match: ID=1a281f72-acde-49f1-9f3a-0cda45df7f8b, Score=1.0000, Document=FEMA 10(R)/2015-RB
2025-08-04 08:17:43,231 - knowledge_base_executor - INFO - [QDRANT] Match: ID=4700aab3-5bc9-5aac-a983-e08bd853f0bb, Score=0.9529, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:43,231 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06f77fc3-7228-514f-8562-24519f5d9d4a, Score=0.9529, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:43,231 - knowledge_base_executor - INFO - [QDRANT] Match: ID=81ccb53b-4614-5c94-a95b-f02af834fc91, Score=0.9529, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:43,231 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d0e725b-aeac-5e6b-80ff-92fe03d7caf2, Score=0.9529, Document=FEMA 23(R)/2015-RB
2025-08-04 08:17:43,231 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:43,231 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:17:43,231 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'FEMA 10(R)/2015-RB' → 'FEMA10(R)/2015-RB'
2025-08-04 08:17:43,231 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: FEMA10(R)/2015-RB
2025-08-04 08:17:43,231 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2']}
2025-08-04 08:17:43,231 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:43,231 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 1a281f72-acde-49f1-9f3a-0cda45df7f8b
2025-08-04 08:17:43,231 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:43,232 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:43,232 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:43,232 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:43,232 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2'])] must_not=None, Payload={}
2025-08-04 08:17:43,243 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 4 matching docs in rbi_master_direction
2025-08-04 08:17:43,259 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3200 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:17:43,259 - knowledge_base_executor - INFO - ✅ Successfully updated 4 documents in collection: rbi_master_direction
2025-08-04 08:17:43,260 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:43,260 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:17:43,260 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2']}
2025-08-04 08:17:43,260 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:43,260 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 1a281f72-acde-49f1-9f3a-0cda45df7f8b
2025-08-04 08:17:43,260 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:43,260 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:43,260 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:43,260 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:43,260 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2'])] must_not=None, Payload={}
2025-08-04 08:17:43,262 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:17:43,262 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:17:43,262 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:17:43,262 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:17:43,262 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2']}
2025-08-04 08:17:43,262 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:17:43,262 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 1a281f72-acde-49f1-9f3a-0cda45df7f8b
2025-08-04 08:17:43,262 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:17:43,262 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:17:43,262 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:17:43,262 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:17:43,262 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2'])] must_not=None, Payload={}
2025-08-04 08:17:43,264 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:17:43,265 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:17:43,265 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T02:47:43.265080"}
2025-08-04 08:17:43,265 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:17:43,265 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:17:43,265 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:17:43,266 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:17:43,266 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Foreign Currency Accounts by a person resident in India_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:43,266 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Foreign Currency Accounts by a person resident in India_ _Fifth Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:43,266 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:17:43,266 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=cce8656f-e47e-4ee4-b229-e3a08f305fa3, Document=None, Chunk=0
2025-08-04 08:17:43,266 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 4764 chars
2025-08-04 08:17:45,328 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: cce8656f-e47e-4ee4-b229-e3a08f305fa3)
2025-08-04 08:17:45,328 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: cce8656f-e47e-4ee4-b229-e3a08f305fa3) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:47:45.328622", "document_id": null, "uuid": "cce8656f-e47e-4ee4-b229-e3a08f305fa3", "collection": "rbi_circular"}
2025-08-04 08:17:45,328 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:17:45,328 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:17:45,329 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:17:54,907 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:17:54,907 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Manner of Receipt and Payment_ _Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:54,908 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Manner of Receipt and Payment_ _Amendment_ Regulations_ 2025.pdf
2025-08-04 08:17:54,908 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:17:54,908 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=262f30a8-899c-41ed-979c-32dde764d0c4, Document=, Chunk=None
2025-08-04 08:17:54,908 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for  with text: 8000 chars
2025-08-04 08:17:58,686 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for  (ID: 262f30a8-899c-41ed-979c-32dde764d0c4)
2025-08-04 08:17:58,686 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document  (ID: 262f30a8-899c-41ed-979c-32dde764d0c4) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:47:58.686890", "document_id": "", "uuid": "262f30a8-899c-41ed-979c-32dde764d0c4", "collection": "rbi_other"}
2025-08-04 08:17:58,686 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:17:58,686 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:17:58,687 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:18:06,032 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:18:06,032 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Manner of Receipt and Payment_ _Amendment_ Regulations_ 2025.pdf
2025-08-04 08:18:06,032 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Manner of Receipt and Payment_ _Amendment_ Regulations_ 2025.pdf
2025-08-04 08:18:06,033 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:18:06,033 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:18:06,104 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:18:06,104 - knowledge_base_executor - INFO - [QDRANT] Match: ID=4700aab3-5bc9-5aac-a983-e08bd853f0bb, Score=0.8808, Document=FEMA 23(R)/2015-RB
2025-08-04 08:18:06,104 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d0e725b-aeac-5e6b-80ff-92fe03d7caf2, Score=0.8808, Document=FEMA 23(R)/2015-RB
2025-08-04 08:18:06,104 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06f77fc3-7228-514f-8562-24519f5d9d4a, Score=0.8808, Document=FEMA 23(R)/2015-RB
2025-08-04 08:18:06,104 - knowledge_base_executor - INFO - [QDRANT] Match: ID=81ccb53b-4614-5c94-a95b-f02af834fc91, Score=0.8808, Document=FEMA 23(R)/2015-RB
2025-08-04 08:18:06,104 - knowledge_base_executor - INFO - [QDRANT] Match: ID=709094ad-5569-562d-bfef-a7c7fe49438c, Score=0.8808, Document=FEMA 23(R)/2015-RB
2025-08-04 08:18:06,104 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:18:06,104 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:18:06,104 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'FEMA 14(R)/2023-RB' → 'FEMA14(R)/2023-RB'
2025-08-04 08:18:06,105 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: FEMA14(R)/2023-RB
2025-08-04 08:18:06,105 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:18:06,105 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:18:06,105 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:18:06,105 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:18:06,105 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:18:06,105 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:18:06,105 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:18:06,105 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:18:06,118 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-04 08:18:06,142 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3201 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:18:06,142 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-04 08:18:06,142 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:18:06,142 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:18:06,142 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:18:06,142 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:18:06,142 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:18:06,142 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:18:06,142 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:18:06,142 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:18:06,142 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:18:06,142 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:18:06,144 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:18:06,144 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:18:06,144 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:18:06,144 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:18:06,144 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:18:06,144 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:18:06,144 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:18:06,144 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:18:06,144 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:18:06,144 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a']...
2025-08-04 08:18:06,145 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:18:06,145 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:18:06,146 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:18:06,146 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:18:06,146 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T02:48:06.146611"}
2025-08-04 08:18:06,146 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:18:06,146 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:18:06,147 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:18:06,147 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:18:06,148 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Manner of Receipt and Payment_ _Amendment_ Regulations_ 2025.pdf
2025-08-04 08:18:06,148 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Manner of Receipt and Payment_ _Amendment_ Regulations_ 2025.pdf
2025-08-04 08:18:06,148 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:18:06,148 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=9ee97581-10e6-4a90-9a2a-16c3aa840a8a, Document=None, Chunk=0
2025-08-04 08:18:06,148 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 4113 chars
2025-08-04 08:18:11,012 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 9ee97581-10e6-4a90-9a2a-16c3aa840a8a)
2025-08-04 08:18:11,012 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 9ee97581-10e6-4a90-9a2a-16c3aa840a8a) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:48:11.012739", "document_id": null, "uuid": "9ee97581-10e6-4a90-9a2a-16c3aa840a8a", "collection": "rbi_circular"}
2025-08-04 08:18:11,012 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:18:11,012 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:18:11,013 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:18:19,155 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:18:19,155 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exposures of Scheduled Commercial Banks _SCBs_ to Non-Banking Financial Companies _NBFCs_ _ Review of Risk Weights.pdf
2025-08-04 08:18:19,155 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exposures of Scheduled Commercial Banks _SCBs_ to Non-Banking Financial Companies _NBFCs_ _ Review of Risk Weights.pdf
2025-08-04 08:18:19,156 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:18:19,156 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=cbfc7f63-673a-5fd1-bf0b-066164cc0ad8, Document=RBI/2024-25/120 - DOR.STR.REC.61/21.06.001/2024-25, Chunk=None
2025-08-04 08:18:19,156 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/120 - DOR.STR.REC.61/21.06.001/2024-25 with text: 8000 chars
2025-08-04 08:18:22,616 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/120 - DOR.STR.REC.61/21.06.001/2024-25 (ID: cbfc7f63-673a-5fd1-bf0b-066164cc0ad8)
2025-08-04 08:18:22,616 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/120 - DOR.STR.REC.61/21.06.001/2024-25 (ID: cbfc7f63-673a-5fd1-bf0b-066164cc0ad8) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:48:22.616757", "document_id": "RBI/2024-25/120 - DOR.STR.REC.61/21.06.001/2024-25", "uuid": "cbfc7f63-673a-5fd1-bf0b-066164cc0ad8", "collection": "rbi_other"}
2025-08-04 08:18:22,616 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:18:22,616 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:18:22,617 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:18:35,575 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:18:35,576 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exposures of Scheduled Commercial Banks _SCBs_ to Non-Banking Financial Companies _NBFCs_ _ Review of Risk Weights.pdf
2025-08-04 08:18:35,576 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exposures of Scheduled Commercial Banks _SCBs_ to Non-Banking Financial Companies _NBFCs_ _ Review of Risk Weights.pdf
2025-08-04 08:18:35,576 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:18:35,576 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:18:35,673 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:18:35,673 - knowledge_base_executor - INFO - [QDRANT] Match: ID=87ce2058-92a1-49bf-8379-39f5f41a4f7c, Score=0.9594, Document=RBI/2024-25/124
2025-08-04 08:18:35,673 - knowledge_base_executor - INFO - [QDRANT] Match: ID=595f060b-0c29-4a88-83c1-3665b14b0e92, Score=0.9594, Document=RBI/2024-25/124
2025-08-04 08:18:35,673 - knowledge_base_executor - INFO - [QDRANT] Match: ID=eca85fa5-0a47-5743-8ad2-81d3e4a2496c, Score=0.9594, Document=RBI/2024-25/124
2025-08-04 08:18:35,673 - knowledge_base_executor - INFO - [QDRANT] Match: ID=01e29e49-395a-409f-8599-2b5809595c10, Score=0.9566, Document=RBI/2024-25/129
2025-08-04 08:18:35,673 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0054771a-f489-4388-a332-3f4cad776891, Score=0.9566, Document=RBI/2024-25/129
2025-08-04 08:18:35,673 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:18:35,673 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:18:35,673 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: RBI/2024-25/120
2025-08-04 08:18:35,673 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['87ce2058-92a1-49bf-8379-39f5f41a4f7c', '595f060b-0c29-4a88-83c1-3665b14b0e92', 'eca85fa5-0a47-5743-8ad2-81d3e4a2496c', '01e29e49-395a-409f-8599-2b5809595c10', '0054771a-f489-4388-a332-3f4cad776891']}
2025-08-04 08:18:35,673 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:18:35,673 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 87ce2058-92a1-49bf-8379-39f5f41a4f7c
2025-08-04 08:18:35,673 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 595f060b-0c29-4a88-83c1-3665b14b0e92
2025-08-04 08:18:35,673 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = eca85fa5-0a47-5743-8ad2-81d3e4a2496c
2025-08-04 08:18:35,673 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['87ce2058-92a1-49bf-8379-39f5f41a4f7c', '595f060b-0c29-4a88-83c1-3665b14b0e92', 'eca85fa5-0a47-5743-8ad2-81d3e4a2496c']...
2025-08-04 08:18:35,673 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:18:35,674 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['87ce2058-92a1-49bf-8379-39f5f41a4f7c', '595f060b-0c29-4a88-83c1-3665b14b0e92', 'eca85fa5-0a47-5743-8ad2-81d3e4a2496c', '01e29e49-395a-409f-8599-2b5809595c10', '0054771a-f489-4388-a332-3f4cad776891'])] must_not=None, Payload={}
2025-08-04 08:18:35,676 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:18:35,676 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:18:35,676 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:18:35,676 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:18:35,676 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['87ce2058-92a1-49bf-8379-39f5f41a4f7c', '595f060b-0c29-4a88-83c1-3665b14b0e92', 'eca85fa5-0a47-5743-8ad2-81d3e4a2496c', '01e29e49-395a-409f-8599-2b5809595c10', '0054771a-f489-4388-a332-3f4cad776891']}
2025-08-04 08:18:35,676 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:18:35,676 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 87ce2058-92a1-49bf-8379-39f5f41a4f7c
2025-08-04 08:18:35,676 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 595f060b-0c29-4a88-83c1-3665b14b0e92
2025-08-04 08:18:35,676 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = eca85fa5-0a47-5743-8ad2-81d3e4a2496c
2025-08-04 08:18:35,676 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['87ce2058-92a1-49bf-8379-39f5f41a4f7c', '595f060b-0c29-4a88-83c1-3665b14b0e92', 'eca85fa5-0a47-5743-8ad2-81d3e4a2496c']...
2025-08-04 08:18:35,676 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:18:35,676 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['87ce2058-92a1-49bf-8379-39f5f41a4f7c', '595f060b-0c29-4a88-83c1-3665b14b0e92', 'eca85fa5-0a47-5743-8ad2-81d3e4a2496c', '01e29e49-395a-409f-8599-2b5809595c10', '0054771a-f489-4388-a332-3f4cad776891'])] must_not=None, Payload={}
2025-08-04 08:18:35,678 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:18:35,678 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:18:35,678 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:18:35,678 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:18:35,678 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['87ce2058-92a1-49bf-8379-39f5f41a4f7c', '595f060b-0c29-4a88-83c1-3665b14b0e92', 'eca85fa5-0a47-5743-8ad2-81d3e4a2496c', '01e29e49-395a-409f-8599-2b5809595c10', '0054771a-f489-4388-a332-3f4cad776891']}
2025-08-04 08:18:35,678 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:18:35,678 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 87ce2058-92a1-49bf-8379-39f5f41a4f7c
2025-08-04 08:18:35,678 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 595f060b-0c29-4a88-83c1-3665b14b0e92
2025-08-04 08:18:35,678 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = eca85fa5-0a47-5743-8ad2-81d3e4a2496c
2025-08-04 08:18:35,678 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['87ce2058-92a1-49bf-8379-39f5f41a4f7c', '595f060b-0c29-4a88-83c1-3665b14b0e92', 'eca85fa5-0a47-5743-8ad2-81d3e4a2496c']...
2025-08-04 08:18:35,678 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:18:35,678 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['87ce2058-92a1-49bf-8379-39f5f41a4f7c', '595f060b-0c29-4a88-83c1-3665b14b0e92', 'eca85fa5-0a47-5743-8ad2-81d3e4a2496c', '01e29e49-395a-409f-8599-2b5809595c10', '0054771a-f489-4388-a332-3f4cad776891'])] must_not=None, Payload={}
2025-08-04 08:18:35,680 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:18:35,680 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:18:35,680 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: No matching documents in rbi_master_direction; No matching documents in rbi_master_circular; No matching documents in rbi_circular", "timestamp": "2025-08-04T02:48:35.680355"}
2025-08-04 08:18:35,680 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-04 08:18:35,680 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-04 08:18:35,681 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:18:35,681 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: NO_ACTION
2025-08-04 08:18:35,681 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exposures of Scheduled Commercial Banks _SCBs_ to Non-Banking Financial Companies _NBFCs_ _ Review of Risk Weights.pdf
2025-08-04 08:18:35,681 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exposures of Scheduled Commercial Banks _SCBs_ to Non-Banking Financial Companies _NBFCs_ _ Review of Risk Weights.pdf
2025-08-04 08:18:35,681 - knowledge_base_executor - INFO - [QDRANT] Action details: NO_ACTION
2025-08-04 08:18:35,682 - knowledge_base_executor - INFO - [QDRANT] Action NO_ACTION executed with result: {"action": "NO_ACTION", "success": true, "details": "No action executed.", "timestamp": "2025-08-04T02:48:35.682011"}
2025-08-04 08:18:35,682 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:18:35,682 - knowledge_base_executor - INFO - [QDRANT] Action 1: NO_ACTION - ✅ SUCCESS
2025-08-04 08:18:35,682 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:18:35,683 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:18:35,683 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exposures of Scheduled Commercial Banks _SCBs_ to Non-Banking Financial Companies _NBFCs_ _ Review of Risk Weights.pdf
2025-08-04 08:18:35,683 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exposures of Scheduled Commercial Banks _SCBs_ to Non-Banking Financial Companies _NBFCs_ _ Review of Risk Weights.pdf
2025-08-04 08:18:35,683 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:18:35,683 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=39b75b0c-a5aa-4dbb-b9da-5abdf925383d, Document=None, Chunk=0
2025-08-04 08:18:35,683 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 7816 chars
2025-08-04 08:18:37,191 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 39b75b0c-a5aa-4dbb-b9da-5abdf925383d)
2025-08-04 08:18:37,191 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 39b75b0c-a5aa-4dbb-b9da-5abdf925383d) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:48:37.191525", "document_id": null, "uuid": "39b75b0c-a5aa-4dbb-b9da-5abdf925383d", "collection": "rbi_circular"}
2025-08-04 08:18:37,191 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:18:37,191 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:18:37,192 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:18:48,359 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:18:48,359 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:18:48,359 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:18:48,360 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:18:48,360 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=9734d481-ce1c-4747-86dc-b781fa5ed066, Document=, Chunk=None
2025-08-04 08:18:48,360 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for  with text: 8000 chars
2025-08-04 08:18:51,969 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for  (ID: 9734d481-ce1c-4747-86dc-b781fa5ed066)
2025-08-04 08:18:51,969 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document  (ID: 9734d481-ce1c-4747-86dc-b781fa5ed066) to collections: ['rbi_other']", "timestamp": "2025-08-04T02:48:51.969697", "document_id": "", "uuid": "9734d481-ce1c-4747-86dc-b781fa5ed066", "collection": "rbi_other"}
2025-08-04 08:18:51,969 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:18:51,969 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:18:51,970 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:19:01,289 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:19:01,290 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:19:01,290 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:19:01,290 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:19:01,290 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:19:01,363 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:19:01,363 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d0e725b-aeac-5e6b-80ff-92fe03d7caf2, Score=0.8271, Document=FEMA 23(R)/2015-RB
2025-08-04 08:19:01,364 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06f77fc3-7228-514f-8562-24519f5d9d4a, Score=0.8271, Document=FEMA 23(R)/2015-RB
2025-08-04 08:19:01,364 - knowledge_base_executor - INFO - [QDRANT] Match: ID=4700aab3-5bc9-5aac-a983-e08bd853f0bb, Score=0.8271, Document=FEMA 23(R)/2015-RB
2025-08-04 08:19:01,364 - knowledge_base_executor - INFO - [QDRANT] Match: ID=81ccb53b-4614-5c94-a95b-f02af834fc91, Score=0.8271, Document=FEMA 23(R)/2015-RB
2025-08-04 08:19:01,364 - knowledge_base_executor - INFO - [QDRANT] Match: ID=709094ad-5569-562d-bfef-a7c7fe49438c, Score=0.8271, Document=FEMA 23(R)/2015-RB
2025-08-04 08:19:01,364 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:19:01,364 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:19:01,364 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: FEMA.395/2019-RB
2025-08-04 08:19:01,364 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:19:01,364 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:19:01,364 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:19:01,364 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:19:01,364 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:19:01,364 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb']...
2025-08-04 08:19:01,364 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:19:01,364 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:19:01,377 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-04 08:19:01,398 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3202 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:19:01,398 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-04 08:19:01,398 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:19:01,398 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:19:01,398 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:19:01,398 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:19:01,398 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:19:01,398 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:19:01,398 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:19:01,398 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb']...
2025-08-04 08:19:01,398 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:19:01,398 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:19:01,401 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:19:01,401 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:19:01,401 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:19:01,401 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:19:01,401 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c']}
2025-08-04 08:19:01,401 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:19:01,401 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:19:01,401 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f77fc3-7228-514f-8562-24519f5d9d4a
2025-08-04 08:19:01,401 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:19:01,401 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb']...
2025-08-04 08:19:01,401 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:19:01,401 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '06f77fc3-7228-514f-8562-24519f5d9d4a', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '81ccb53b-4614-5c94-a95b-f02af834fc91', '709094ad-5569-562d-bfef-a7c7fe49438c'])] must_not=None, Payload={}
2025-08-04 08:19:01,404 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:19:01,404 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:19:01,404 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T02:49:01.404258"}
2025-08-04 08:19:01,404 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:19:01,404 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:19:01,405 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:19:01,405 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:19:01,405 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:19:01,405 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:19:01,405 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:19:01,405 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=2172d971-9bb0-4080-8fee-4e5ed46968fc, Document=None, Chunk=0
2025-08-04 08:19:01,405 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 4621 chars
2025-08-04 08:19:05,525 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 2172d971-9bb0-4080-8fee-4e5ed46968fc)
2025-08-04 08:19:05,525 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 2172d971-9bb0-4080-8fee-4e5ed46968fc) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:49:05.525509", "document_id": null, "uuid": "2172d971-9bb0-4080-8fee-4e5ed46968fc", "collection": "rbi_circular"}
2025-08-04 08:19:05,525 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:19:05,525 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:19:05,526 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:19:10,940 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:19:10,940 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:19:10,940 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:19:10,940 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:19:10,940 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:19:10,985 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:19:10,985 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9dc02ea6-55eb-482e-b648-826e5c934100, Score=0.5022, Document=Guidelines on exchange traded interest rate derivatives Draft Circular for Quick Comments
2025-08-04 08:19:10,985 - knowledge_base_executor - INFO - [QDRANT] Match: ID=26b42780-eb17-4bce-9407-dde3dd6d4e31, Score=0.5022, Document=Guidelines on exchange traded interest rate derivatives Draft Circular for Quick Comments
2025-08-04 08:19:10,985 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0296041d-0bdc-464c-a4ba-321ea1df5969, Score=0.5022, Document=Guidelines on exchange traded interest rate derivatives Draft Circular for Quick Comments
2025-08-04 08:19:10,985 - knowledge_base_executor - INFO - [QDRANT] Match: ID=382b0cd6-86ff-49e4-8752-e203b42d53bb, Score=0.4585, Document=RBI/2016-17/17 DPSS.CO.PD.Mobile Banking.No./2/02.23.001/**************-08-04 08:19:10,985 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b7c3c700-5ae8-4131-a9bd-946806910c2c, Score=0.4585, Document=RBI/2016-17/17 DPSS.CO.PD.Mobile Banking.No./2/02.23.001/**************-08-04 08:19:10,985 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:19:10,985 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:19:10,985 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'Foreign Exchange Management (Deposit) Regulations, 2016' → 'ForeignExchangeManagement(Deposit)Regulations,2016'
2025-08-04 08:19:10,985 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: ForeignExchangeManagement(Deposit)Regulations,2016
2025-08-04 08:19:10,986 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c']}
2025-08-04 08:19:10,986 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:19:10,986 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9dc02ea6-55eb-482e-b648-826e5c934100
2025-08-04 08:19:10,986 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 26b42780-eb17-4bce-9407-dde3dd6d4e31
2025-08-04 08:19:10,986 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0296041d-0bdc-464c-a4ba-321ea1df5969
2025-08-04 08:19:10,986 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969']...
2025-08-04 08:19:10,986 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:19:10,986 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c'])] must_not=None, Payload={}
2025-08-04 08:19:10,988 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:19:10,988 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:19:10,988 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:19:10,988 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:19:10,988 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c']}
2025-08-04 08:19:10,988 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:19:10,988 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9dc02ea6-55eb-482e-b648-826e5c934100
2025-08-04 08:19:10,988 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 26b42780-eb17-4bce-9407-dde3dd6d4e31
2025-08-04 08:19:10,988 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0296041d-0bdc-464c-a4ba-321ea1df5969
2025-08-04 08:19:10,988 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969']...
2025-08-04 08:19:10,988 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:19:10,989 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c'])] must_not=None, Payload={}
2025-08-04 08:19:10,998 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 2 matching docs in rbi_master_circular
2025-08-04 08:19:11,014 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8290 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:19:11,014 - knowledge_base_executor - INFO - ✅ Successfully updated 2 documents in collection: rbi_master_circular
2025-08-04 08:19:11,014 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:19:11,014 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:19:11,014 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c']}
2025-08-04 08:19:11,014 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:19:11,014 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9dc02ea6-55eb-482e-b648-826e5c934100
2025-08-04 08:19:11,014 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 26b42780-eb17-4bce-9407-dde3dd6d4e31
2025-08-04 08:19:11,014 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0296041d-0bdc-464c-a4ba-321ea1df5969
2025-08-04 08:19:11,014 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969']...
2025-08-04 08:19:11,014 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:19:11,014 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c'])] must_not=None, Payload={}
2025-08-04 08:19:11,024 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 3 matching docs in rbi_circular
2025-08-04 08:19:11,037 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13059 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:19:11,037 - knowledge_base_executor - INFO - ✅ Successfully updated 3 documents in collection: rbi_circular
2025-08-04 08:19:11,037 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:49:11.037203"}
2025-08-04 08:19:11,037 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:19:11,037 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:19:11,038 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:19:11,038 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:19:11,038 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:19:11,038 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:19:11,038 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:19:11,038 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=387525ae-ac8a-43c2-a787-ce02548e67f7, Document=None, Chunk=1
2025-08-04 08:19:11,039 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 4870 chars
2025-08-04 08:19:13,294 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 387525ae-ac8a-43c2-a787-ce02548e67f7)
2025-08-04 08:19:13,294 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 387525ae-ac8a-43c2-a787-ce02548e67f7) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:49:13.294283", "document_id": null, "uuid": "387525ae-ac8a-43c2-a787-ce02548e67f7", "collection": "rbi_circular"}
2025-08-04 08:19:13,294 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:19:13,294 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:19:13,294 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:19:25,562 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:19:25,562 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:19:25,562 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:19:25,563 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:19:25,563 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9dc02ea6-55eb-482e-b648-826e5c934100, Score=0.5022, Document=Guidelines on exchange traded interest rate derivatives Draft Circular for Quick Comments
2025-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] Match: ID=26b42780-eb17-4bce-9407-dde3dd6d4e31, Score=0.5022, Document=Guidelines on exchange traded interest rate derivatives Draft Circular for Quick Comments
2025-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0296041d-0bdc-464c-a4ba-321ea1df5969, Score=0.5022, Document=Guidelines on exchange traded interest rate derivatives Draft Circular for Quick Comments
2025-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] Match: ID=382b0cd6-86ff-49e4-8752-e203b42d53bb, Score=0.4585, Document=RBI/2016-17/17 DPSS.CO.PD.Mobile Banking.No./2/02.23.001/**************-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b7c3c700-5ae8-4131-a9bd-946806910c2c, Score=0.4585, Document=RBI/2016-17/17 DPSS.CO.PD.Mobile Banking.No./2/02.23.001/**************-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'Foreign Exchange Management (Deposit) Regulations, 2016' → 'ForeignExchangeManagement(Deposit)Regulations,2016'
2025-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: ForeignExchangeManagement(Deposit)Regulations,2016
2025-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c']}
2025-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9dc02ea6-55eb-482e-b648-826e5c934100
2025-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 26b42780-eb17-4bce-9407-dde3dd6d4e31
2025-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0296041d-0bdc-464c-a4ba-321ea1df5969
2025-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969']...
2025-08-04 08:19:25,641 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:19:25,642 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c'])] must_not=None, Payload={}
2025-08-04 08:19:25,646 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:19:25,646 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:19:25,646 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:19:25,646 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:19:25,646 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c']}
2025-08-04 08:19:25,646 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:19:25,646 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9dc02ea6-55eb-482e-b648-826e5c934100
2025-08-04 08:19:25,647 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 26b42780-eb17-4bce-9407-dde3dd6d4e31
2025-08-04 08:19:25,647 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0296041d-0bdc-464c-a4ba-321ea1df5969
2025-08-04 08:19:25,647 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969']...
2025-08-04 08:19:25,647 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:19:25,647 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c'])] must_not=None, Payload={}
2025-08-04 08:19:25,653 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 2 matching docs in rbi_master_circular
2025-08-04 08:19:25,665 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8291 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:19:25,665 - knowledge_base_executor - INFO - ✅ Successfully updated 2 documents in collection: rbi_master_circular
2025-08-04 08:19:25,665 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:19:25,665 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:19:25,665 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c']}
2025-08-04 08:19:25,665 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:19:25,665 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9dc02ea6-55eb-482e-b648-826e5c934100
2025-08-04 08:19:25,665 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 26b42780-eb17-4bce-9407-dde3dd6d4e31
2025-08-04 08:19:25,665 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0296041d-0bdc-464c-a4ba-321ea1df5969
2025-08-04 08:19:25,665 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969']...
2025-08-04 08:19:25,665 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:19:25,665 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c'])] must_not=None, Payload={}
2025-08-04 08:19:25,672 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 3 matching docs in rbi_circular
2025-08-04 08:19:25,684 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13061 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:19:25,684 - knowledge_base_executor - INFO - ✅ Successfully updated 3 documents in collection: rbi_circular
2025-08-04 08:19:25,684 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:49:25.684824"}
2025-08-04 08:19:25,685 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:19:25,685 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:19:25,687 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:19:25,687 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:19:25,687 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:19:25,687 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:19:25,687 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:19:25,687 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:19:25,703 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:19:25,703 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9dc02ea6-55eb-482e-b648-826e5c934100, Score=0.5022, Document=Guidelines on exchange traded interest rate derivatives Draft Circular for Quick Comments
2025-08-04 08:19:25,703 - knowledge_base_executor - INFO - [QDRANT] Match: ID=26b42780-eb17-4bce-9407-dde3dd6d4e31, Score=0.5022, Document=Guidelines on exchange traded interest rate derivatives Draft Circular for Quick Comments
2025-08-04 08:19:25,703 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0296041d-0bdc-464c-a4ba-321ea1df5969, Score=0.5022, Document=Guidelines on exchange traded interest rate derivatives Draft Circular for Quick Comments
2025-08-04 08:19:25,703 - knowledge_base_executor - INFO - [QDRANT] Match: ID=382b0cd6-86ff-49e4-8752-e203b42d53bb, Score=0.4585, Document=RBI/2016-17/17 DPSS.CO.PD.Mobile Banking.No./2/02.23.001/**************-08-04 08:19:25,704 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b7c3c700-5ae8-4131-a9bd-946806910c2c, Score=0.4585, Document=RBI/2016-17/17 DPSS.CO.PD.Mobile Banking.No./2/02.23.001/**************-08-04 08:19:25,704 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:19:25,704 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:19:25,704 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'Foreign Exchange Management (Deposit) Regulations, 2016' → 'ForeignExchangeManagement(Deposit)Regulations,2016'
2025-08-04 08:19:25,704 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: ForeignExchangeManagement(Deposit)Regulations,2016
2025-08-04 08:19:25,704 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c']}
2025-08-04 08:19:25,704 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:19:25,704 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9dc02ea6-55eb-482e-b648-826e5c934100
2025-08-04 08:19:25,704 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 26b42780-eb17-4bce-9407-dde3dd6d4e31
2025-08-04 08:19:25,704 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0296041d-0bdc-464c-a4ba-321ea1df5969
2025-08-04 08:19:25,704 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969']...
2025-08-04 08:19:25,704 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:19:25,704 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c'])] must_not=None, Payload={}
2025-08-04 08:19:25,706 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:19:25,706 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:19:25,706 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:19:25,706 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:19:25,706 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c']}
2025-08-04 08:19:25,706 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:19:25,706 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9dc02ea6-55eb-482e-b648-826e5c934100
2025-08-04 08:19:25,706 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 26b42780-eb17-4bce-9407-dde3dd6d4e31
2025-08-04 08:19:25,707 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0296041d-0bdc-464c-a4ba-321ea1df5969
2025-08-04 08:19:25,707 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969']...
2025-08-04 08:19:25,707 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:19:25,707 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c'])] must_not=None, Payload={}
2025-08-04 08:19:25,711 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 2 matching docs in rbi_master_circular
2025-08-04 08:19:25,720 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8292 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:19:25,720 - knowledge_base_executor - INFO - ✅ Successfully updated 2 documents in collection: rbi_master_circular
2025-08-04 08:19:25,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:19:25,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:19:25,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c']}
2025-08-04 08:19:25,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:19:25,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9dc02ea6-55eb-482e-b648-826e5c934100
2025-08-04 08:19:25,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 26b42780-eb17-4bce-9407-dde3dd6d4e31
2025-08-04 08:19:25,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0296041d-0bdc-464c-a4ba-321ea1df5969
2025-08-04 08:19:25,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969']...
2025-08-04 08:19:25,720 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:19:25,720 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9dc02ea6-55eb-482e-b648-826e5c934100', '26b42780-eb17-4bce-9407-dde3dd6d4e31', '0296041d-0bdc-464c-a4ba-321ea1df5969', '382b0cd6-86ff-49e4-8752-e203b42d53bb', 'b7c3c700-5ae8-4131-a9bd-946806910c2c'])] must_not=None, Payload={}
2025-08-04 08:19:25,725 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 3 matching docs in rbi_circular
2025-08-04 08:19:25,735 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13062 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:19:25,735 - knowledge_base_executor - INFO - ✅ Successfully updated 3 documents in collection: rbi_circular
2025-08-04 08:19:25,735 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T02:49:25.735312"}
2025-08-04 08:19:25,735 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:19:25,735 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:19:25,736 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:19:25,736 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: NO_ACTION
2025-08-04 08:19:25,736 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:19:25,736 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:19:25,736 - knowledge_base_executor - INFO - [QDRANT] Action details: NO_ACTION
2025-08-04 08:19:25,736 - knowledge_base_executor - INFO - [QDRANT] Action NO_ACTION executed with result: {"action": "NO_ACTION", "success": true, "details": "No action executed.", "timestamp": "2025-08-04T02:49:25.736648"}
2025-08-04 08:19:25,736 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:19:25,736 - knowledge_base_executor - INFO - [QDRANT] Action 1: NO_ACTION - ✅ SUCCESS
2025-08-04 08:19:25,737 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:19:25,737 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:19:25,737 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:19:25,737 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Foreign Exchange Management _Mode of Payment and Reporting of Non-Debt Instruments_ _Third Amendment_ Regulations_ 2025.pdf
2025-08-04 08:19:25,737 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:19:25,737 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=320bc042-c439-4396-9fbc-f4f1ef527838, Document=None, Chunk=2
2025-08-04 08:19:25,737 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 4315 chars
2025-08-04 08:19:27,948 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 320bc042-c439-4396-9fbc-f4f1ef527838)
2025-08-04 08:19:27,948 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 320bc042-c439-4396-9fbc-f4f1ef527838) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:49:27.948753", "document_id": null, "uuid": "320bc042-c439-4396-9fbc-f4f1ef527838", "collection": "rbi_circular"}
2025-08-04 08:19:27,948 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:19:27,948 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:19:27,949 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:19:36,567 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:19:36,568 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/General Notification for Sale and Issue of Government of India Securities _including Treasury Bills and Cash Management Bills_.pdf
2025-08-04 08:19:36,568 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/General Notification for Sale and Issue of Government of India Securities _including Treasury Bills and Cash Management Bills_.pdf
2025-08-04 08:19:36,568 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:19:36,568 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=49e02f10-a2d2-5633-84b0-6acdc512fd33, Document=RBI/2024-25/133 - Ref.No.IDMD.2320/08.01.01/2024-25, Chunk=None
2025-08-04 08:19:36,568 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/133 - Ref.No.IDMD.2320/08.01.01/2024-25 with text: 7207 chars
2025-08-04 08:19:40,649 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/133 - Ref.No.IDMD.2320/08.01.01/2024-25 (ID: 49e02f10-a2d2-5633-84b0-6acdc512fd33)
2025-08-04 08:19:40,649 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/133 - Ref.No.IDMD.2320/08.01.01/2024-25 (ID: 49e02f10-a2d2-5633-84b0-6acdc512fd33) to collections: ['rbi_circular']", "timestamp": "2025-08-04T02:49:40.649420", "document_id": "RBI/2024-25/133 - Ref.No.IDMD.2320/08.01.01/2024-25", "uuid": "49e02f10-a2d2-5633-84b0-6acdc512fd33", "collection": "rbi_circular"}
2025-08-04 08:19:40,649 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:19:40,649 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:19:40,650 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
