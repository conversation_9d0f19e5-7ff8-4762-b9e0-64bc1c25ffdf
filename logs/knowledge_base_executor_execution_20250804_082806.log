2025-08-04 08:28:06,673 - knowledge_base_executor - <PERSON><PERSON><PERSON> - <PERSON><PERSON> configured to write logs to: logs/knowledge_base_executor_execution_20250804_082806.log
2025-08-04 08:28:06,673 - knowledge_base_executor - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-04 08:28:10,319 - knowledge_base_executor - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-04 08:28:10,319 - knowledge_base_executor - INFO - 🗑️ [QDRANT] Executing REMOVE_DOCUMENT action: {"action_type": "REMOVE_DOCUMENT", "target_document": "MANUAL_REVIEW_REQUIRED", "collection_name": ["rbi_circular"], "use_vector_search": true, "document_number": "Test Document Number"}
2025-08-04 08:28:10,319 - knowledge_base_executor - INFO - 🔍 [QDRANT] Target collections for removal: ['rbi_circular']
2025-08-04 08:28:10,319 - knowledge_base_executor - INFO - [QDRANT] 🔍 Trying to find documents to remove using vector search
2025-08-04 08:28:10,319 - knowledge_base_executor - INFO - [QDRANT] 🔍 Search parameters: document_id='MANUAL_REVIEW_REQUIRED', document_number='Test Document Number'
2025-08-04 08:28:13,521 - knowledge_base_executor - INFO - [QDRANT] 🔍 Searching across collections: ['rbi_circular']
2025-08-04 08:28:13,521 - knowledge_base_executor - INFO - [QDRANT] 🔍 SKIPPING DOCUMENT ID SEARCH (MANUAL_REVIEW_REQUIRED)
2025-08-04 08:28:13,521 - knowledge_base_executor - INFO - [QDRANT] 🔍 SEARCHING BY DOCUMENT NUMBER: 'Test Document Number'
2025-08-04 08:28:13,651 - knowledge_base_executor - INFO - [QDRANT] ✅ Found 10 matches by document number
2025-08-04 08:28:13,651 - knowledge_base_executor - INFO - [QDRANT] 📋 DOCUMENT NUMBER SEARCH RESULTS:
2025-08-04 08:28:13,651 - knowledge_base_executor - INFO - [QDRANT]    1. ID=8a26f3b3-4845-4e05-bca1-cebfaf86353a | Score=0.3617 | DocID=RBI/2024-25/120 - DOR.STR.REC.61/21.06.001/2024-25 | DocNum=unknown
2025-08-04 08:28:13,651 - knowledge_base_executor - INFO - [QDRANT]    2. ID=0bceb959-f4eb-4ac7-879c-4a70923d6068 | Score=0.3359 | DocID=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 | DocNum=unknown
2025-08-04 08:28:13,651 - knowledge_base_executor - INFO - [QDRANT]    3. ID=ceffd7c8-ae16-4b9e-a0c0-350eb45eeca7 | Score=0.3359 | DocID=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 | DocNum=unknown
2025-08-04 08:28:13,651 - knowledge_base_executor - INFO - [QDRANT]    4. ID=2638f94b-0bc4-4c51-b2c2-aae911025668 | Score=0.3359 | DocID=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 | DocNum=unknown
2025-08-04 08:28:13,651 - knowledge_base_executor - INFO - [QDRANT]    5. ID=4f710668-d147-4c88-8239-ecee38118347 | Score=0.3359 | DocID=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 | DocNum=unknown
2025-08-04 08:28:13,651 - knowledge_base_executor - INFO - [QDRANT]    6. ID=bd079189-c0d9-449a-b5a2-61767d8aca33 | Score=0.3195 | DocID=RBI/2025-26/65 - DOR.STR.REC.39/21.06.008/2025-26 | DocNum=unknown
2025-08-04 08:28:13,651 - knowledge_base_executor - INFO - [QDRANT]    7. ID=45e1d982-dbae-4bda-9493-52ab74735756 | Score=0.3163 | DocID=DoR.RET.REC.12/12.01.001/2024-25 | DocNum=unknown
2025-08-04 08:28:13,651 - knowledge_base_executor - INFO - [QDRANT]    8. ID=7c96648b-63c9-484c-a5fe-d553b0d8f4e5 | Score=0.3100 | DocID=RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 | DocNum=unknown
2025-08-04 08:28:13,651 - knowledge_base_executor - INFO - [QDRANT]    9. ID=6ff8163a-0027-412b-a237-b343acbc7037 | Score=0.3100 | DocID=RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 | DocNum=unknown
2025-08-04 08:28:13,651 - knowledge_base_executor - INFO - [QDRANT]    10. ID=78e63424-31da-4ff2-a793-3897c8a0ce69 | Score=0.3037 | DocID=DOR.CRE.REC.No.06/08.12.001/2023-24 | DocNum=unknown
2025-08-04 08:28:13,651 - knowledge_base_executor - INFO - [QDRANT] ➕ Added 10 new unique matches from document number search
2025-08-04 08:28:13,651 - knowledge_base_executor - INFO - [QDRANT] 🎯 Found total of 10 potential documents to remove via vector search
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT] 📊 FINAL SEARCH RESULTS SUMMARY:
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT] #1. Score: 0.3617 | ID: 8a26f3b3-4845-4e05-bca1-cebfaf86353a | DocID: RBI/2024-25/120 - DOR.STR.REC.61/21.06.001/2024-25
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Title: Exposures Of Scheduled Commercial Banks  Scbs  To Non-Banking Financial Companies  Nbfcs    Review Of Risk Weights
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Preview: <section data-bbox="[72.0, 18.**************, 219.*************, 39.*************]" data-page="1"><h...
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT] #2. Score: 0.3359 | ID: 0bceb959-f4eb-4ac7-879c-4a70923d6068 | DocID: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Preview: <p data-bbox="[60.***************, 70.*************, 486.*************, 138.**************]" data-pa...
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT] #3. Score: 0.3359 | ID: ceffd7c8-ae16-4b9e-a0c0-350eb45eeca7 | DocID: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Preview: <p data-bbox="[60.***************, 70.*************, 486.*************, 138.**************]" data-pa...
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT] #4. Score: 0.3359 | ID: 2638f94b-0bc4-4c51-b2c2-aae911025668 | DocID: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Preview: <p data-bbox="[60.***************, 70.*************, 486.*************, 138.**************]" data-pa...
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT] #5. Score: 0.3359 | ID: 4f710668-d147-4c88-8239-ecee38118347 | DocID: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Preview: <p data-bbox="[60.***************, 70.*************, 486.*************, 138.**************]" data-pa...
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT] #6. Score: 0.3195 | ID: bd079189-c0d9-449a-b5a2-61767d8aca33 | DocID: RBI/2025-26/65 - DOR.STR.REC.39/21.06.008/2025-26
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Title: Basel III Capital Regulations – External Credit Assessment Institutions (ECAIs) – CareEdge Global IFSC Limited
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Preview: <p data-bbox="[71.99996948242188, 106.**************, 526.8359985351562, 258.1798400878906]" data-pa...
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT] #7. Score: 0.3163 | ID: 45e1d982-dbae-4bda-9493-52ab74735756 | DocID: DoR.RET.REC.12/12.01.001/2024-25
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT] #8. Score: 0.3100 | ID: 7c96648b-63c9-484c-a5fe-d553b0d8f4e5 | DocID: RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Title: Change In Bank Rate
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Preview: <p data-bbox="[306.0, 41.***************, 308.*************, 56.***************]" data-page="1"></p>...
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT] #9. Score: 0.3100 | ID: 6ff8163a-0027-412b-a237-b343acbc7037 | DocID: RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Title: Change In Bank Rate
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Preview: <p data-bbox="[306.0, 41.***************, 308.*************, 56.***************]" data-page="1"></p>...
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT] #10. Score: 0.3037 | ID: 78e63424-31da-4ff2-a793-3897c8a0ce69 | DocID: DOR.CRE.REC.No.06/08.12.001/2023-24
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-04 08:28:13,652 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-04 08:28:13,652 - knowledge_base_executor - WARNING - [QDRANT] ⚠️ No similar matches found for MANUAL_REVIEW_REQUIRED, keeping original target
2025-08-04 08:28:13,653 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for REMOVE_DOCUMENT
2025-08-04 08:28:13,653 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action_type', 'target_document', 'collection_name', 'use_vector_search', 'document_number', 'document_id', 'filter_fields']
2025-08-04 08:28:13,653 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'metadata.document_id': 'MANUAL_REVIEW_REQUIRED'}
2025-08-04 08:28:13,653 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding filter: metadata.document_id = MANUAL_REVIEW_REQUIRED
2025-08-04 08:28:13,653 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding metadata filter: metadata.document_number = Test Document Number
2025-08-04 08:28:13,653 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Created filter with 2 conditions
2025-08-04 08:28:13,653 - knowledge_base_executor - INFO - [QDRANT] REMOVE: Collection=rbi_circular, Filter=should=None min_should=None must=[FieldCondition(key='metadata.document_id', match=MatchValue(value='MANUAL_REVIEW_REQUIRED'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None), FieldCondition(key='metadata.document_number', match=MatchValue(value='Test Document Number'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None)] must_not=None
2025-08-04 08:28:13,653 - knowledge_base_executor - INFO - [QDRANT] REMOVE filter fields: {"metadata.document_id": "MANUAL_REVIEW_REQUIRED"}
2025-08-04 08:28:13,735 - knowledge_base_executor - WARNING - [QDRANT] ⚠️ No documents found matching filter in rbi_circular
2025-08-04 08:28:13,735 - knowledge_base_executor - INFO - 🔍 [QDRANT] REMOVE operation completed with result: {"action": "REMOVE_DOCUMENT", "success": false, "details": "MANUAL_REVIEW_REQUIRED documents cannot be removed automatically", "timestamp": "2025-08-04T02:58:13.735734"}
