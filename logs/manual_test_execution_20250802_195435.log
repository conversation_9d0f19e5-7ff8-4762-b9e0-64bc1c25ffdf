2025-08-02 19:54:35,182 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250802_195435.log
2025-08-02 19:54:35,182 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250802_195435.log
2025-08-02 19:54:35,182 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-02 19:54:35,182 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-02 19:54:38,720 - utils.config - INFO - Configuration loaded successfully
2025-08-02 19:54:39,227 - prompts.notification_categorizer - INFO - Processed 0 chunks from content
2025-08-02 19:54:39,227 - prompts.notification_categorizer - INFO - Chunks: []
2025-08-02 19:54:39,228 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250802_195439.log
2025-08-02 19:54:39,228 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250802_195439.log
2025-08-02 19:54:39,228 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-02 19:54:39,228 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-02 19:54:39,240 - qdrant_utils - INFO - Configured Hugging Face HTTP backend to bypass SSL verification
2025-08-02 19:54:39,242 - config - INFO - Configuration loaded successfully
2025-08-02 19:54:40,605 - qdrant_utils - INFO - Loaded SPLADE model: naver/efficient-splade-VI-BT-large-doc
2025-08-02 19:54:40,605 - qdrant_utils - INFO - Initializing QdrantManager with URL: http://localhost:6333
2025-08-02 19:54:40,625 - httpx - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-02 19:54:40,631 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections "HTTP/1.1 200 OK"
2025-08-02 19:54:40,632 - qdrant_utils - INFO - ✅ QdrantClient initialized successfully
2025-08-02 19:54:41,540 - qdrant_utils - INFO - ✅ Sparse embedder initialized with model: Qdrant/bm25
2025-08-02 19:54:41,541 - qdrant_utils - INFO - 🔍 FastEmbed creates sparse vector type: <class 'langchain_qdrant.sparse_embeddings.SparseVector'>
2025-08-02 19:54:41,541 - qdrant_utils - INFO - 🔍 FastEmbed sparse vector indices count: 1
2025-08-02 19:54:41,556 - manual_test - INFO - 🚀 Starting Real Pipeline Test with Enhanced KB Storage...
2025-08-02 19:54:41,556 - manual_test - INFO - 🚀 Starting Real Pipeline Test with Enhanced KB Storage...
2025-08-02 19:54:41,556 - manual_test - INFO - 📄 Using notifications file: rbi_notifications.json
2025-08-02 19:54:41,556 - manual_test - INFO - 📄 Using notifications file: rbi_notifications.json
2025-08-02 19:54:41,556 - manual_test - INFO - Initialized key rotator with 2 keys
2025-08-02 19:54:41,556 - manual_test - INFO - Initialized key rotator with 2 keys
2025-08-02 19:54:41,556 - manual_test - INFO - ✅ Environment setup complete
2025-08-02 19:54:41,556 - manual_test - INFO - ✅ Environment setup complete
2025-08-02 19:54:41,557 - manual_test - INFO - ✅ Real notification processor initialized
2025-08-02 19:54:41,557 - manual_test - INFO - ✅ Real notification processor initialized
2025-08-02 19:54:41,558 - knowledge_base_executor - INFO - Logger configured to write logs to: logs/knowledge_base_executor_execution_20250802_195441.log
2025-08-02 19:54:41,558 - knowledge_base_executor - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-02 19:54:41,571 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-08-02 19:54:41,571 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-02 19:54:45,295 - httpx - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-02 19:54:45,295 - utils.metadata_vector_utils - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-02 19:54:45,295 - knowledge_base_executor - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-02 19:54:45,296 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-08-02 19:54:45,296 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-02 19:54:49,115 - httpx - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-02 19:54:49,116 - utils.metadata_vector_utils - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-02 19:54:49,116 - manual_test - INFO - ✅ Metadata vector search initialized
2025-08-02 19:54:49,116 - manual_test - INFO - ✅ Metadata vector search initialized
2025-08-02 19:54:49,116 - manual_test - INFO - 🧠 Selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-02 19:54:49,116 - manual_test - INFO - 🧠 Selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-02 19:54:49,117 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-08-02 19:54:49,117 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-02 19:54:52,925 - httpx - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-02 19:54:52,925 - manual_test - INFO - ✅ Direct metadata search components initialized
2025-08-02 19:54:52,925 - manual_test - INFO - ✅ Direct metadata search components initialized
2025-08-02 19:54:52,925 - manual_test - INFO - 🚀 Starting ENHANCED pipeline test with comprehensive UUID tracking...
2025-08-02 19:54:52,925 - manual_test - INFO - 🚀 Starting ENHANCED pipeline test with comprehensive UUID tracking...
2025-08-02 19:54:52,925 - manual_test - INFO - 🚀 Starting ENHANCED pipeline test with up to 50 notifications...
2025-08-02 19:54:52,925 - manual_test - INFO - 🚀 Starting ENHANCED pipeline test with up to 50 notifications...
2025-08-02 19:54:52,926 - manual_test - INFO - 📂 Loaded 1 notifications from rbi_notifications.json
2025-08-02 19:54:52,926 - manual_test - INFO - 📂 Loaded 1 notifications from rbi_notifications.json
2025-08-02 19:54:52,926 - manual_test - INFO - 
2025-08-02 19:54:52,926 - manual_test - INFO - 
2025-08-02 19:54:52,926 - manual_test - INFO - 🔄 ═══════════════════════════════════════════════════════════════
2025-08-02 19:54:52,926 - manual_test - INFO - 🔄 ═══════════════════════════════════════════════════════════════
2025-08-02 19:54:52,926 - manual_test - INFO - 🔄 PROCESSING NOTIFICATION 1/1 (100.0%)
2025-08-02 19:54:52,926 - manual_test - INFO - 🔄 PROCESSING NOTIFICATION 1/1 (100.0%)
2025-08-02 19:54:52,926 - manual_test - INFO - 🔄 Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Cr...
2025-08-02 19:54:52,926 - manual_test - INFO - 🔄 Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Cr...
2025-08-02 19:54:52,926 - manual_test - INFO - 🔄 Date: 
2025-08-02 19:54:52,926 - manual_test - INFO - 🔄 Date: 
2025-08-02 19:54:52,926 - manual_test - INFO - 🔄 ═══════════════════════════════════════════════════════════════
2025-08-02 19:54:52,926 - manual_test - INFO - 🔄 ═══════════════════════════════════════════════════════════════
2025-08-02 19:54:52,926 - manual_test - INFO - 
2025-08-02 19:54:52,926 - manual_test - INFO - 
2025-08-02 19:54:52,928 - manual_test - WARNING - ⚠️ Primary code patterns failed, trying fallback extraction
2025-08-02 19:54:52,928 - manual_test - WARNING - ⚠️ Primary code patterns failed, trying fallback extraction
2025-08-02 19:54:52,928 - manual_test - WARNING - ⚠️ No codes extracted from text
2025-08-02 19:54:52,928 - manual_test - WARNING - ⚠️ No codes extracted from text
2025-08-02 19:54:52,928 - manual_test - INFO - 🚀 ENHANCED Processing notification: Master Circular - Guarantees  Co-Acceptances   Let...
2025-08-02 19:54:52,928 - manual_test - INFO - 🚀 ENHANCED Processing notification: Master Circular - Guarantees  Co-Acceptances   Let...
2025-08-02 19:54:52,929 - manual_test - INFO - 📊 Initial notification codes from title: {'short_code': '', 'long_code': '', 'full_code': '', 'year': '', 'all_codes': []}
2025-08-02 19:54:52,929 - manual_test - INFO - 📊 Initial notification codes from title: {'short_code': '', 'long_code': '', 'full_code': '', 'year': '', 'all_codes': []}
2025-08-02 19:54:52,929 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:54:52,929 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:54:53,016 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:54:53,016 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:54:53,205 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:54:53,205 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:54:53,212 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:54:53,212 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:54:53,221 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:54:53,221 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:54:53,221 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['UBD.PCB.No.59/13.05.000', 'BSD.No.IP.30/12.05.05', 'A.P. (DIR Series) Circular No. 18', 'Plan.PCB.CIR.07/09.27.00', '.IP.30/12.05.05/2002-03', '.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000', '.REC.3/09.27.000/2024-25', 'BSD.No.IP.30/12.05.05/2002-03', 'UBD.PCB.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000/2008', '.No.36/13.05.000/2008-09', 'UBD.No.Plan.42/09.27.00']}
2025-08-02 19:54:53,221 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['UBD.PCB.No.59/13.05.000', 'BSD.No.IP.30/12.05.05', 'A.P. (DIR Series) Circular No. 18', 'Plan.PCB.CIR.07/09.27.00', '.IP.30/12.05.05/2002-03', '.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000', '.REC.3/09.27.000/2024-25', 'BSD.No.IP.30/12.05.05/2002-03', 'UBD.PCB.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000/2008', '.No.36/13.05.000/2008-09', 'UBD.No.Plan.42/09.27.00']}
2025-08-02 19:54:53,221 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:54:53,221 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:54:53,221 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:54:53,221 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:54:53,221 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:54:53,221 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:54:53,221 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:54:53,221 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:54:53,221 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:54:53,221 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:54:53,221 - manual_test - INFO - ✅ Extracted 49457 chars from local PDF
2025-08-02 19:54:53,221 - manual_test - INFO - ✅ Extracted 49457 chars from local PDF
2025-08-02 19:54:53,221 - manual_test - INFO - 📄 Extracting notification codes from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:54:53,221 - manual_test - INFO - 📄 Extracting notification codes from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:54:53,221 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:54:53,221 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:54:53,293 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:54:53,293 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:54:53,481 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:54:53,481 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:54:53,489 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:54:53,489 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:54:53,497 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:54:53,497 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:54:53,497 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['UBD.PCB.No.59/13.05.000', 'BSD.No.IP.30/12.05.05', 'A.P. (DIR Series) Circular No. 18', 'Plan.PCB.CIR.07/09.27.00', '.IP.30/12.05.05/2002-03', '.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000', '.REC.3/09.27.000/2024-25', 'BSD.No.IP.30/12.05.05/2002-03', 'UBD.PCB.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000/2008', '.No.36/13.05.000/2008-09', 'UBD.No.Plan.42/09.27.00']}
2025-08-02 19:54:53,497 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['UBD.PCB.No.59/13.05.000', 'BSD.No.IP.30/12.05.05', 'A.P. (DIR Series) Circular No. 18', 'Plan.PCB.CIR.07/09.27.00', '.IP.30/12.05.05/2002-03', '.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000', '.REC.3/09.27.000/2024-25', 'BSD.No.IP.30/12.05.05/2002-03', 'UBD.PCB.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000/2008', '.No.36/13.05.000/2008-09', 'UBD.No.Plan.42/09.27.00']}
2025-08-02 19:54:53,497 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:54:53,497 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:54:53,497 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:54:53,497 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:54:53,498 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:54:53,498 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:54:53,498 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:54:53,498 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:54:53,498 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:54:53,498 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:54:53,498 - manual_test - INFO - 📋 Enhanced codes after PDF extraction: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['UBD.PCB.No.59/13.05.000', 'BSD.No.IP.30/12.05.05', 'A.P. (DIR Series) Circular No. 18', 'Plan.PCB.CIR.07/09.27.00', '.IP.30/12.05.05/2002-03', '.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000', '.REC.3/09.27.000/2024-25', 'BSD.No.IP.30/12.05.05/2002-03', 'UBD.PCB.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000/2008', '.No.36/13.05.000/2008-09', 'UBD.No.Plan.42/09.27.00'], 'watermark_status': 'ACTIVE', 'is_active': True}
2025-08-02 19:54:53,498 - manual_test - INFO - 📋 Enhanced codes after PDF extraction: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['UBD.PCB.No.59/13.05.000', 'BSD.No.IP.30/12.05.05', 'A.P. (DIR Series) Circular No. 18', 'Plan.PCB.CIR.07/09.27.00', '.IP.30/12.05.05/2002-03', '.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000', '.REC.3/09.27.000/2024-25', 'BSD.No.IP.30/12.05.05/2002-03', 'UBD.PCB.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000/2008', '.No.36/13.05.000/2008-09', 'UBD.No.Plan.42/09.27.00'], 'watermark_status': 'ACTIVE', 'is_active': True}
2025-08-02 19:54:53,498 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:54:53,498 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:54:53,498 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:54:53,498 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:54:53,498 - manual_test - INFO -    📋 Full code: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:54:53,498 - manual_test - INFO -    📋 Full code: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:54:53,498 - manual_test - INFO -    📅 Year: 2025
2025-08-02 19:54:53,498 - manual_test - INFO -    📅 Year: 2025
2025-08-02 19:54:53,498 - manual_test - INFO -    🏷️ Watermark status: ACTIVE
2025-08-02 19:54:53,498 - manual_test - INFO -    🏷️ Watermark status: ACTIVE
2025-08-02 19:54:53,498 - manual_test - INFO - --- ENHANCED Stage 1: Notification Analysis ---
2025-08-02 19:54:53,498 - manual_test - INFO - --- ENHANCED Stage 1: Notification Analysis ---
2025-08-02 19:54:53,498 - manual_test - INFO - 🔍 Starting notification analysis for: Master Circular - Guarantees  Co-Acceptances   Let...
2025-08-02 19:54:53,498 - manual_test - INFO - 🔍 Starting notification analysis for: Master Circular - Guarantees  Co-Acceptances   Let...
2025-08-02 19:54:53,498 - manual_test - INFO - 📏 Notification length: 49457 characters
2025-08-02 19:54:53,498 - manual_test - INFO - 📏 Notification length: 49457 characters
2025-08-02 19:54:53,498 - manual_test - INFO - 📝 Prompt created, making LLM call...
2025-08-02 19:54:53,498 - manual_test - INFO - 📝 Prompt created, making LLM call...
2025-08-02 19:54:53,501 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-02 19:54:53,501 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-02 19:54:56,858 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:54:56,884 - manual_test - INFO - ✅ Notification categorized as: Review (confidence: high)
2025-08-02 19:54:56,884 - manual_test - INFO - ✅ Notification categorized as: Review (confidence: high)
2025-08-02 19:54:56,887 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4.1 for regulatory_interpretation - Selected critical tier for regulatory_interpretation (context: 7218, priority: quality)
2025-08-02 19:54:56,887 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4.1 for regulatory_interpretation - Selected critical tier for regulatory_interpretation (context: 7218, priority: quality)
2025-08-02 19:55:05,300 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:55:05,306 - manual_test - INFO - 📋 KB Decision: update_existing (store: True, remove: True)
2025-08-02 19:55:05,306 - manual_test - INFO - 📋 KB Decision: update_existing (store: True, remove: True)
2025-08-02 19:55:05,306 - manual_test - INFO - --- ENHANCED Stage 2: Notification-level KB Actions ---
2025-08-02 19:55:05,306 - manual_test - INFO - --- ENHANCED Stage 2: Notification-level KB Actions ---
2025-08-02 19:55:05,306 - manual_test - INFO - notification_codes being passed to KB storage: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['UBD.PCB.No.59/13.05.000', 'BSD.No.IP.30/12.05.05', 'A.P. (DIR Series) Circular No. 18', 'Plan.PCB.CIR.07/09.27.00', '.IP.30/12.05.05/2002-03', '.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000', '.REC.3/09.27.000/2024-25', 'BSD.No.IP.30/12.05.05/2002-03', 'UBD.PCB.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000/2008', '.No.36/13.05.000/2008-09', 'UBD.No.Plan.42/09.27.00'], 'watermark_status': 'ACTIVE', 'is_active': True}
2025-08-02 19:55:05,306 - manual_test - INFO - notification_codes being passed to KB storage: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['UBD.PCB.No.59/13.05.000', 'BSD.No.IP.30/12.05.05', 'A.P. (DIR Series) Circular No. 18', 'Plan.PCB.CIR.07/09.27.00', '.IP.30/12.05.05/2002-03', '.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000', '.REC.3/09.27.000/2024-25', 'BSD.No.IP.30/12.05.05/2002-03', 'UBD.PCB.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000/2008', '.No.36/13.05.000/2008-09', 'UBD.No.Plan.42/09.27.00'], 'watermark_status': 'ACTIVE', 'is_active': True}
2025-08-02 19:55:05,306 - manual_test - INFO - 🔍 Detecting document type for flowchart-based processing...
2025-08-02 19:55:05,306 - manual_test - INFO - 🔍 Detecting document type for flowchart-based processing...
2025-08-02 19:55:05,306 - manual_test - INFO - 🔍 Detecting document type from title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs...
2025-08-02 19:55:05,306 - manual_test - INFO - 🔍 Detecting document type from title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs...
2025-08-02 19:55:05,306 - manual_test - INFO - 📋 Target collection from KB decision: rbi_master_circular
2025-08-02 19:55:05,306 - manual_test - INFO - 📋 Target collection from KB decision: rbi_master_circular
2025-08-02 19:55:05,307 - manual_test - INFO - ✅ Detected: Master Circular
2025-08-02 19:55:05,307 - manual_test - INFO - ✅ Detected: Master Circular
2025-08-02 19:55:05,307 - manual_test - INFO - 📋 Document Type Detected: master_circular
2025-08-02 19:55:05,307 - manual_test - INFO - 📋 Document Type Detected: master_circular
2025-08-02 19:55:05,307 - manual_test - INFO - 🎯 Generating flowchart-based actions for document type: master_circular
2025-08-02 19:55:05,307 - manual_test - INFO - 🎯 Generating flowchart-based actions for document type: master_circular
2025-08-02 19:55:05,307 - manual_test - INFO - 📘 Processing Master Circular actions...
2025-08-02 19:55:05,307 - manual_test - INFO - 📘 Processing Master Circular actions...
2025-08-02 19:55:05,753 - utils.s3_utils - INFO - Successfully uploaded file to https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:55:05,756 - manual_test - INFO - ✅ Uploaded notification PDF to S3: https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:55:05,756 - manual_test - INFO - ✅ Uploaded notification PDF to S3: https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:55:05,756 - manual_test - INFO - ✅ Added: ADD_DOCUMENT action for Master Circular
2025-08-02 19:55:05,756 - manual_test - INFO - ✅ Added: ADD_DOCUMENT action for Master Circular
2025-08-02 19:55:05,756 - manual_test - INFO - 🎯 Generated 1 KB actions based on flowchart:
2025-08-02 19:55:05,756 - manual_test - INFO - 🎯 Generated 1 KB actions based on flowchart:
2025-08-02 19:55:05,756 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT in rbi_master_circular
2025-08-02 19:55:05,756 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT in rbi_master_circular
2025-08-02 19:55:05,757 - manual_test - INFO -       🎯 Target: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:55:05,757 - manual_test - INFO -       🎯 Target: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:55:05,757 - manual_test - INFO -       🤔 Reasoning: New Master Circular publication - follows flowchart logic
2025-08-02 19:55:05,757 - manual_test - INFO -       🤔 Reasoning: New Master Circular publication - follows flowchart logic
2025-08-02 19:55:05,757 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-02 19:55:05,757 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-02 19:55:06,331 - utils.s3_utils - INFO - Successfully uploaded file to https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:55:06,332 - manual_test - INFO - ✅ Uploaded notification PDF to S3: https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:55:06,332 - manual_test - INFO - ✅ Uploaded notification PDF to S3: https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:55:06,332 - manual_test - INFO - ✅ Executing 1 flowchart-based KB actions
2025-08-02 19:55:06,332 - manual_test - INFO - ✅ Executing 1 flowchart-based KB actions
2025-08-02 19:55:06,332 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT for collection rbi_master_circular
2025-08-02 19:55:06,332 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT for collection rbi_master_circular
2025-08-02 19:55:06,332 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:55:06,332 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:06,332 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:06,333 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:55:06,333 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_circular'], PID=85bdd850-23cb-581c-a557-f7f038cf5ba2, Document=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25, Chunk=None
2025-08-02 19:55:06,333 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 with text: 8000 chars
2025-08-02 19:55:06,338 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_master_circular/exists "HTTP/1.1 200 OK"
2025-08-02 19:55:06,344 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_master_circular "HTTP/1.1 200 OK"
2025-08-02 19:55:06,345 - qdrant_utils - INFO - 🔍 Collection rbi_master_circular vectors: size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:55:06,345 - qdrant_utils - INFO - 🔍 Collection rbi_master_circular sparse vectors: {'fast-sparse-bm25': SparseVectorParams(index=SparseIndexParams(full_scan_threshold=None, on_disk=None, datatype=None), modifier=<Modifier.IDF: 'idf'>)}
2025-08-02 19:55:06,345 - qdrant_utils - WARNING - Collection rbi_master_circular exists but uses hybrid/single format. Will work with existing.
2025-08-02 19:55:07,873 - openai_utils - INFO - Embedding 1 documents
2025-08-02 19:55:08,987 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-02 19:55:09,181 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_master_circular "HTTP/1.1 200 OK"
2025-08-02 19:55:09,182 - qdrant_utils - INFO - Collection info for rbi_master_circular: vectors=size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:55:09,182 - qdrant_utils - INFO - Collection rbi_master_circular uses hybrid format (single dense + sparse)
2025-08-02 19:55:09,182 - qdrant_utils - INFO - Collection format: hybrid
2025-08-02 19:55:09,182 - qdrant_utils - INFO - ✅ Added dense vector with 3072 dimensions
2025-08-02 19:55:09,186 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_master_circular "HTTP/1.1 200 OK"
2025-08-02 19:55:09,187 - qdrant_utils - INFO - ✅ Added sparse vector 'fast-sparse-bm25' with 410 indices
2025-08-02 19:55:09,187 - qdrant_utils - WARNING - ⚠️ Skipping sparse vector 'splade' - not configured in collection schema
2025-08-02 19:55:09,187 - qdrant_utils - INFO - 🔍 Final vector keys for point 85bdd850-23cb-581c-a557-f7f038cf5ba2: ['', 'fast-sparse-bm25']
2025-08-02 19:55:09,224 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_master_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:55:09,225 - qdrant_utils - INFO - ✅ Using hybrid format for point 85bdd850-23cb-581c-a557-f7f038cf5ba2
2025-08-02 19:55:09,225 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 (ID: 85bdd850-23cb-581c-a557-f7f038cf5ba2)
2025-08-02 19:55:09,225 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 (ID: 85bdd850-23cb-581c-a557-f7f038cf5ba2) to collections: ['rbi_master_circular']", "timestamp": "2025-08-02T14:25:09.225212", "document_id": "RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25", "uuid": "85bdd850-23cb-581c-a557-f7f038cf5ba2", "collection": "rbi_master_circular"}
2025-08-02 19:55:09,225 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:09,225 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:09,226 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:09,226 - manual_test - INFO - ✅ Flowchart-based KB Results summary:
2025-08-02 19:55:09,226 - manual_test - INFO - ✅ Flowchart-based KB Results summary:
2025-08-02 19:55:09,226 - manual_test - INFO -   📋 Result 1: ADD_DOCUMENT - SUCCESS
2025-08-02 19:55:09,226 - manual_test - INFO -   📋 Result 1: ADD_DOCUMENT - SUCCESS
2025-08-02 19:55:09,226 - manual_test - INFO -       📦 Collection: rbi_master_circular
2025-08-02 19:55:09,226 - manual_test - INFO -       📦 Collection: rbi_master_circular
2025-08-02 19:55:09,226 - manual_test - INFO -       🆔 UUID: 85bdd850-23cb-581c-a557-f7f038cf5ba2
2025-08-02 19:55:09,226 - manual_test - INFO -       🆔 UUID: 85bdd850-23cb-581c-a557-f7f038cf5ba2
2025-08-02 19:55:09,226 - manual_test - INFO -       🤔 Reasoning: New Master Circular publication - follows flowchart logic
2025-08-02 19:55:09,226 - manual_test - INFO -       🤔 Reasoning: New Master Circular publication - follows flowchart logic
2025-08-02 19:55:09,226 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-02 19:55:09,226 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-02 19:55:09,227 - manual_test - INFO - 📊 Added 1 documents with UUIDs: ['85bdd850-23cb-581c-a557-f7f038cf5ba2']
2025-08-02 19:55:09,227 - manual_test - INFO - 📊 Added 1 documents with UUIDs: ['85bdd850-23cb-581c-a557-f7f038cf5ba2']
2025-08-02 19:55:09,227 - manual_test - INFO - 📊 Adding notification to metadata_vectors collection for enhanced search
2025-08-02 19:55:09,227 - manual_test - INFO - 📊 Adding notification to metadata_vectors collection for enhanced search
2025-08-02 19:55:09,229 - utils.qdrant_utils - INFO - Configured Hugging Face HTTP backend to bypass SSL verification
2025-08-02 19:55:10,468 - utils.qdrant_utils - INFO - Loaded SPLADE model: naver/efficient-splade-VI-BT-large-doc
2025-08-02 19:55:10,468 - utils.qdrant_utils - INFO - Initializing QdrantManager with URL: http://localhost:6333
2025-08-02 19:55:10,482 - httpx - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-02 19:55:10,486 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections "HTTP/1.1 200 OK"
2025-08-02 19:55:10,487 - utils.qdrant_utils - INFO - ✅ QdrantClient initialized successfully
2025-08-02 19:55:11,376 - utils.qdrant_utils - INFO - ✅ Sparse embedder initialized with model: Qdrant/bm25
2025-08-02 19:55:11,376 - utils.qdrant_utils - INFO - 🔍 FastEmbed creates sparse vector type: <class 'langchain_qdrant.sparse_embeddings.SparseVector'>
2025-08-02 19:55:11,376 - utils.qdrant_utils - INFO - 🔍 FastEmbed sparse vector indices count: 1
2025-08-02 19:55:11,376 - manual_test - INFO - 🧠 Using smart-selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-02 19:55:11,376 - manual_test - INFO - 🧠 Using smart-selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-02 19:55:11,376 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-08-02 19:55:11,376 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-02 19:55:15,387 - manual_test - INFO - 🔍 Adding notification to metadata_vectors with ID: 0bceb959-f4eb-4ac7-879c-4a70923d6068
2025-08-02 19:55:15,387 - manual_test - INFO - 🔍 Adding notification to metadata_vectors with ID: 0bceb959-f4eb-4ac7-879c-4a70923d6068
2025-08-02 19:55:15,387 - manual_test - INFO - 🔍 Vector keys: ['document_title_vec', 'document_number_vec', 'short_summary_vec', 'document_id_vec']
2025-08-02 19:55:15,387 - manual_test - INFO - 🔍 Vector keys: ['document_title_vec', 'document_number_vec', 'short_summary_vec', 'document_id_vec']
2025-08-02 19:55:15,388 - manual_test - INFO - 🔍 Vector dimensions: [('document_title_vec', 384), ('document_number_vec', 384), ('short_summary_vec', 384), ('document_id_vec', 384)]
2025-08-02 19:55:15,388 - manual_test - INFO - 🔍 Vector dimensions: [('document_title_vec', 384), ('document_number_vec', 384), ('short_summary_vec', 384), ('document_id_vec', 384)]
2025-08-02 19:55:15,395 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/metadata_vectors "HTTP/1.1 200 OK"
2025-08-02 19:55:15,417 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/metadata_vectors/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:55:15,418 - manual_test - INFO - ✅ Added notification to metadata_vectors: 0bceb959-f4eb-4ac7-879c-4a70923d6068
2025-08-02 19:55:15,418 - manual_test - INFO - ✅ Added notification to metadata_vectors: 0bceb959-f4eb-4ac7-879c-4a70923d6068
2025-08-02 19:55:15,418 - manual_test - INFO -    📊 Notification metadata vectors UUIDs: 1
2025-08-02 19:55:15,418 - manual_test - INFO -    📊 Notification metadata vectors UUIDs: 1
2025-08-02 19:55:15,418 - manual_test - INFO - --- ENHANCED Stage 3: STREAMLINED PDF Processing and Action Execution ---
2025-08-02 19:55:15,418 - manual_test - INFO - --- ENHANCED Stage 3: STREAMLINED PDF Processing and Action Execution ---
2025-08-02 19:55:15,418 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:15,418 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:15,491 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:15,491 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:15,679 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:15,679 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:15,687 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:55:15,687 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:55:15,696 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:55:15,696 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:55:15,696 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['UBD.PCB.No.59/13.05.000', 'BSD.No.IP.30/12.05.05', 'A.P. (DIR Series) Circular No. 18', 'Plan.PCB.CIR.07/09.27.00', '.IP.30/12.05.05/2002-03', '.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000', '.REC.3/09.27.000/2024-25', 'BSD.No.IP.30/12.05.05/2002-03', 'UBD.PCB.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000/2008', '.No.36/13.05.000/2008-09', 'UBD.No.Plan.42/09.27.00']}
2025-08-02 19:55:15,696 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['UBD.PCB.No.59/13.05.000', 'BSD.No.IP.30/12.05.05', 'A.P. (DIR Series) Circular No. 18', 'Plan.PCB.CIR.07/09.27.00', '.IP.30/12.05.05/2002-03', '.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000', '.REC.3/09.27.000/2024-25', 'BSD.No.IP.30/12.05.05/2002-03', 'UBD.PCB.No.59/13.05.000/2008-09', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000/2008', '.No.36/13.05.000/2008-09', 'UBD.No.Plan.42/09.27.00']}
2025-08-02 19:55:15,696 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:55:15,696 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:55:15,696 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:55:15,696 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:55:15,696 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:55:15,696 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:55:15,696 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:55:15,696 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:55:15,696 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:55:15,696 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:55:15,696 - manual_test - INFO - 🔧 Starting streamlined processing of 3 chunks
2025-08-02 19:55:15,696 - manual_test - INFO - 🔧 Starting streamlined processing of 3 chunks
2025-08-02 19:55:15,696 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 1/3
2025-08-02 19:55:15,696 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 1/3
2025-08-02 19:55:15,696 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 2513 chars
2025-08-02 19:55:15,696 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 2513 chars
2025-08-02 19:55:15,696 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:55:15,696 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:55:15,696 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:55:15,696 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:55:15,696 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 2513 chars
2025-08-02 19:55:15,696 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 2513 chars
2025-08-02 19:55:15,697 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:55:15,697 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:55:15,697 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:55:15,697 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:55:15,697 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o-mini for document_classification - Selected simple tier for document_classification (context: 491, priority: balanced)
2025-08-02 19:55:15,697 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o-mini for document_classification - Selected simple tier for document_classification (context: 491, priority: balanced)
2025-08-02 19:55:19,331 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:55:19,334 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:55:19,334 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:55:19,334 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular issued by the Reserv...
2025-08-02 19:55:19,334 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular issued by the Reserv...
2025-08-02 19:55:19,334 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:55:19,334 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:55:19,335 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:55:19,335 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:55:19,335 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular issued by the Reserve Bank of India, which consolidates existing instructions and guidelines related to guarantees, co-acceptances, and letters of credit for Urban Co-operative Banks (UCBs). It is regulatory in nature as it pertains to compliance obligations for banks, and it should be indexed for reference and compliance purposes."}
2025-08-02 19:55:19,335 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular issued by the Reserve Bank of India, which consolidates existing instructions and guidelines related to guarantees, co-acceptances, and letters of credit for Urban Co-operative Banks (UCBs). It is regulatory in nature as it pertains to compliance obligations for banks, and it should be indexed for reference and compliance purposes."}
2025-08-02 19:55:19,335 - manual_test - INFO - ✅ Chunk 1 classified as regulatory - INDEXING
2025-08-02 19:55:19,335 - manual_test - INFO - ✅ Chunk 1 classified as regulatory - INDEXING
2025-08-02 19:55:19,335 - root - INFO - Sending structured request to extract document actions
2025-08-02 19:55:22,879 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:55:22,890 - root - INFO - Received response type: <class '__main__.DocumentActionsList'>
2025-08-02 19:55:22,890 - root - INFO - ✅ Successfully received structured Pydantic output
2025-08-02 19:55:22,890 - manual_test - INFO - 🎯 Executing 1 actions for chunk 1
2025-08-02 19:55:22,890 - manual_test - INFO - 🎯 Executing 1 actions for chunk 1
2025-08-02 19:55:22,890 - manual_test - INFO - 🎯 Executing 1 chunk-specific actions
2025-08-02 19:55:22,890 - manual_test - INFO - 🎯 Executing 1 chunk-specific actions
2025-08-02 19:55:22,890 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-02 19:55:22,890 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-02 19:55:22,890 - manual_test - INFO - 📝 Sample action: {'document_id': 'DoR.STR.REC.3/09.27.000/2024-25', 'action_type': 'UPDATE_DOCUMENT', 'confidence': 'high', 'document_title': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs', 'document_url': None, 'reference_number': None, 'department': 'DoR', 'original_date': '2024-04-01', 'update_location': 'full-document', 'sunset_withdraw_date': None, 'reasoning': 'The document has been revised to consolidate all instructions issued up to March 31, 2025.'}
2025-08-02 19:55:22,890 - manual_test - INFO - 📝 Sample action: {'document_id': 'DoR.STR.REC.3/09.27.000/2024-25', 'action_type': 'UPDATE_DOCUMENT', 'confidence': 'high', 'document_title': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs', 'document_url': None, 'reference_number': None, 'department': 'DoR', 'original_date': '2024-04-01', 'update_location': 'full-document', 'sunset_withdraw_date': None, 'reasoning': 'The document has been revised to consolidate all instructions issued up to March 31, 2025.'}
2025-08-02 19:55:22,890 - manual_test - INFO - ▶️ Processing action 1/1
2025-08-02 19:55:22,890 - manual_test - INFO - ▶️ Processing action 1/1
2025-08-02 19:55:22,891 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_DoR_STR_REC_3_0_chunk0_da30ad62
2025-08-02 19:55:22,891 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_DoR_STR_REC_3_0_chunk0_da30ad62
2025-08-02 19:55:22,891 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:55:22,891 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:55:22,891 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:55:22,891 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:55:22,891 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:55:22,891 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:22,891 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:22,892 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:55:22,892 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:55:22,987 - utils.metadata_vector_utils - INFO - Searching metadata_vectors for 'DoR.STR.REC.3/09.27.000/2024-25' in field 'document_id'
2025-08-02 19:55:23,033 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-02 19:55:23,034 - utils.metadata_vector_utils - INFO - Found 5 results for metadata search
2025-08-02 19:55:23,034 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:55:23,034 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9741a628-999e-4b3f-aa8d-da299fe6f6c1, Score=0.8026, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-02 19:55:23,034 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3d495fa6-6797-461c-bf81-7de0eb59a24a, Score=0.8026, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-02 19:55:23,034 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2105f02f-10ac-520c-82c1-d91e04509d6d, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:55:23,035 - knowledge_base_executor - INFO - [QDRANT] Match: ID=d2b23822-c5a2-5527-8507-5b3d29d5794c, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:55:23,035 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d04a677-f401-5734-88ac-14846a6cf5b3, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:55:23,035 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:23,035 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:23,037 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3'])] must_not=None, Payload={}
2025-08-02 19:55:23,065 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_master_direction/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:23,069 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 3 matching docs in rbi_master_direction
2025-08-02 19:55:23,090 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_master_direction/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:55:23,090 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3172 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-02 19:55:23,090 - knowledge_base_executor - INFO - ✅ Successfully updated 3 documents in collection: rbi_master_direction
2025-08-02 19:55:23,090 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:23,090 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:23,090 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:55:23,090 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:23,091 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:55:23,091 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:55:23,091 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:55:23,091 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:55:23,091 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:23,091 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3'])] must_not=None, Payload={}
2025-08-02 19:55:23,098 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_master_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-02 19:55:23,098 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:23,098 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3'])] must_not=None, Payload={}
2025-08-02 19:55:23,108 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:23,112 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 2 matching docs in rbi_circular
2025-08-02 19:55:23,128 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:55:23,128 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=12984 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-02 19:55:23,128 - knowledge_base_executor - INFO - ✅ Successfully updated 2 documents in collection: rbi_circular
2025-08-02 19:55:23,128 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction', 'rbi_circular']", "timestamp": "2025-08-02T14:25:23.128494"}
2025-08-02 19:55:23,128 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:23,128 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:23,130 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:23,130 - manual_test - INFO - ✅ Action executed successfully: UPDATE_DOCUMENT - DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:55:23,130 - manual_test - INFO - ✅ Action executed successfully: UPDATE_DOCUMENT - DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:55:23,130 - manual_test - INFO - 📊 Chunk action execution: 1/1 successful
2025-08-02 19:55:23,130 - manual_test - INFO - 📊 Chunk action execution: 1/1 successful
2025-08-02 19:55:23,131 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 1:
2025-08-02 19:55:23,131 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 1:
2025-08-02 19:55:23,131 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2025-26/11
2025-08-02 19:55:23,131 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2025-26/11
2025-08-02 19:55:23,131 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2025-26/11
2025-08-02 19:55:23,131 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2025-26/11
2025-08-02 19:55:23,131 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-02 19:55:23,131 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-02 19:55:23,131 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:55:23,131 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:23,131 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:23,131 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:55:23,131 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=37f15250-7c7f-4831-ad6b-b1a973f43640, Document=None, Chunk=0
2025-08-02 19:55:23,131 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 2513 chars
2025-08-02 19:55:23,134 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular/exists "HTTP/1.1 200 OK"
2025-08-02 19:55:23,136 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:55:23,136 - qdrant_utils - INFO - 🔍 Collection rbi_circular vectors: size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:55:23,136 - qdrant_utils - INFO - 🔍 Collection rbi_circular sparse vectors: {'fast-sparse-bm25': SparseVectorParams(index=SparseIndexParams(full_scan_threshold=None, on_disk=None, datatype=None), modifier=<Modifier.IDF: 'idf'>)}
2025-08-02 19:55:23,136 - qdrant_utils - WARNING - Collection rbi_circular exists but uses hybrid/single format. Will work with existing.
2025-08-02 19:55:25,179 - openai_utils - INFO - Embedding 1 documents
2025-08-02 19:55:26,087 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-02 19:55:26,223 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:55:26,224 - qdrant_utils - INFO - Collection info for rbi_circular: vectors=size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:55:26,224 - qdrant_utils - INFO - Collection rbi_circular uses hybrid format (single dense + sparse)
2025-08-02 19:55:26,224 - qdrant_utils - INFO - Collection format: hybrid
2025-08-02 19:55:26,224 - qdrant_utils - INFO - ✅ Added dense vector with 3072 dimensions
2025-08-02 19:55:26,226 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:55:26,226 - qdrant_utils - INFO - ✅ Added sparse vector 'fast-sparse-bm25' with 162 indices
2025-08-02 19:55:26,226 - qdrant_utils - WARNING - ⚠️ Skipping sparse vector 'splade' - not configured in collection schema
2025-08-02 19:55:26,226 - qdrant_utils - INFO - 🔍 Final vector keys for point 37f15250-7c7f-4831-ad6b-b1a973f43640: ['', 'fast-sparse-bm25']
2025-08-02 19:55:26,234 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:55:26,234 - qdrant_utils - INFO - ✅ Using hybrid format for point 37f15250-7c7f-4831-ad6b-b1a973f43640
2025-08-02 19:55:26,234 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 37f15250-7c7f-4831-ad6b-b1a973f43640)
2025-08-02 19:55:26,234 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 37f15250-7c7f-4831-ad6b-b1a973f43640) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:25:26.234665", "document_id": null, "uuid": "37f15250-7c7f-4831-ad6b-b1a973f43640", "collection": "rbi_circular"}
2025-08-02 19:55:26,234 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:26,234 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:26,235 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:26,235 - manual_test - INFO - 🆔 CHUNK UUID: 37f15250-7c7f-4831-ad6b-b1a973f43640
2025-08-02 19:55:26,235 - manual_test - INFO - 🆔 CHUNK UUID: 37f15250-7c7f-4831-ad6b-b1a973f43640
2025-08-02 19:55:26,235 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 1
2025-08-02 19:55:26,235 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 1
2025-08-02 19:55:26,235 - manual_test - INFO -    ✅ Chunk 1 added to processing queue
2025-08-02 19:55:26,235 - manual_test - INFO -    ✅ Chunk 1 added to processing queue
2025-08-02 19:55:26,235 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 2/3
2025-08-02 19:55:26,235 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 2/3
2025-08-02 19:55:26,235 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 8230 chars
2025-08-02 19:55:26,235 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 8230 chars
2025-08-02 19:55:26,235 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:55:26,235 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:55:26,235 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:55:26,235 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:55:26,235 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 8230 chars
2025-08-02 19:55:26,235 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 8230 chars
2025-08-02 19:55:26,236 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:55:26,236 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:55:26,236 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:55:26,236 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:55:26,236 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o for document_classification - Selected medium tier for document_classification (context: 1206, priority: balanced)
2025-08-02 19:55:26,236 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o for document_classification - Selected medium tier for document_classification (context: 1206, priority: balanced)
2025-08-02 19:55:30,993 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:55:30,999 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:55:30,999 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:55:30,999 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular, which typically con...
2025-08-02 19:55:30,999 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular, which typically con...
2025-08-02 19:55:30,999 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:55:30,999 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:55:31,000 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:55:31,000 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:55:31,000 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular, which typically consolidates regulatory requirements, compliance obligations, and guidelines issued by the RBI. It provides detailed instructions on guarantees, co-acceptances, and letters of credit for Urban Co-operative Banks (UCBs), indicating its regulatory nature. Master Circulars are essential for compliance and understanding of regulatory frameworks, making them suitable for indexing in the knowledge base. The content is not administrative or withdrawn, and the detailed guidelines confirm its regulatory classification."}
2025-08-02 19:55:31,000 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular, which typically consolidates regulatory requirements, compliance obligations, and guidelines issued by the RBI. It provides detailed instructions on guarantees, co-acceptances, and letters of credit for Urban Co-operative Banks (UCBs), indicating its regulatory nature. Master Circulars are essential for compliance and understanding of regulatory frameworks, making them suitable for indexing in the knowledge base. The content is not administrative or withdrawn, and the detailed guidelines confirm its regulatory classification."}
2025-08-02 19:55:31,000 - manual_test - INFO - ✅ Chunk 2 classified as regulatory - INDEXING
2025-08-02 19:55:31,000 - manual_test - INFO - ✅ Chunk 2 classified as regulatory - INDEXING
2025-08-02 19:55:31,001 - root - INFO - Sending structured request to extract document actions
2025-08-02 19:55:39,298 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:55:39,302 - root - INFO - Received response type: <class '__main__.DocumentActionsList'>
2025-08-02 19:55:39,303 - root - INFO - ✅ Successfully received structured Pydantic output
2025-08-02 19:55:39,303 - manual_test - INFO - 🎯 Executing 1 actions for chunk 2
2025-08-02 19:55:39,303 - manual_test - INFO - 🎯 Executing 1 actions for chunk 2
2025-08-02 19:55:39,303 - manual_test - INFO - 🎯 Executing 1 chunk-specific actions
2025-08-02 19:55:39,303 - manual_test - INFO - 🎯 Executing 1 chunk-specific actions
2025-08-02 19:55:39,303 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-02 19:55:39,303 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-02 19:55:39,303 - manual_test - INFO - 📝 Sample action: {'document_id': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs', 'action_type': 'UPDATE_DOCUMENT', 'confidence': 'high', 'document_title': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit', 'document_url': None, 'reference_number': None, 'department': 'Department of Regulation', 'original_date': None, 'update_location': 'full-document', 'sunset_withdraw_date': None, 'reasoning': 'The document outlines updated guidelines and safeguards for issuance of guarantees, co-acceptances, and letters of credit by UCBs.'}
2025-08-02 19:55:39,303 - manual_test - INFO - 📝 Sample action: {'document_id': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs', 'action_type': 'UPDATE_DOCUMENT', 'confidence': 'high', 'document_title': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit', 'document_url': None, 'reference_number': None, 'department': 'Department of Regulation', 'original_date': None, 'update_location': 'full-document', 'sunset_withdraw_date': None, 'reasoning': 'The document outlines updated guidelines and safeguards for issuance of guarantees, co-acceptances, and letters of credit by UCBs.'}
2025-08-02 19:55:39,303 - manual_test - INFO - ▶️ Processing action 1/1
2025-08-02 19:55:39,303 - manual_test - INFO - ▶️ Processing action 1/1
2025-08-02 19:55:39,303 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_Master_Circular_chunk1_099fb094
2025-08-02 19:55:39,303 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_Master_Circular_chunk1_099fb094
2025-08-02 19:55:39,303 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs
2025-08-02 19:55:39,303 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs
2025-08-02 19:55:39,303 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs
2025-08-02 19:55:39,303 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs
2025-08-02 19:55:39,304 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:55:39,304 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:39,304 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:39,304 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:55:39,304 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:55:39,406 - utils.metadata_vector_utils - INFO - Searching metadata_vectors for 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs' in field 'document_id'
2025-08-02 19:55:39,416 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-02 19:55:39,417 - utils.metadata_vector_utils - INFO - Found 5 results for metadata search
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] Match: ID=521d0371-918a-4608-a972-d5d632952cc8, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] Match: ID=7940f618-ba47-45d9-aa0d-98ebba0fb222, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ccd83ffc-2347-403f-92cc-2aeb36cd3027, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] Match: ID=c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs' → 'MasterCircular-Guarantees,Co-acceptances&LettersofCredit-UCBs'
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: MasterCircular-Guarantees,Co-acceptances&LettersofCredit-UCBs
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:39,417 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1'])] must_not=None, Payload={}
2025-08-02 19:55:39,421 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_master_direction/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-02 19:55:39,421 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:39,421 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1'])] must_not=None, Payload={}
2025-08-02 19:55:39,439 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_master_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:39,445 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_circular
2025-08-02 19:55:39,483 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_master_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8280 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_circular
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:39,483 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1'])] must_not=None, Payload={}
2025-08-02 19:55:39,487 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:39,487 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-02 19:55:39,487 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-02 19:55:39,487 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular']", "timestamp": "2025-08-02T14:25:39.487449"}
2025-08-02 19:55:39,487 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:39,487 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:39,488 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:39,488 - manual_test - INFO - ✅ Action executed successfully: UPDATE_DOCUMENT - Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs
2025-08-02 19:55:39,488 - manual_test - INFO - ✅ Action executed successfully: UPDATE_DOCUMENT - Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs
2025-08-02 19:55:39,488 - manual_test - INFO - 📊 Chunk action execution: 1/1 successful
2025-08-02 19:55:39,488 - manual_test - INFO - 📊 Chunk action execution: 1/1 successful
2025-08-02 19:55:39,488 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 2:
2025-08-02 19:55:39,488 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 2:
2025-08-02 19:55:39,488 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2025-26/11
2025-08-02 19:55:39,488 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2025-26/11
2025-08-02 19:55:39,488 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2025-26/11
2025-08-02 19:55:39,488 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2025-26/11
2025-08-02 19:55:39,488 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-02 19:55:39,488 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-02 19:55:39,488 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:55:39,488 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:39,488 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:39,488 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:55:39,488 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=1b52add7-0399-472c-ba6e-98a51adb1391, Document=None, Chunk=1
2025-08-02 19:55:39,488 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-02 19:55:39,490 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular/exists "HTTP/1.1 200 OK"
2025-08-02 19:55:39,491 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:55:39,492 - qdrant_utils - INFO - 🔍 Collection rbi_circular vectors: size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:55:39,492 - qdrant_utils - INFO - 🔍 Collection rbi_circular sparse vectors: {'fast-sparse-bm25': SparseVectorParams(index=SparseIndexParams(full_scan_threshold=None, on_disk=None, datatype=None), modifier=<Modifier.IDF: 'idf'>)}
2025-08-02 19:55:39,492 - qdrant_utils - WARNING - Collection rbi_circular exists but uses hybrid/single format. Will work with existing.
2025-08-02 19:55:39,837 - openai_utils - INFO - Embedding 1 documents
2025-08-02 19:55:41,242 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-02 19:55:41,528 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:55:41,529 - qdrant_utils - INFO - Collection info for rbi_circular: vectors=size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:55:41,529 - qdrant_utils - INFO - Collection rbi_circular uses hybrid format (single dense + sparse)
2025-08-02 19:55:41,529 - qdrant_utils - INFO - Collection format: hybrid
2025-08-02 19:55:41,529 - qdrant_utils - INFO - ✅ Added dense vector with 3072 dimensions
2025-08-02 19:55:41,531 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:55:41,532 - qdrant_utils - INFO - ✅ Added sparse vector 'fast-sparse-bm25' with 408 indices
2025-08-02 19:55:41,532 - qdrant_utils - WARNING - ⚠️ Skipping sparse vector 'splade' - not configured in collection schema
2025-08-02 19:55:41,532 - qdrant_utils - INFO - 🔍 Final vector keys for point 1b52add7-0399-472c-ba6e-98a51adb1391: ['', 'fast-sparse-bm25']
2025-08-02 19:55:41,541 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:55:41,541 - qdrant_utils - INFO - ✅ Using hybrid format for point 1b52add7-0399-472c-ba6e-98a51adb1391
2025-08-02 19:55:41,541 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 1b52add7-0399-472c-ba6e-98a51adb1391)
2025-08-02 19:55:41,541 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 1b52add7-0399-472c-ba6e-98a51adb1391) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:25:41.541755", "document_id": null, "uuid": "1b52add7-0399-472c-ba6e-98a51adb1391", "collection": "rbi_circular"}
2025-08-02 19:55:41,541 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:41,541 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:41,542 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:41,542 - manual_test - INFO - 🆔 CHUNK UUID: 1b52add7-0399-472c-ba6e-98a51adb1391
2025-08-02 19:55:41,542 - manual_test - INFO - 🆔 CHUNK UUID: 1b52add7-0399-472c-ba6e-98a51adb1391
2025-08-02 19:55:41,542 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 2
2025-08-02 19:55:41,542 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 2
2025-08-02 19:55:41,542 - manual_test - INFO -    ✅ Chunk 2 added to processing queue
2025-08-02 19:55:41,542 - manual_test - INFO -    ✅ Chunk 2 added to processing queue
2025-08-02 19:55:41,542 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 3/3
2025-08-02 19:55:41,542 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 3/3
2025-08-02 19:55:41,542 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 38710 chars
2025-08-02 19:55:41,542 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 38710 chars
2025-08-02 19:55:41,542 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:55:41,542 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:55:41,542 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:55:41,542 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:55:41,542 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 38710 chars
2025-08-02 19:55:41,542 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 38710 chars
2025-08-02 19:55:41,543 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:55:41,543 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:55:41,543 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:55:41,543 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:55:41,543 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o for document_classification - Selected medium tier for document_classification (context: 5883, priority: balanced)
2025-08-02 19:55:41,543 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o for document_classification - Selected medium tier for document_classification (context: 5883, priority: balanced)
2025-08-02 19:55:46,158 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:55:46,160 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:55:46,160 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:55:46,160 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular, which consolidates ...
2025-08-02 19:55:46,160 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular, which consolidates ...
2025-08-02 19:55:46,160 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:55:46,160 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:55:46,161 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:55:46,161 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:55:46,161 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular, which consolidates regulatory guidelines and requirements for banks, specifically focusing on guarantees, co-acceptances, and letters of credit. It provides detailed instructions and compliance obligations for banks, making it a regulatory document. Master Circulars are critical for ensuring that banks adhere to RBI's regulatory framework, and thus, they should be indexed in the knowledge base for easy reference and compliance purposes."}
2025-08-02 19:55:46,161 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular, which consolidates regulatory guidelines and requirements for banks, specifically focusing on guarantees, co-acceptances, and letters of credit. It provides detailed instructions and compliance obligations for banks, making it a regulatory document. Master Circulars are critical for ensuring that banks adhere to RBI's regulatory framework, and thus, they should be indexed in the knowledge base for easy reference and compliance purposes."}
2025-08-02 19:55:46,161 - manual_test - INFO - ✅ Chunk 3 classified as regulatory - INDEXING
2025-08-02 19:55:46,161 - manual_test - INFO - ✅ Chunk 3 classified as regulatory - INDEXING
2025-08-02 19:55:46,161 - root - INFO - Sending structured request to extract document actions
2025-08-02 19:55:57,817 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:55:57,822 - root - INFO - Received response type: <class '__main__.DocumentActionsList'>
2025-08-02 19:55:57,822 - root - INFO - ✅ Successfully received structured Pydantic output
2025-08-02 19:55:57,822 - manual_test - INFO - 🎯 Executing 4 actions for chunk 3
2025-08-02 19:55:57,822 - manual_test - INFO - 🎯 Executing 4 actions for chunk 3
2025-08-02 19:55:57,822 - manual_test - INFO - 🎯 Executing 4 chunk-specific actions
2025-08-02 19:55:57,822 - manual_test - INFO - 🎯 Executing 4 chunk-specific actions
2025-08-02 19:55:57,822 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-02 19:55:57,822 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-02 19:55:57,822 - manual_test - INFO - 📝 Sample action: {'document_id': 'UBD.No.Plan.(PCB)49/09.27.00/96-97', 'action_type': 'UPDATE_DOCUMENT', 'confidence': 'high', 'document_title': 'Payment under bank guarantee - Immediate settlement of cases', 'document_url': None, 'reference_number': None, 'department': 'UBD', 'original_date': '1997-04-26', 'update_location': 'full-document', 'sunset_withdraw_date': None, 'reasoning': 'The document discusses the immediate settlement of cases under bank guarantees, which is a recurring issue highlighted in the notification.'}
2025-08-02 19:55:57,822 - manual_test - INFO - 📝 Sample action: {'document_id': 'UBD.No.Plan.(PCB)49/09.27.00/96-97', 'action_type': 'UPDATE_DOCUMENT', 'confidence': 'high', 'document_title': 'Payment under bank guarantee - Immediate settlement of cases', 'document_url': None, 'reference_number': None, 'department': 'UBD', 'original_date': '1997-04-26', 'update_location': 'full-document', 'sunset_withdraw_date': None, 'reasoning': 'The document discusses the immediate settlement of cases under bank guarantees, which is a recurring issue highlighted in the notification.'}
2025-08-02 19:55:57,822 - manual_test - INFO - ▶️ Processing action 1/4
2025-08-02 19:55:57,822 - manual_test - INFO - ▶️ Processing action 1/4
2025-08-02 19:55:57,823 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_UBD_No_Plan__PC_chunk2_325b8af5
2025-08-02 19:55:57,823 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_UBD_No_Plan__PC_chunk2_325b8af5
2025-08-02 19:55:57,823 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for UBD.No.Plan.(PCB)49/09.27.00/96-97
2025-08-02 19:55:57,823 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for UBD.No.Plan.(PCB)49/09.27.00/96-97
2025-08-02 19:55:57,823 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: UBD.No.Plan.(PCB)49/09.27.00/96-97
2025-08-02 19:55:57,823 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: UBD.No.Plan.(PCB)49/09.27.00/96-97
2025-08-02 19:55:57,823 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:55:57,824 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:57,824 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:57,824 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:55:57,824 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:55:57,943 - utils.metadata_vector_utils - INFO - Searching metadata_vectors for 'UBD.No.Plan.(PCB)49/09.27.00/96-97' in field 'document_id'
2025-08-02 19:55:57,955 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-02 19:55:57,955 - utils.metadata_vector_utils - INFO - Found 5 results for metadata search
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5dd89363-59cc-4134-ada4-eeda877a51b5, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] Match: ID=729eb4c6-a6c3-4aed-9a25-aa597db91127, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9affc01a-813a-4ab4-afdc-e864c70dd44f, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ac5ffabf-b12b-4fc4-93be-9684c9047105, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] Match: ID=96f706da-38a1-4bc3-bd09-648ab7d128db, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.Plan.(PCB)49/09.27.00/96-97
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:57,955 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:55:57,956 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:55:57,956 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:55:57,956 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:55:57,956 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:57,956 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db'])] must_not=None, Payload={}
2025-08-02 19:55:57,961 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_master_direction/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:57,961 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-02 19:55:57,961 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-02 19:55:57,961 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:57,961 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:57,961 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:55:57,961 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:57,961 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:55:57,961 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:55:57,961 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:55:57,962 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:55:57,962 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:57,962 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db'])] must_not=None, Payload={}
2025-08-02 19:55:57,982 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_master_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:57,994 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_circular
2025-08-02 19:55:58,046 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_master_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8281 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_circular
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:55:58,046 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,047 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db'])] must_not=None, Payload={}
2025-08-02 19:55:58,049 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:58,049 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-02 19:55:58,049 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-02 19:55:58,050 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular']", "timestamp": "2025-08-02T14:25:58.049844"}
2025-08-02 19:55:58,050 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:58,050 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:58,051 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:58,051 - manual_test - INFO - ✅ Action executed successfully: UPDATE_DOCUMENT - UBD.No.Plan.(PCB)49/09.27.00/96-97
2025-08-02 19:55:58,051 - manual_test - INFO - ✅ Action executed successfully: UPDATE_DOCUMENT - UBD.No.Plan.(PCB)49/09.27.00/96-97
2025-08-02 19:55:58,051 - manual_test - INFO - ▶️ Processing action 2/4
2025-08-02 19:55:58,051 - manual_test - INFO - ▶️ Processing action 2/4
2025-08-02 19:55:58,051 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_UBD_No_Plan_Cir_chunk2_325b8af5
2025-08-02 19:55:58,051 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_UBD_No_Plan_Cir_chunk2_325b8af5
2025-08-02 19:55:58,051 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for UBD.No.Plan.Cir.SUB.1/09.27.00/94-95
2025-08-02 19:55:58,051 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for UBD.No.Plan.Cir.SUB.1/09.27.00/94-95
2025-08-02 19:55:58,051 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: UBD.No.Plan.Cir.SUB.1/09.27.00/94-95
2025-08-02 19:55:58,051 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: UBD.No.Plan.Cir.SUB.1/09.27.00/94-95
2025-08-02 19:55:58,051 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:55:58,051 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:58,051 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:58,051 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:55:58,051 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:55:58,091 - utils.metadata_vector_utils - INFO - Searching metadata_vectors for 'UBD.No.Plan.Cir.SUB.1/09.27.00/94-95' in field 'document_id'
2025-08-02 19:55:58,099 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-02 19:55:58,099 - utils.metadata_vector_utils - INFO - Found 5 results for metadata search
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] Match: ID=166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a, Score=0.6521, Document=FIDD.CO.Plan.BC.23/04.09.01/2015-16
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] Match: ID=c0891098-62c7-4c42-8d31-7c970dda399f, Score=0.6521, Document=FIDD.CO.Plan.BC.23/04.09.01/2015-16
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] Match: ID=040e1215-100c-44b2-9c5a-325db2ecaf87, Score=0.6521, Document=FIDD.CO.Plan.BC.23/04.09.01/2015-16
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ac5ffabf-b12b-4fc4-93be-9684c9047105, Score=0.6509, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] Match: ID=729eb4c6-a6c3-4aed-9a25-aa597db91127, Score=0.6509, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.Plan.Cir.SUB.1/09.27.00/94-95
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '729eb4c6-a6c3-4aed-9a25-aa597db91127']}
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c0891098-62c7-4c42-8d31-7c970dda399f
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 040e1215-100c-44b2-9c5a-325db2ecaf87
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87']...
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,099 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '729eb4c6-a6c3-4aed-9a25-aa597db91127'])] must_not=None, Payload={}
2025-08-02 19:55:58,102 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_master_direction/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-02 19:55:58,102 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '729eb4c6-a6c3-4aed-9a25-aa597db91127']}
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c0891098-62c7-4c42-8d31-7c970dda399f
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 040e1215-100c-44b2-9c5a-325db2ecaf87
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87']...
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,102 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '729eb4c6-a6c3-4aed-9a25-aa597db91127'])] must_not=None, Payload={}
2025-08-02 19:55:58,106 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_master_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:58,111 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 2 matching docs in rbi_master_circular
2025-08-02 19:55:58,130 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_master_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:55:58,130 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8282 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-02 19:55:58,130 - knowledge_base_executor - INFO - ✅ Successfully updated 2 documents in collection: rbi_master_circular
2025-08-02 19:55:58,130 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,130 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:58,131 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '729eb4c6-a6c3-4aed-9a25-aa597db91127']}
2025-08-02 19:55:58,131 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,131 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a
2025-08-02 19:55:58,131 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c0891098-62c7-4c42-8d31-7c970dda399f
2025-08-02 19:55:58,131 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 040e1215-100c-44b2-9c5a-325db2ecaf87
2025-08-02 19:55:58,131 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87']...
2025-08-02 19:55:58,131 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,131 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['166bb57f-c728-44b3-a2f3-7d6f1bf3ac5a', 'c0891098-62c7-4c42-8d31-7c970dda399f', '040e1215-100c-44b2-9c5a-325db2ecaf87', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '729eb4c6-a6c3-4aed-9a25-aa597db91127'])] must_not=None, Payload={}
2025-08-02 19:55:58,138 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:58,142 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 3 matching docs in rbi_circular
2025-08-02 19:55:58,156 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:55:58,156 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=12987 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-02 19:55:58,156 - knowledge_base_executor - INFO - ✅ Successfully updated 3 documents in collection: rbi_circular
2025-08-02 19:55:58,156 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-02T14:25:58.156655"}
2025-08-02 19:55:58,156 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:58,156 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:58,157 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:58,157 - manual_test - INFO - ✅ Action executed successfully: UPDATE_DOCUMENT - UBD.No.Plan.Cir.SUB.1/09.27.00/94-95
2025-08-02 19:55:58,157 - manual_test - INFO - ✅ Action executed successfully: UPDATE_DOCUMENT - UBD.No.Plan.Cir.SUB.1/09.27.00/94-95
2025-08-02 19:55:58,157 - manual_test - INFO - ▶️ Processing action 3/4
2025-08-02 19:55:58,157 - manual_test - INFO - ▶️ Processing action 3/4
2025-08-02 19:55:58,157 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_UBD_No_POT_1_UB_chunk2_325b8af5
2025-08-02 19:55:58,157 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_UBD_No_POT_1_UB_chunk2_325b8af5
2025-08-02 19:55:58,157 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for UBD.No.POT.1/UB.58-92/3
2025-08-02 19:55:58,157 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for UBD.No.POT.1/UB.58-92/3
2025-08-02 19:55:58,157 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: UBD.No.POT.1/UB.58-92/3
2025-08-02 19:55:58,157 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: UBD.No.POT.1/UB.58-92/3
2025-08-02 19:55:58,157 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:55:58,157 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:58,157 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:58,157 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:55:58,157 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:55:58,195 - utils.metadata_vector_utils - INFO - Searching metadata_vectors for 'UBD.No.POT.1/UB.58-92/3' in field 'document_id'
2025-08-02 19:55:58,198 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-02 19:55:58,198 - utils.metadata_vector_utils - INFO - Found 5 results for metadata search
2025-08-02 19:55:58,198 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:55:58,198 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9a0db77f-d109-46ff-9dd5-42e6866482c2, Score=0.5587, Document=DBOD.PSBD.BC.88/16.13.100/2005-06
2025-08-02 19:55:58,198 - knowledge_base_executor - INFO - [QDRANT] Match: ID=529482b7-61a4-4dbc-85f6-082d3171e0ea, Score=0.5521, Document=Ref.No.UBD.DS. 2 /13.02.00/2002-03
2025-08-02 19:55:58,198 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b7aff217-917a-46f2-93ba-ecbc1a1eb565, Score=0.5448, Document=BP.BC.11/21.01.040/99-00
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] Match: ID=998c8368-4404-41db-a8ac-d02d9c525c94, Score=0.5448, Document=BP.BC.11/21.01.040/99-00
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b4358508-03e0-4c1e-b893-bcede5fbbb79, Score=0.5448, Document=BP.BC.11/21.01.040/99-00
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.POT.1/UB.58-92/3
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79']}
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9a0db77f-d109-46ff-9dd5-42e6866482c2
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 529482b7-61a4-4dbc-85f6-082d3171e0ea
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b7aff217-917a-46f2-93ba-ecbc1a1eb565
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565']...
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,199 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79'])] must_not=None, Payload={}
2025-08-02 19:55:58,201 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_master_direction/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-02 19:55:58,201 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79']}
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9a0db77f-d109-46ff-9dd5-42e6866482c2
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 529482b7-61a4-4dbc-85f6-082d3171e0ea
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b7aff217-917a-46f2-93ba-ecbc1a1eb565
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565']...
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,201 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79'])] must_not=None, Payload={}
2025-08-02 19:55:58,203 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_master_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:58,203 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-02 19:55:58,203 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-02 19:55:58,203 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,203 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:58,204 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79']}
2025-08-02 19:55:58,204 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,204 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9a0db77f-d109-46ff-9dd5-42e6866482c2
2025-08-02 19:55:58,204 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 529482b7-61a4-4dbc-85f6-082d3171e0ea
2025-08-02 19:55:58,204 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b7aff217-917a-46f2-93ba-ecbc1a1eb565
2025-08-02 19:55:58,204 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565']...
2025-08-02 19:55:58,204 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,204 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79'])] must_not=None, Payload={}
2025-08-02 19:55:58,218 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:58,229 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-02 19:55:58,271 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:55:58,271 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=12988 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-02 19:55:58,271 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-02 19:55:58,272 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-02T14:25:58.271952"}
2025-08-02 19:55:58,272 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:58,272 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:58,272 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:58,272 - manual_test - INFO - ✅ Action executed successfully: UPDATE_DOCUMENT - UBD.No.POT.1/UB.58-92/3
2025-08-02 19:55:58,272 - manual_test - INFO - ✅ Action executed successfully: UPDATE_DOCUMENT - UBD.No.POT.1/UB.58-92/3
2025-08-02 19:55:58,272 - manual_test - INFO - ▶️ Processing action 4/4
2025-08-02 19:55:58,272 - manual_test - INFO - ▶️ Processing action 4/4
2025-08-02 19:55:58,272 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_UBD_No_I_L_PCB__chunk2_325b8af5
2025-08-02 19:55:58,272 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_UBD_No_I_L_PCB__chunk2_325b8af5
2025-08-02 19:55:58,273 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for UBD.No.I&L/PCB/9/12.05.00/95-96
2025-08-02 19:55:58,273 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for UBD.No.I&L/PCB/9/12.05.00/95-96
2025-08-02 19:55:58,273 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: UBD.No.I&L/PCB/9/12.05.00/95-96
2025-08-02 19:55:58,273 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: UBD.No.I&L/PCB/9/12.05.00/95-96
2025-08-02 19:55:58,273 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:55:58,273 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:58,273 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:58,273 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:55:58,273 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:55:58,281 - utils.metadata_vector_utils - INFO - Searching metadata_vectors for 'UBD.No.I&L/PCB/9/12.05.00/95-96' in field 'document_id'
2025-08-02 19:55:58,285 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-02 19:55:58,285 - utils.metadata_vector_utils - INFO - Found 5 results for metadata search
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3d5b542d-4758-4b8b-9adf-56b5ae78f253, Score=0.6118, Document=RPCD.No.RRB.BC.33/03.05.33(E)/2005-06
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ac35390c-19c4-4447-b048-bb917acfa27f, Score=0.6118, Document=RPCD.No.RRB.BC.33/03.05.33(E)/2005-06
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] Match: ID=bfa6e812-0474-4eba-b9d1-25382c83aa14, Score=0.6081, Document=RPCD.No.RRB.BC.76/03.05.33(C)/2005-06
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] Match: ID=c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae, Score=0.6081, Document=RPCD.No.RRB.BC.76/03.05.33(C)/2005-06
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] Match: ID=05e1db56-5a24-45dd-8de5-570e08a4eea9, Score=0.6046, Document=RPCD.No.RRB.BC.76/03.05.34/96-97
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.I&L/PCB/9/12.05.00/95-96
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9']}
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d5b542d-4758-4b8b-9adf-56b5ae78f253
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ac35390c-19c4-4447-b048-bb917acfa27f
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = bfa6e812-0474-4eba-b9d1-25382c83aa14
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14']...
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,285 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9'])] must_not=None, Payload={}
2025-08-02 19:55:58,289 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_master_direction/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-02 19:55:58,290 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9']}
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d5b542d-4758-4b8b-9adf-56b5ae78f253
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ac35390c-19c4-4447-b048-bb917acfa27f
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = bfa6e812-0474-4eba-b9d1-25382c83aa14
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14']...
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,290 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9'])] must_not=None, Payload={}
2025-08-02 19:55:58,291 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_master_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-02 19:55:58,292 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9']}
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d5b542d-4758-4b8b-9adf-56b5ae78f253
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ac35390c-19c4-4447-b048-bb917acfa27f
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = bfa6e812-0474-4eba-b9d1-25382c83aa14
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14']...
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-02 19:55:58,292 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9'])] must_not=None, Payload={}
2025-08-02 19:55:58,302 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/rbi_circular/points/scroll "HTTP/1.1 200 OK"
2025-08-02 19:55:58,311 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-02 19:55:58,335 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:55:58,335 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=12989 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-02 19:55:58,335 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-02 19:55:58,336 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-02T14:25:58.336009"}
2025-08-02 19:55:58,336 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:55:58,336 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-02 19:55:58,336 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:55:58,336 - manual_test - INFO - ✅ Action executed successfully: UPDATE_DOCUMENT - UBD.No.I&L/PCB/9/12.05.00/95-96
2025-08-02 19:55:58,336 - manual_test - INFO - ✅ Action executed successfully: UPDATE_DOCUMENT - UBD.No.I&L/PCB/9/12.05.00/95-96
2025-08-02 19:55:58,336 - manual_test - INFO - 📊 Chunk action execution: 4/4 successful
2025-08-02 19:55:58,336 - manual_test - INFO - 📊 Chunk action execution: 4/4 successful
2025-08-02 19:55:58,336 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 3:
2025-08-02 19:55:58,336 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 3:
2025-08-02 19:55:58,336 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2025-26/11
2025-08-02 19:55:58,336 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2025-26/11
2025-08-02 19:55:58,336 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2025-26/11
2025-08-02 19:55:58,336 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2025-26/11
2025-08-02 19:55:58,336 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-02 19:55:58,336 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-02 19:55:58,337 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:55:58,337 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:58,337 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:55:58,337 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:55:58,337 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=2d7f10b4-ea32-4160-9397-227437d55cdf, Document=None, Chunk=2
2025-08-02 19:55:58,337 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-02 19:55:58,338 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular/exists "HTTP/1.1 200 OK"
2025-08-02 19:55:58,339 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:55:58,339 - qdrant_utils - INFO - 🔍 Collection rbi_circular vectors: size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:55:58,339 - qdrant_utils - INFO - 🔍 Collection rbi_circular sparse vectors: {'fast-sparse-bm25': SparseVectorParams(index=SparseIndexParams(full_scan_threshold=None, on_disk=None, datatype=None), modifier=<Modifier.IDF: 'idf'>)}
2025-08-02 19:55:58,339 - qdrant_utils - WARNING - Collection rbi_circular exists but uses hybrid/single format. Will work with existing.
2025-08-02 19:55:59,505 - openai_utils - INFO - Embedding 1 documents
2025-08-02 19:56:00,596 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-02 19:56:00,833 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:56:00,834 - qdrant_utils - INFO - Collection info for rbi_circular: vectors=size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:56:00,834 - qdrant_utils - INFO - Collection rbi_circular uses hybrid format (single dense + sparse)
2025-08-02 19:56:00,834 - qdrant_utils - INFO - Collection format: hybrid
2025-08-02 19:56:00,834 - qdrant_utils - INFO - ✅ Added dense vector with 3072 dimensions
2025-08-02 19:56:00,836 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:56:00,836 - qdrant_utils - INFO - ✅ Added sparse vector 'fast-sparse-bm25' with 358 indices
2025-08-02 19:56:00,836 - qdrant_utils - WARNING - ⚠️ Skipping sparse vector 'splade' - not configured in collection schema
2025-08-02 19:56:00,836 - qdrant_utils - INFO - 🔍 Final vector keys for point 2d7f10b4-ea32-4160-9397-227437d55cdf: ['', 'fast-sparse-bm25']
2025-08-02 19:56:00,845 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:56:00,846 - qdrant_utils - INFO - ✅ Using hybrid format for point 2d7f10b4-ea32-4160-9397-227437d55cdf
2025-08-02 19:56:00,846 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 2d7f10b4-ea32-4160-9397-227437d55cdf)
2025-08-02 19:56:00,846 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 2d7f10b4-ea32-4160-9397-227437d55cdf) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:26:00.846130", "document_id": null, "uuid": "2d7f10b4-ea32-4160-9397-227437d55cdf", "collection": "rbi_circular"}
2025-08-02 19:56:00,846 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:56:00,846 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:56:00,846 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:56:00,846 - manual_test - INFO - 🆔 CHUNK UUID: 2d7f10b4-ea32-4160-9397-227437d55cdf
2025-08-02 19:56:00,846 - manual_test - INFO - 🆔 CHUNK UUID: 2d7f10b4-ea32-4160-9397-227437d55cdf
2025-08-02 19:56:00,846 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 3
2025-08-02 19:56:00,846 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 3
2025-08-02 19:56:00,846 - manual_test - INFO -    ✅ Chunk 3 added to processing queue
2025-08-02 19:56:00,846 - manual_test - INFO -    ✅ Chunk 3 added to processing queue
2025-08-02 19:56:00,846 - manual_test - INFO - � Chunk Processing Summary:
2025-08-02 19:56:00,846 - manual_test - INFO - � Chunk Processing Summary:
2025-08-02 19:56:00,846 - manual_test - INFO -    - Total chunks: 3
2025-08-02 19:56:00,846 - manual_test - INFO -    - Total chunks: 3
2025-08-02 19:56:00,847 - manual_test - INFO -    - Indexed chunks: 3
2025-08-02 19:56:00,847 - manual_test - INFO -    - Indexed chunks: 3
2025-08-02 19:56:00,847 - manual_test - INFO -    - Skipped chunks: 0
2025-08-02 19:56:00,847 - manual_test - INFO -    - Skipped chunks: 0
2025-08-02 19:56:00,847 - manual_test - INFO -    - Chunks producing actions: 3
2025-08-02 19:56:00,847 - manual_test - INFO -    - Chunks producing actions: 3
2025-08-02 19:56:00,847 - manual_test - INFO - ✅ STREAMLINED PDF processing: 3 chunks, 6 actions executed
2025-08-02 19:56:00,847 - manual_test - INFO - ✅ STREAMLINED PDF processing: 3 chunks, 6 actions executed
2025-08-02 19:56:00,847 - manual_test - INFO - ✅ Notification 1/1 processed successfully - 0 documents added
2025-08-02 19:56:00,847 - manual_test - INFO - ✅ Notification 1/1 processed successfully - 0 documents added
2025-08-02 19:56:00,847 - manual_test - INFO - 
2025-08-02 19:56:00,847 - manual_test - INFO - 
2025-08-02 19:56:00,847 - manual_test - INFO - 🎯 ═══════════════════════════════════════════════════════════════
2025-08-02 19:56:00,847 - manual_test - INFO - 🎯 ═══════════════════════════════════════════════════════════════
2025-08-02 19:56:00,847 - manual_test - INFO - 🎯 FINAL ENHANCED PIPELINE TEST SUMMARY
2025-08-02 19:56:00,847 - manual_test - INFO - 🎯 FINAL ENHANCED PIPELINE TEST SUMMARY
2025-08-02 19:56:00,847 - manual_test - INFO - 🎯 ═══════════════════════════════════════════════════════════════
2025-08-02 19:56:00,847 - manual_test - INFO - 🎯 ═══════════════════════════════════════════════════════════════
2025-08-02 19:56:00,847 - manual_test - INFO - 📊 Notifications Processed: 1
2025-08-02 19:56:00,847 - manual_test - INFO - 📊 Notifications Processed: 1
2025-08-02 19:56:00,847 - manual_test - INFO - ✅ Successful: 1
2025-08-02 19:56:00,847 - manual_test - INFO - ✅ Successful: 1
2025-08-02 19:56:00,847 - manual_test - INFO - ❌ Failed: 0
2025-08-02 19:56:00,847 - manual_test - INFO - ❌ Failed: 0
2025-08-02 19:56:00,847 - manual_test - INFO - 🆔 Total Documents Added: 0
2025-08-02 19:56:00,847 - manual_test - INFO - 🆔 Total Documents Added: 0
2025-08-02 19:56:00,847 - manual_test - INFO - 📝 Total Documents Updated: 0
2025-08-02 19:56:00,847 - manual_test - INFO - 📝 Total Documents Updated: 0
2025-08-02 19:56:00,848 - manual_test - INFO - ⚡ Total Operations: 0
2025-08-02 19:56:00,848 - manual_test - INFO - ⚡ Total Operations: 0
2025-08-02 19:56:00,848 - manual_test - INFO - 
2025-08-02 19:56:00,848 - manual_test - INFO - 
2025-08-02 19:56:00,848 - manual_test - INFO - 🆔 ALL ADDED DOCUMENT UUIDs (0):
2025-08-02 19:56:00,848 - manual_test - INFO - 🆔 ALL ADDED DOCUMENT UUIDs (0):
2025-08-02 19:56:00,848 - manual_test - INFO - 
2025-08-02 19:56:00,848 - manual_test - INFO - 
2025-08-02 19:56:00,848 - manual_test - INFO - 🎯 ═══════════════════════════════════════════════════════════════
2025-08-02 19:56:00,848 - manual_test - INFO - 🎯 ═══════════════════════════════════════════════════════════════
2025-08-02 19:56:00,850 - manual_test - INFO - ✅ Enhanced test completed! Results saved to real_pipeline_results_enhanced.json
2025-08-02 19:56:00,850 - manual_test - INFO - ✅ Enhanced test completed! Results saved to real_pipeline_results_enhanced.json
2025-08-02 19:56:00,850 - manual_test - INFO - 📊 Summary: 1 successful, 0 failed
2025-08-02 19:56:00,850 - manual_test - INFO - 📊 Summary: 1 successful, 0 failed
2025-08-02 19:56:00,850 - manual_test - INFO - 
2025-08-02 19:56:00,850 - manual_test - INFO - 
2025-08-02 19:56:00,850 - manual_test - INFO - 📋 DETAILED NOTIFICATION RESULTS:
2025-08-02 19:56:00,850 - manual_test - INFO - 📋 DETAILED NOTIFICATION RESULTS:
2025-08-02 19:56:00,850 - manual_test - INFO -   📋  1. Master Circular - Guarantees  Co-Acceptances   Let...
2025-08-02 19:56:00,850 - manual_test - INFO -   📋  1. Master Circular - Guarantees  Co-Acceptances   Let...
2025-08-02 19:56:00,850 - manual_test - INFO -        Status: success | KB: Process: True, Store: True | No UUIDs
2025-08-02 19:56:00,850 - manual_test - INFO -        Status: success | KB: Process: True, Store: True | No UUIDs
2025-08-02 19:56:00,850 - manual_test - INFO - 
2025-08-02 19:56:00,850 - manual_test - INFO - 
2025-08-02 19:56:00,850 - manual_test - INFO - 🎯 OVERALL UUID SUMMARY:
2025-08-02 19:56:00,850 - manual_test - INFO - 🎯 OVERALL UUID SUMMARY:
2025-08-02 19:56:00,850 - manual_test - INFO -    📈 Total Documents Added: 0
2025-08-02 19:56:00,850 - manual_test - INFO -    📈 Total Documents Added: 0
2025-08-02 19:56:00,850 - manual_test - INFO -    📝 Total Documents Updated: 0
2025-08-02 19:56:00,850 - manual_test - INFO -    📝 Total Documents Updated: 0
2025-08-02 19:56:00,850 - manual_test - INFO -    ⚡ Total Operations: 0
2025-08-02 19:56:00,850 - manual_test - INFO -    ⚡ Total Operations: 0
2025-08-02 19:56:00,850 - manual_test - INFO -    🆔 All UUIDs: 0
2025-08-02 19:56:00,850 - manual_test - INFO -    🆔 All UUIDs: 0
