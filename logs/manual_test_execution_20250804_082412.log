2025-08-04 08:24:12,999 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250804_082412.log
2025-08-04 08:24:12,999 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-04 08:24:16,036 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250804_082416.log
2025-08-04 08:24:16,036 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-04 08:24:18,188 - manual_test - INFO - Initialized key rotator with 2 keys
2025-08-04 08:24:18,188 - manual_test - INFO - ✅ Environment setup complete
2025-08-04 08:24:18,189 - manual_test - INFO - ✅ Real notification processor initialized
2025-08-04 08:24:25,496 - manual_test - INFO - ✅ Metadata vector search initialized
2025-08-04 08:24:25,496 - manual_test - INFO - 🧠 Selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-04 08:24:28,791 - manual_test - INFO - ✅ Direct metadata search components initialized
2025-08-04 08:24:28,791 - manual_test - INFO - 🔍 Starting notification analysis for: Amendment to Guidelines on Risk Management - Super...
2025-08-04 08:24:28,791 - manual_test - INFO - 📏 Notification length: 316 characters
2025-08-04 08:24:28,792 - manual_test - INFO - 📝 Prompt created, making LLM call...
2025-08-04 08:24:28,794 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:24:31,348 - manual_test - INFO - ✅ Notification categorized as: Superseded (confidence: high)
2025-08-04 08:24:31,348 - manual_test - INFO - 🔍 Extracting affected documents for: Amendment to Guidelines on Risk Management - Super...
2025-08-04 08:24:31,348 - manual_test - INFO - 📏 Notification length: 316 characters
2025-08-04 08:24:31,355 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:24:38,304 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:24:44,949 - manual_test - INFO - ✅ Found 1 unique document actions (0 removals for supersession)
2025-08-04 08:24:44,949 - manual_test - INFO - 📊 Action reduction: 2 → 1 after deduplication
2025-08-04 08:24:44,950 - manual_test - INFO - 🎯 Determining update actions for: Amendment to Guidelines on Risk Management - Super...
2025-08-04 08:24:44,950 - manual_test - INFO - 📏 Notification length: 316 characters
2025-08-04 08:24:44,958 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:24:47,953 - manual_test - INFO - ✅ Generated action ID: REMOVE_DOCUMENT_RBI_FED_2023-24_15_c39470710_0804_0824
2025-08-04 08:24:47,953 - manual_test - WARNING - ⚠️ Action missing URLs: ['new_document_url', 'rbi_page_url'] for action type: REMOVE_DOCUMENT
2025-08-04 08:24:47,953 - manual_test - INFO - ✅ Generated action ID: ADD_DOCUMENT_RBI_FED_2024-25_XX_c39470710_0804_0824
2025-08-04 08:24:47,954 - manual_test - WARNING - ⚠️ Action missing URLs: ['new_document_url', 'rbi_page_url'] for action type: ADD_DOCUMENT
2025-08-04 08:24:47,954 - manual_test - INFO - ✅ Generated 2 update actions
2025-08-04 08:24:54,601 - manual_test - INFO - Initialized key rotator with 2 keys
2025-08-04 08:24:54,601 - manual_test - INFO - ✅ Environment setup complete
2025-08-04 08:24:54,602 - manual_test - INFO - ✅ Real notification processor initialized
2025-08-04 08:25:01,102 - manual_test - INFO - ✅ Metadata vector search initialized
2025-08-04 08:25:01,102 - manual_test - INFO - 🧠 Selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-04 08:25:04,354 - manual_test - INFO - ✅ Direct metadata search components initialized
2025-08-04 08:25:04,356 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/FED/2023-24/15, Long: 
2025-08-04 08:25:04,357 - manual_test - INFO - 🚀 ENHANCED Processing notification: Revised Guidelines on Risk Management - Supersedes...
2025-08-04 08:25:04,357 - manual_test - INFO - 📊 Initial notification codes from title: {'short_code': 'RBI/FED/2023-24/15', 'long_code': '', 'full_code': 'RBI/FED/2023-24/15', 'year': '2023', 'all_codes': ['RBI/FED/2023-24/15']}
2025-08-04 08:25:04,357 - manual_test - INFO - 📝 Using description content: 274 chars
2025-08-04 08:25:04,357 - manual_test - INFO - ⚠️ No local PDF available, using codes from title only: {'short_code': 'RBI/FED/2023-24/15', 'long_code': '', 'full_code': 'RBI/FED/2023-24/15', 'year': '2023', 'all_codes': ['RBI/FED/2023-24/15']}
2025-08-04 08:25:04,357 - manual_test - INFO - --- ENHANCED Stage 1: Notification Analysis ---
2025-08-04 08:25:04,357 - manual_test - INFO - 🔍 Starting notification analysis for: Revised Guidelines on Risk Management - Supersedes...
2025-08-04 08:25:04,357 - manual_test - INFO - 📏 Notification length: 274 characters
2025-08-04 08:25:04,357 - manual_test - INFO - 📝 Prompt created, making LLM call...
2025-08-04 08:25:04,360 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:25:06,309 - manual_test - INFO - ✅ Notification categorized as: Superseded (confidence: high)
2025-08-04 08:25:06,315 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4.1 for regulatory_interpretation - Selected complex tier for regulatory_interpretation (context: 421, priority: quality)
2025-08-04 08:25:09,408 - manual_test - INFO - 📋 KB Decision: update_existing (store: True, remove: True)
2025-08-04 08:25:09,408 - manual_test - INFO - --- ENHANCED Stage 2: Notification-level KB Actions ---
2025-08-04 08:25:09,408 - manual_test - INFO - notification_codes being passed to KB storage: {'short_code': 'RBI/FED/2023-24/15', 'long_code': '', 'full_code': 'RBI/FED/2023-24/15', 'year': '2023', 'all_codes': ['RBI/FED/2023-24/15']}
2025-08-04 08:25:09,408 - manual_test - INFO - 🔍 Detecting document type for flowchart-based processing...
2025-08-04 08:25:09,408 - manual_test - INFO - 🔍 Detecting document type from title: Revised Guidelines on Risk Management - Supersedes RBI/FED/2023-24/15...
2025-08-04 08:25:09,408 - manual_test - INFO - 📋 Target collection from KB decision: rbi_circular
2025-08-04 08:25:09,408 - manual_test - INFO - ✅ Detected: Other
2025-08-04 08:25:09,408 - manual_test - INFO - 📋 Document Type Detected: other
2025-08-04 08:25:09,408 - manual_test - INFO - 🎯 Generating flowchart-based actions for document type: other
2025-08-04 08:25:09,409 - manual_test - INFO - 📑 Processing Other document actions...
2025-08-04 08:25:09,409 - manual_test - INFO - ✅ Added: ADD_DOCUMENT action for Other document
2025-08-04 08:25:09,409 - manual_test - INFO - 🎯 Generated 1 KB actions based on flowchart:
2025-08-04 08:25:09,409 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT in rbi_other
2025-08-04 08:25:09,409 - manual_test - INFO -       🎯 Target: RBI/FED/2023-24/15
2025-08-04 08:25:09,409 - manual_test - INFO -       🤔 Reasoning: Other notification type - follows flowchart logic
2025-08-04 08:25:09,409 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-04 08:25:09,409 - manual_test - INFO - ✅ Executing 1 flowchart-based KB actions
2025-08-04 08:25:09,409 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT for collection rbi_other
2025-08-04 08:25:13,027 - manual_test - INFO - ✅ Flowchart-based KB Results summary:
2025-08-04 08:25:13,027 - manual_test - INFO -   📋 Result 1: ADD_DOCUMENT - SUCCESS
2025-08-04 08:25:13,027 - manual_test - INFO -       📦 Collection: rbi_other
2025-08-04 08:25:13,027 - manual_test - INFO -       🆔 UUID: 126075d8-8eef-57b7-9250-335f6a572fd6
2025-08-04 08:25:13,027 - manual_test - INFO -       🤔 Reasoning: Other notification type - follows flowchart logic
2025-08-04 08:25:13,027 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-04 08:25:13,027 - manual_test - INFO - 📊 Added 1 documents with UUIDs: ['126075d8-8eef-57b7-9250-335f6a572fd6']
2025-08-04 08:25:13,027 - manual_test - INFO - 📊 Adding notification to metadata_vectors collection for enhanced search
2025-08-04 08:25:15,058 - manual_test - INFO - 🧠 Using smart-selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-04 08:25:18,583 - manual_test - INFO - 🔍 Adding notification to metadata_vectors with ID: 15dcddc7-2d1e-44da-b41e-853a83727db2
2025-08-04 08:25:18,583 - manual_test - INFO - 🔍 Vector keys: ['document_title_vec', 'document_number_vec', 'short_summary_vec', 'document_id_vec']
2025-08-04 08:25:18,584 - manual_test - INFO - 🔍 Vector dimensions: [('document_title_vec', 384), ('document_number_vec', 384), ('short_summary_vec', 384), ('document_id_vec', 384)]
2025-08-04 08:25:18,625 - manual_test - INFO - ✅ Added notification to metadata_vectors: 15dcddc7-2d1e-44da-b41e-853a83727db2
2025-08-04 08:25:18,626 - manual_test - INFO -    📊 Notification metadata vectors UUIDs: 1
2025-08-04 08:25:18,626 - manual_test - INFO - --- ENHANCED Stage 3: STREAMLINED PDF Processing and Action Execution ---
2025-08-04 08:25:18,626 - manual_test - INFO - ⚠️ No local PDF available for streamlined processing
