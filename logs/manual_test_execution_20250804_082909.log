2025-08-04 08:29:09,279 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250804_082909.log
2025-08-04 08:29:09,279 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-04 08:29:12,259 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250804_082912.log
2025-08-04 08:29:12,259 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-04 08:29:14,526 - manual_test - INFO - Initialized key rotator with 2 keys
2025-08-04 08:29:14,526 - manual_test - INFO - ✅ Environment setup complete
2025-08-04 08:29:14,527 - manual_test - INFO - ✅ Real notification processor initialized
2025-08-04 08:29:21,295 - manual_test - INFO - ✅ Metadata vector search initialized
2025-08-04 08:29:21,295 - manual_test - INFO - 🧠 Selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-04 08:29:24,580 - manual_test - INFO - ✅ Direct metadata search components initialized
2025-08-04 08:29:24,580 - manual_test - INFO - 🔍 Starting notification analysis for: Amendment to Guidelines on Risk Management - Super...
2025-08-04 08:29:24,580 - manual_test - INFO - 📏 Notification length: 316 characters
2025-08-04 08:29:24,580 - manual_test - INFO - 📝 Prompt created, making LLM call...
2025-08-04 08:29:24,584 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:29:26,676 - manual_test - INFO - ✅ Notification categorized as: Superseded (confidence: high)
2025-08-04 08:29:26,676 - manual_test - INFO - 🔍 Extracting affected documents for: Amendment to Guidelines on Risk Management - Super...
2025-08-04 08:29:26,676 - manual_test - INFO - 📏 Notification length: 316 characters
2025-08-04 08:29:26,682 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:29:31,074 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:29:36,081 - manual_test - INFO - ✅ Found 1 unique document actions (0 removals for supersession)
2025-08-04 08:29:36,081 - manual_test - INFO - 📊 Action reduction: 2 → 1 after deduplication
2025-08-04 08:29:36,083 - manual_test - INFO - 🎯 Determining update actions for: Amendment to Guidelines on Risk Management - Super...
2025-08-04 08:29:36,083 - manual_test - INFO - 📏 Notification length: 316 characters
2025-08-04 08:29:36,091 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:29:39,159 - manual_test - INFO - ✅ Generated action ID: REMOVE_DOCUMENT_RBI_FED_2023-24_15_c39470710_0804_0829
2025-08-04 08:29:39,160 - manual_test - WARNING - ⚠️ Action missing URLs: ['new_document_url', 'rbi_page_url'] for action type: REMOVE_DOCUMENT
2025-08-04 08:29:39,160 - manual_test - INFO - ✅ Generated action ID: ADD_DOCUMENT_RBI_FED_2024-25_17_c39470710_0804_0829
2025-08-04 08:29:39,160 - manual_test - WARNING - ⚠️ Action missing URLs: ['new_document_url', 'rbi_page_url'] for action type: ADD_DOCUMENT
2025-08-04 08:29:39,161 - manual_test - INFO - ✅ Generated 2 update actions
2025-08-04 08:29:45,914 - manual_test - INFO - Initialized key rotator with 2 keys
2025-08-04 08:29:45,914 - manual_test - INFO - ✅ Environment setup complete
2025-08-04 08:29:45,915 - manual_test - INFO - ✅ Real notification processor initialized
2025-08-04 08:29:52,267 - manual_test - INFO - ✅ Metadata vector search initialized
2025-08-04 08:29:52,267 - manual_test - INFO - 🧠 Selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-04 08:29:55,483 - manual_test - INFO - ✅ Direct metadata search components initialized
2025-08-04 08:29:55,486 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/FED/2023-24/15, Long: 
2025-08-04 08:29:55,486 - manual_test - INFO - 🚀 ENHANCED Processing notification: Revised Guidelines on Risk Management - Supersedes...
2025-08-04 08:29:55,486 - manual_test - INFO - 📊 Initial notification codes from title: {'short_code': 'RBI/FED/2023-24/15', 'long_code': '', 'full_code': 'RBI/FED/2023-24/15', 'year': '2023', 'all_codes': ['RBI/FED/2023-24/15']}
2025-08-04 08:29:55,486 - manual_test - INFO - 📝 Using description content: 274 chars
2025-08-04 08:29:55,486 - manual_test - INFO - ⚠️ No local PDF available, using codes from title only: {'short_code': 'RBI/FED/2023-24/15', 'long_code': '', 'full_code': 'RBI/FED/2023-24/15', 'year': '2023', 'all_codes': ['RBI/FED/2023-24/15']}
2025-08-04 08:29:55,486 - manual_test - INFO - --- ENHANCED Stage 1: Notification Analysis ---
2025-08-04 08:29:55,486 - manual_test - INFO - 🔍 Starting notification analysis for: Revised Guidelines on Risk Management - Supersedes...
2025-08-04 08:29:55,486 - manual_test - INFO - 📏 Notification length: 274 characters
2025-08-04 08:29:55,486 - manual_test - INFO - 📝 Prompt created, making LLM call...
2025-08-04 08:29:55,490 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-04 08:29:57,380 - manual_test - INFO - ✅ Notification categorized as: Superseded (confidence: high)
2025-08-04 08:29:57,385 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4.1 for regulatory_interpretation - Selected complex tier for regulatory_interpretation (context: 421, priority: quality)
2025-08-04 08:29:59,429 - manual_test - INFO - 📋 KB Decision: update_existing (store: True, remove: True)
2025-08-04 08:29:59,430 - manual_test - INFO - --- ENHANCED Stage 2: Notification-level KB Actions ---
2025-08-04 08:29:59,430 - manual_test - INFO - notification_codes being passed to KB storage: {'short_code': 'RBI/FED/2023-24/15', 'long_code': '', 'full_code': 'RBI/FED/2023-24/15', 'year': '2023', 'all_codes': ['RBI/FED/2023-24/15']}
2025-08-04 08:29:59,430 - manual_test - INFO - 🔍 Detecting document type for flowchart-based processing...
2025-08-04 08:29:59,430 - manual_test - INFO - 🔍 Detecting document type from title: Revised Guidelines on Risk Management - Supersedes RBI/FED/2023-24/15...
2025-08-04 08:29:59,430 - manual_test - INFO - 📋 Target collection from KB decision: rbi_circular
2025-08-04 08:29:59,430 - manual_test - INFO - ✅ Detected: Other
2025-08-04 08:29:59,431 - manual_test - INFO - 📋 Document Type Detected: other
2025-08-04 08:29:59,431 - manual_test - INFO - 🎯 Generating flowchart-based actions for document type: other
2025-08-04 08:29:59,431 - manual_test - INFO - 📑 Processing Other document actions...
2025-08-04 08:29:59,431 - manual_test - INFO - ✅ Added: ADD_DOCUMENT action for Other document
2025-08-04 08:29:59,431 - manual_test - INFO - 🎯 Generated 1 KB actions based on flowchart:
2025-08-04 08:29:59,431 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT in rbi_other
2025-08-04 08:29:59,431 - manual_test - INFO -       🎯 Target: RBI/FED/2023-24/15
2025-08-04 08:29:59,431 - manual_test - INFO -       🤔 Reasoning: Other notification type - follows flowchart logic
2025-08-04 08:29:59,431 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-04 08:29:59,432 - manual_test - INFO - ✅ Executing 1 flowchart-based KB actions
2025-08-04 08:29:59,432 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT for collection rbi_other
2025-08-04 08:30:03,315 - manual_test - INFO - ✅ Flowchart-based KB Results summary:
2025-08-04 08:30:03,315 - manual_test - INFO -   📋 Result 1: ADD_DOCUMENT - SUCCESS
2025-08-04 08:30:03,315 - manual_test - INFO -       📦 Collection: rbi_other
2025-08-04 08:30:03,315 - manual_test - INFO -       🆔 UUID: 170b3594-a3a7-5a05-a1ca-7d6874b5df61
2025-08-04 08:30:03,315 - manual_test - INFO -       🤔 Reasoning: Other notification type - follows flowchart logic
2025-08-04 08:30:03,315 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-04 08:30:03,315 - manual_test - INFO - 📊 Added 1 documents with UUIDs: ['170b3594-a3a7-5a05-a1ca-7d6874b5df61']
2025-08-04 08:30:03,315 - manual_test - INFO - 📊 Adding notification to metadata_vectors collection for enhanced search
2025-08-04 08:30:05,361 - manual_test - INFO - 🧠 Using smart-selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-04 08:30:09,205 - manual_test - INFO - 🔍 Adding notification to metadata_vectors with ID: 30b2edc1-27a9-44c8-ab7d-2b77fb049e73
2025-08-04 08:30:09,206 - manual_test - INFO - 🔍 Vector keys: ['document_title_vec', 'document_number_vec', 'short_summary_vec', 'document_id_vec']
2025-08-04 08:30:09,206 - manual_test - INFO - 🔍 Vector dimensions: [('document_title_vec', 384), ('document_number_vec', 384), ('short_summary_vec', 384), ('document_id_vec', 384)]
2025-08-04 08:30:09,248 - manual_test - INFO - ✅ Added notification to metadata_vectors: 30b2edc1-27a9-44c8-ab7d-2b77fb049e73
2025-08-04 08:30:09,248 - manual_test - INFO -    📊 Notification metadata vectors UUIDs: 1
2025-08-04 08:30:09,248 - manual_test - INFO - --- ENHANCED Stage 3: STREAMLINED PDF Processing and Action Execution ---
2025-08-04 08:30:09,248 - manual_test - INFO - ⚠️ No local PDF available for streamlined processing
