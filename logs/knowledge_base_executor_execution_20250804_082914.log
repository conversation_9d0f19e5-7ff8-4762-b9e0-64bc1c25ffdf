2025-08-04 08:29:14,527 - knowledge_base_executor - <PERSON><PERSON><PERSON> - <PERSON><PERSON> configured to write logs to: logs/knowledge_base_executor_execution_20250804_082914.log
2025-08-04 08:29:14,527 - knowledge_base_executor - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-04 08:29:18,065 - knowledge_base_executor - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-04 08:29:39,173 - knowledge_base_executor - INFO - <PERSON><PERSON> configured to write logs to: logs/knowledge_base_executor_execution_20250804_082939.log
2025-08-04 08:29:39,173 - knowledge_base_executor - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-04 08:29:42,504 - knowledge_base_executor - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-04 08:29:42,504 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: REMOVE_DOCUMENT
2025-08-04 08:29:42,504 - knowledge_base_executor - INFO - [QDRANT] Action details: REMOVE_DOCUMENT
2025-08-04 08:29:42,504 - knowledge_base_executor - INFO - 🗑️ [QDRANT] Executing REMOVE_DOCUMENT action: {"action_type": "REMOVE_DOCUMENT", "target_document": "RBI/FED/2023-24/15", "collection_name": ["rbi_circular"], "use_vector_search": true, "document_id": "RBI/FED/2023-24/15", "reasoning": "Superseded by new circular"}
2025-08-04 08:29:42,504 - knowledge_base_executor - INFO - 🔍 [QDRANT] Target collections for removal: ['rbi_circular']
2025-08-04 08:29:42,505 - knowledge_base_executor - INFO - [QDRANT] 🔍 Trying to find documents to remove using vector search
2025-08-04 08:29:42,505 - knowledge_base_executor - INFO - [QDRANT] 🔍 Search parameters: document_id='RBI/FED/2023-24/15', document_number=''
2025-08-04 08:29:45,674 - knowledge_base_executor - INFO - [QDRANT] 🔍 Searching across collections: ['rbi_circular']
2025-08-04 08:29:45,674 - knowledge_base_executor - INFO - [QDRANT] 🔍 SEARCHING BY DOCUMENT ID: 'RBI/FED/2023-24/15'
2025-08-04 08:29:45,779 - knowledge_base_executor - INFO - [QDRANT] 🔍 PERFORMING FULL TEXT SEARCH with: 'RBI/FED/2023-24/15'
2025-08-04 08:29:45,806 - knowledge_base_executor - INFO - [QDRANT] ✅ Found 10 matches in 'short_summary' vector
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT] ➕ Added 10 new unique matches from text search
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT] 🎯 Found total of 10 potential documents to remove via vector search
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT] 📊 FINAL SEARCH RESULTS SUMMARY:
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT] #1. Score: 0.6216 | ID: 38553b01-10a1-45e7-8bc1-dd4a71e02576 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT] #2. Score: 0.6216 | ID: eb015e72-8375-46de-8eaf-d927bcf54102 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT] #3. Score: 0.6216 | ID: 150aabca-3d4b-4722-a7f2-342fa87d5ed9 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT] #4. Score: 0.6216 | ID: bf944bbe-f6bc-4269-9e4a-a72afdfc777d | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT] #5. Score: 0.6216 | ID: f7ee1056-586e-4a86-8a1e-f8d8a08c1361 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT] #6. Score: 0.6216 | ID: 81835ca3-9c65-4491-94a8-6b88ca352557 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,820 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,821 - knowledge_base_executor - INFO - [QDRANT] #7. Score: 0.6216 | ID: f7c4d7aa-f3d9-440d-822d-1bced88e50d8 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,821 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,821 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,821 - knowledge_base_executor - INFO - [QDRANT] #8. Score: 0.6216 | ID: 2f658f80-79c7-4ad0-b52f-5c5ae831dc01 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,821 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,821 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,821 - knowledge_base_executor - INFO - [QDRANT] #9. Score: 0.6216 | ID: d3a7519d-d3e9-4485-bb3a-6b28a1782cf5 | DocID: RBI/2022-23/139 FIDD.CO.FSD.BC.No.13/05.02.001/2022-23
2025-08-04 08:29:45,821 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,821 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,821 - knowledge_base_executor - INFO - [QDRANT] #10. Score: 0.6196 | ID: 0e08f04e-5bd9-4ced-89ed-24b0c9e2c49e | DocID: RBI/2017-18/165  Ref. No. IDMD/2669/08.02.032/2017-18
2025-08-04 08:29:45,821 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-04 08:29:45,821 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-04 08:29:45,821 - knowledge_base_executor - WARNING - [QDRANT] ⚠️ No similar matches found for RBI/FED/2023-24/15, keeping original target
2025-08-04 08:29:45,821 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for REMOVE_DOCUMENT
2025-08-04 08:29:45,821 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action_type', 'target_document', 'collection_name', 'use_vector_search', 'document_id', 'reasoning', 'filter_fields']
2025-08-04 08:29:45,821 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'metadata.document_id': 'RBI/FED/2023-24/15'}
2025-08-04 08:29:45,821 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding filter: metadata.document_id = RBI/FED/2023-24/15
2025-08-04 08:29:45,822 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding metadata filter: metadata.document_id = RBI/FED/2023-24/15
2025-08-04 08:29:45,822 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Created filter with 3 conditions
2025-08-04 08:29:45,822 - knowledge_base_executor - INFO - [QDRANT] REMOVE: Collection=rbi_circular, Filter=should=None min_should=None must=[FieldCondition(key='metadata.document_id', match=MatchValue(value='RBI/FED/2023-24/15'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None), FieldCondition(key='metadata.document_id', match=MatchValue(value='RBI/FED/2023-24/15'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None), FieldCondition(key='document_id', match=MatchValue(value='RBI/FED/2023-24/15'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None)] must_not=None
2025-08-04 08:29:45,822 - knowledge_base_executor - INFO - [QDRANT] REMOVE filter fields: {"metadata.document_id": "RBI/FED/2023-24/15"}
2025-08-04 08:29:45,907 - knowledge_base_executor - WARNING - [QDRANT] ⚠️ No documents found matching filter in rbi_circular
2025-08-04 08:29:45,907 - knowledge_base_executor - INFO - 🔍 [QDRANT] REMOVE operation completed with result: {"action": "REMOVE_DOCUMENT", "success": false, "details": "No matching documents found for ID: RBI/FED/2023-24/15", "timestamp": "2025-08-04T02:59:45.907116"}
2025-08-04 08:29:45,907 - knowledge_base_executor - INFO - [QDRANT] Action REMOVE_DOCUMENT executed with result: {"action": "REMOVE_DOCUMENT", "success": false, "details": "No matching documents found for ID: RBI/FED/2023-24/15", "timestamp": "2025-08-04T02:59:45.907116"}
2025-08-04 08:29:45,907 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-04 08:29:45,907 - knowledge_base_executor - INFO - [QDRANT] Action 1: REMOVE_DOCUMENT - ❌ FAILED
2025-08-04 08:29:45,909 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:29:45,916 - knowledge_base_executor - INFO - Logger configured to write logs to: logs/knowledge_base_executor_execution_20250804_082945.log
2025-08-04 08:29:45,916 - knowledge_base_executor - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-04 08:29:49,145 - knowledge_base_executor - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-04 08:29:59,432 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:29:59,432 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:29:59,432 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=170b3594-a3a7-5a05-a1ca-7d6874b5df61, Document=RBI/FED/2023-24/15, Chunk=None
2025-08-04 08:29:59,432 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/FED/2023-24/15 with text: 568 chars
2025-08-04 08:30:03,313 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/FED/2023-24/15 (ID: 170b3594-a3a7-5a05-a1ca-7d6874b5df61)
2025-08-04 08:30:03,314 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/FED/2023-24/15 (ID: 170b3594-a3a7-5a05-a1ca-7d6874b5df61) to collections: ['rbi_other']", "timestamp": "2025-08-04T03:00:03.313994", "document_id": "RBI/FED/2023-24/15", "uuid": "170b3594-a3a7-5a05-a1ca-7d6874b5df61", "collection": "rbi_other"}
2025-08-04 08:30:03,314 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:30:03,314 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:30:03,315 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
