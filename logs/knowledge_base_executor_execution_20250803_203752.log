2025-08-03 20:37:52,506 - knowledge_base_executor - <PERSON><PERSON><PERSON> - <PERSON><PERSON> configured to write logs to: logs/knowledge_base_executor_execution_20250803_203752.log
2025-08-03 20:37:52,506 - knowledge_base_executor - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-03 20:37:56,123 - knowledge_base_executor - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-03 20:38:12,447 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:38:12,448 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-03 20:38:12,448 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-03 20:38:12,448 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:38:12,448 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=1e2c2208-de4b-55de-bb3c-9a9ff4bd473e, Document=RBI/2024-25/112, Chunk=None
2025-08-03 20:38:12,448 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/112 with text: 6475 chars
2025-08-03 20:38:20,357 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/112 (ID: 1e2c2208-de4b-55de-bb3c-9a9ff4bd473e)
2025-08-03 20:38:20,357 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/112 (ID: 1e2c2208-de4b-55de-bb3c-9a9ff4bd473e) to collections: ['rbi_other']", "timestamp": "2025-08-03T15:08:20.357457", "document_id": "RBI/2024-25/112", "uuid": "1e2c2208-de4b-55de-bb3c-9a9ff4bd473e", "collection": "rbi_other"}
2025-08-03 20:38:20,357 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:38:20,357 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:38:20,359 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:38:33,186 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:38:33,187 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-03 20:38:33,187 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-03 20:38:33,187 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:38:33,187 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=a255182d-f306-546f-99eb-3f9923c70043, Document=RBI/2024-25/112, Chunk=0
2025-08-03 20:38:33,188 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/112 with text: 3340 chars
2025-08-03 20:38:36,974 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/112 (ID: a255182d-f306-546f-99eb-3f9923c70043)
2025-08-03 20:38:36,974 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/112 (ID: a255182d-f306-546f-99eb-3f9923c70043) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-03T15:08:36.974919", "document_id": "RBI/2024-25/112", "uuid": "a255182d-f306-546f-99eb-3f9923c70043", "collection": "rbi_master_direction"}
2025-08-03 20:38:36,974 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:38:36,975 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:38:36,988 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:38:36,989 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:38:36,989 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-03 20:38:36,989 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-03 20:38:36,989 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:38:36,989 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=d1b6a8ef-c1f4-4a99-a10e-8cc87603ea75, Document=None, Chunk=0
2025-08-03 20:38:36,989 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 3229 chars
2025-08-03 20:38:39,350 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: d1b6a8ef-c1f4-4a99-a10e-8cc87603ea75)
2025-08-03 20:38:39,351 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: d1b6a8ef-c1f4-4a99-a10e-8cc87603ea75) to collections: ['rbi_circular']", "timestamp": "2025-08-03T15:08:39.351194", "document_id": null, "uuid": "d1b6a8ef-c1f4-4a99-a10e-8cc87603ea75", "collection": "rbi_circular"}
2025-08-03 20:38:39,351 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:38:39,351 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:38:39,353 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:38:51,791 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:38:51,792 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-03 20:38:51,792 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-03 20:38:51,792 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:38:51,792 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=9f95f16f-4aec-564b-ae89-fb08dedac0f8, Document=RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26, Chunk=None
2025-08-03 20:38:51,792 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26 with text: 8000 chars
2025-08-03 20:38:55,013 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26 (ID: 9f95f16f-4aec-564b-ae89-fb08dedac0f8)
2025-08-03 20:38:55,013 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26 (ID: 9f95f16f-4aec-564b-ae89-fb08dedac0f8) to collections: ['rbi_other']", "timestamp": "2025-08-03T15:08:55.013717", "document_id": "RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26", "uuid": "9f95f16f-4aec-564b-ae89-fb08dedac0f8", "collection": "rbi_other"}
2025-08-03 20:38:55,013 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:38:55,013 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:38:55,017 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:39:01,874 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-03 20:39:01,874 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-03 20:39:01,874 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-03 20:39:01,875 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-03 20:39:01,875 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-03 20:39:02,043 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-03 20:39:02,043 - knowledge_base_executor - INFO - [QDRANT] Match: ID=aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4, Score=0.9121, Document=DoR.LIC.No.S2196/16.13.215/2025-26
2025-08-03 20:39:02,043 - knowledge_base_executor - INFO - [QDRANT] Match: ID=55c9fe74-886e-5477-8ed6-60c7dfda12b8, Score=0.9121, Document=DoR.LIC.No.S2196/16.13.215/2025-26
2025-08-03 20:39:02,043 - knowledge_base_executor - INFO - [QDRANT] Match: ID=869f3b04-ec2f-595b-a89d-b1954df61f84, Score=0.9121, Document=DoR.LIC.No.S2196/16.13.215/2025-26
2025-08-03 20:39:02,043 - knowledge_base_executor - INFO - [QDRANT] Match: ID=1fb27723-a8c3-5847-8444-2413ace14ab0, Score=0.9121, Document=DoR.LIC.No.S2196/16.13.215/2025-26
2025-08-03 20:39:02,043 - knowledge_base_executor - INFO - [QDRANT] Match: ID=a6aac802-03c2-508c-b910-f3a658bc3425, Score=0.9121, Document=DoR.LIC.No.S2196/16.13.215/2025-26
2025-08-03 20:39:02,043 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:39:02,043 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-03 20:39:02,045 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DoR.LIC.No.S1134/16.13.216/2025-26
2025-08-03 20:39:02,045 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', 'a6aac802-03c2-508c-b910-f3a658bc3425']}
2025-08-03 20:39:02,045 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:39:02,045 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4
2025-08-03 20:39:02,045 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 55c9fe74-886e-5477-8ed6-60c7dfda12b8
2025-08-03 20:39:02,045 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 869f3b04-ec2f-595b-a89d-b1954df61f84
2025-08-03 20:39:02,045 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', '869f3b04-ec2f-595b-a89d-b1954df61f84']...
2025-08-03 20:39:02,045 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:39:02,046 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', 'a6aac802-03c2-508c-b910-f3a658bc3425'])] must_not=None, Payload={}
2025-08-03 20:39:02,057 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-03 20:39:02,072 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3174 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-03 20:39:02,072 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-03 20:39:02,072 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:39:02,072 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:39:02,072 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', 'a6aac802-03c2-508c-b910-f3a658bc3425']}
2025-08-03 20:39:02,072 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:39:02,073 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4
2025-08-03 20:39:02,073 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 55c9fe74-886e-5477-8ed6-60c7dfda12b8
2025-08-03 20:39:02,073 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 869f3b04-ec2f-595b-a89d-b1954df61f84
2025-08-03 20:39:02,073 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', '869f3b04-ec2f-595b-a89d-b1954df61f84']...
2025-08-03 20:39:02,073 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:39:02,073 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', 'a6aac802-03c2-508c-b910-f3a658bc3425'])] must_not=None, Payload={}
2025-08-03 20:39:02,075 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-03 20:39:02,075 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-03 20:39:02,075 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:39:02,075 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:39:02,075 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', 'a6aac802-03c2-508c-b910-f3a658bc3425']}
2025-08-03 20:39:02,076 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:39:02,076 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4
2025-08-03 20:39:02,076 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 55c9fe74-886e-5477-8ed6-60c7dfda12b8
2025-08-03 20:39:02,076 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 869f3b04-ec2f-595b-a89d-b1954df61f84
2025-08-03 20:39:02,076 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', '869f3b04-ec2f-595b-a89d-b1954df61f84']...
2025-08-03 20:39:02,076 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:39:02,076 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', 'a6aac802-03c2-508c-b910-f3a658bc3425'])] must_not=None, Payload={}
2025-08-03 20:39:02,080 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-03 20:39:02,081 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-03 20:39:02,081 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-03T15:09:02.081254"}
2025-08-03 20:39:02,081 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:39:02,081 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-03 20:39:02,082 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:39:02,082 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:39:02,082 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-03 20:39:02,082 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-03 20:39:02,082 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:39:02,082 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=12d678c2-7513-476e-b75b-c2f05a899564, Document=None, Chunk=0
2025-08-03 20:39:02,082 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 5161 chars
2025-08-03 20:39:07,361 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 12d678c2-7513-476e-b75b-c2f05a899564)
2025-08-03 20:39:07,361 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 12d678c2-7513-476e-b75b-c2f05a899564) to collections: ['rbi_circular']", "timestamp": "2025-08-03T15:09:07.361283", "document_id": null, "uuid": "12d678c2-7513-476e-b75b-c2f05a899564", "collection": "rbi_circular"}
2025-08-03 20:39:07,361 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:39:07,361 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:39:07,362 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:39:18,864 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:39:18,864 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-03 20:39:18,864 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-03 20:39:18,864 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:39:18,865 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=39a74741-2594-5069-830b-61b11e9f445c, Document=RBI/FED/2025-26/29 - A.P. (DIR Series) Circular No. 17, Chunk=None
2025-08-03 20:39:18,865 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/FED/2025-26/29 - A.P. (DIR Series) Circular No. 17 with text: 8000 chars
2025-08-03 20:39:20,505 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/FED/2025-26/29 - A.P. (DIR Series) Circular No. 17 (ID: 39a74741-2594-5069-830b-61b11e9f445c)
2025-08-03 20:39:20,505 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/FED/2025-26/29 - A.P. (DIR Series) Circular No. 17 (ID: 39a74741-2594-5069-830b-61b11e9f445c) to collections: ['rbi_other']", "timestamp": "2025-08-03T15:09:20.505554", "document_id": "RBI/FED/2025-26/29 - A.P. (DIR Series) Circular No. 17", "uuid": "39a74741-2594-5069-830b-61b11e9f445c", "collection": "rbi_other"}
2025-08-03 20:39:20,505 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:39:20,505 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:39:20,506 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:39:28,136 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-03 20:39:28,136 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-03 20:39:28,136 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-03 20:39:28,136 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-03 20:39:28,136 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-03 20:39:28,230 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-03 20:39:28,230 - knowledge_base_executor - INFO - [QDRANT] Match: ID=36cd09f1-51f3-4944-b799-cd7eb68c96db, Score=0.3964, Document=FEMA 62/2002-RB and related Notifications and A.P. (DIR Series) Circulars consolidated
2025-08-03 20:39:28,230 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5703609a-9362-4eef-8a1b-61dc0cd6d31b, Score=0.3964, Document=FEMA 62/2002-RB and related Notifications and A.P. (DIR Series) Circulars consolidated
2025-08-03 20:39:28,230 - knowledge_base_executor - INFO - [QDRANT] Match: ID=861044f7-0d7e-4975-8746-24862eecb63b, Score=0.3964, Document=FEMA 62/2002-RB and related Notifications and A.P. (DIR Series) Circulars consolidated
2025-08-03 20:39:28,230 - knowledge_base_executor - INFO - [QDRANT] Match: ID=d2f8a0c3-a972-4ba2-8c9e-068a905f738d, Score=0.3964, Document=FEMA 62/2002-RB and related Notifications and A.P. (DIR Series) Circulars consolidated
2025-08-03 20:39:28,230 - knowledge_base_executor - INFO - [QDRANT] Match: ID=73ecb728-548c-4b52-a2d9-b6042464b065, Score=0.3964, Document=FEMA 62/2002-RB and related Notifications and A.P. (DIR Series) Circulars consolidated
2025-08-03 20:39:28,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:39:28,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-03 20:39:28,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'FEMA 1999 Compounding Directions' → 'FEMA1999CompoundingDirections'
2025-08-03 20:39:28,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: FEMA1999CompoundingDirections
2025-08-03 20:39:28,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['36cd09f1-51f3-4944-b799-cd7eb68c96db', '5703609a-9362-4eef-8a1b-61dc0cd6d31b', '861044f7-0d7e-4975-8746-24862eecb63b', 'd2f8a0c3-a972-4ba2-8c9e-068a905f738d', '73ecb728-548c-4b52-a2d9-b6042464b065']}
2025-08-03 20:39:28,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:39:28,230 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 36cd09f1-51f3-4944-b799-cd7eb68c96db
2025-08-03 20:39:28,231 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5703609a-9362-4eef-8a1b-61dc0cd6d31b
2025-08-03 20:39:28,231 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 861044f7-0d7e-4975-8746-24862eecb63b
2025-08-03 20:39:28,231 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['36cd09f1-51f3-4944-b799-cd7eb68c96db', '5703609a-9362-4eef-8a1b-61dc0cd6d31b', '861044f7-0d7e-4975-8746-24862eecb63b']...
2025-08-03 20:39:28,231 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:39:28,231 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['36cd09f1-51f3-4944-b799-cd7eb68c96db', '5703609a-9362-4eef-8a1b-61dc0cd6d31b', '861044f7-0d7e-4975-8746-24862eecb63b', 'd2f8a0c3-a972-4ba2-8c9e-068a905f738d', '73ecb728-548c-4b52-a2d9-b6042464b065'])] must_not=None, Payload={}
2025-08-03 20:39:28,234 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-03 20:39:28,234 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-03 20:39:28,234 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:39:28,234 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:39:28,235 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['36cd09f1-51f3-4944-b799-cd7eb68c96db', '5703609a-9362-4eef-8a1b-61dc0cd6d31b', '861044f7-0d7e-4975-8746-24862eecb63b', 'd2f8a0c3-a972-4ba2-8c9e-068a905f738d', '73ecb728-548c-4b52-a2d9-b6042464b065']}
2025-08-03 20:39:28,235 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:39:28,235 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 36cd09f1-51f3-4944-b799-cd7eb68c96db
2025-08-03 20:39:28,235 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5703609a-9362-4eef-8a1b-61dc0cd6d31b
2025-08-03 20:39:28,235 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 861044f7-0d7e-4975-8746-24862eecb63b
2025-08-03 20:39:28,235 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['36cd09f1-51f3-4944-b799-cd7eb68c96db', '5703609a-9362-4eef-8a1b-61dc0cd6d31b', '861044f7-0d7e-4975-8746-24862eecb63b']...
2025-08-03 20:39:28,235 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:39:28,235 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['36cd09f1-51f3-4944-b799-cd7eb68c96db', '5703609a-9362-4eef-8a1b-61dc0cd6d31b', '861044f7-0d7e-4975-8746-24862eecb63b', 'd2f8a0c3-a972-4ba2-8c9e-068a905f738d', '73ecb728-548c-4b52-a2d9-b6042464b065'])] must_not=None, Payload={}
2025-08-03 20:39:28,250 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_circular
2025-08-03 20:39:28,273 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8283 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-03 20:39:28,273 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_circular
2025-08-03 20:39:28,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:39:28,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:39:28,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['36cd09f1-51f3-4944-b799-cd7eb68c96db', '5703609a-9362-4eef-8a1b-61dc0cd6d31b', '861044f7-0d7e-4975-8746-24862eecb63b', 'd2f8a0c3-a972-4ba2-8c9e-068a905f738d', '73ecb728-548c-4b52-a2d9-b6042464b065']}
2025-08-03 20:39:28,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:39:28,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 36cd09f1-51f3-4944-b799-cd7eb68c96db
2025-08-03 20:39:28,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5703609a-9362-4eef-8a1b-61dc0cd6d31b
2025-08-03 20:39:28,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 861044f7-0d7e-4975-8746-24862eecb63b
2025-08-03 20:39:28,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['36cd09f1-51f3-4944-b799-cd7eb68c96db', '5703609a-9362-4eef-8a1b-61dc0cd6d31b', '861044f7-0d7e-4975-8746-24862eecb63b']...
2025-08-03 20:39:28,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:39:28,273 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['36cd09f1-51f3-4944-b799-cd7eb68c96db', '5703609a-9362-4eef-8a1b-61dc0cd6d31b', '861044f7-0d7e-4975-8746-24862eecb63b', 'd2f8a0c3-a972-4ba2-8c9e-068a905f738d', '73ecb728-548c-4b52-a2d9-b6042464b065'])] must_not=None, Payload={}
2025-08-03 20:39:28,276 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-03 20:39:28,276 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-03 20:39:28,276 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular']", "timestamp": "2025-08-03T15:09:28.276262"}
2025-08-03 20:39:28,276 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:39:28,276 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-03 20:39:28,277 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:39:28,277 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:39:28,277 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-03 20:39:28,277 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-03 20:39:28,277 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:39:28,277 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=733f6262-db19-4623-b6a1-6ba2fc16b4a5, Document=None, Chunk=0
2025-08-03 20:39:28,277 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 1537 chars
2025-08-03 20:39:30,858 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 733f6262-db19-4623-b6a1-6ba2fc16b4a5)
2025-08-03 20:39:30,858 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 733f6262-db19-4623-b6a1-6ba2fc16b4a5) to collections: ['rbi_circular']", "timestamp": "2025-08-03T15:09:30.858126", "document_id": null, "uuid": "733f6262-db19-4623-b6a1-6ba2fc16b4a5", "collection": "rbi_circular"}
2025-08-03 20:39:30,858 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:39:30,858 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:39:30,858 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:39:45,593 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-03 20:39:45,593 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-03 20:39:45,593 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-03 20:39:45,594 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-03 20:39:45,594 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-03 20:39:45,688 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-03 20:39:45,688 - knowledge_base_executor - INFO - [QDRANT] Match: ID=f5227b9a-8ad4-4475-abb1-cad740204786, Score=0.9461, Document=A.P. (DIR Series) Circular No.25
2025-08-03 20:39:45,688 - knowledge_base_executor - INFO - [QDRANT] Match: ID=f7e9c5a4-9d26-40a5-9b97-4698177fce16, Score=0.9461, Document=A.P. (DIR Series) Circular No.25
2025-08-03 20:39:45,688 - knowledge_base_executor - INFO - [QDRANT] Match: ID=08eb16af-79fe-4148-8a1e-296a970e3fb1, Score=0.9450, Document=A.P.(DIR Series) Circular No.17
2025-08-03 20:39:45,688 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2dace538-c7dd-4d91-8a0c-98d413124556, Score=0.9450, Document=A.P.(DIR Series) Circular No.17
2025-08-03 20:39:45,688 - knowledge_base_executor - INFO - [QDRANT] Match: ID=414991ea-c107-4d22-b4f8-1d3c9e37a606, Score=0.9450, Document=A.P.(DIR Series) Circular No.17
2025-08-03 20:39:45,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:39:45,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-03 20:39:45,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'A.P. (DIR Series) Circular No. 17/2024-25' → 'A.P.(DIRSeries)CircularNo.17/2024-25'
2025-08-03 20:39:45,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: A.P.(DIRSeries)CircularNo.17/2024-25
2025-08-03 20:39:45,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['f5227b9a-8ad4-4475-abb1-cad740204786', 'f7e9c5a4-9d26-40a5-9b97-4698177fce16', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '2dace538-c7dd-4d91-8a0c-98d413124556', '414991ea-c107-4d22-b4f8-1d3c9e37a606']}
2025-08-03 20:39:45,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:39:45,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f5227b9a-8ad4-4475-abb1-cad740204786
2025-08-03 20:39:45,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f7e9c5a4-9d26-40a5-9b97-4698177fce16
2025-08-03 20:39:45,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 08eb16af-79fe-4148-8a1e-296a970e3fb1
2025-08-03 20:39:45,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['f5227b9a-8ad4-4475-abb1-cad740204786', 'f7e9c5a4-9d26-40a5-9b97-4698177fce16', '08eb16af-79fe-4148-8a1e-296a970e3fb1']...
2025-08-03 20:39:45,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:39:45,689 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['f5227b9a-8ad4-4475-abb1-cad740204786', 'f7e9c5a4-9d26-40a5-9b97-4698177fce16', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '2dace538-c7dd-4d91-8a0c-98d413124556', '414991ea-c107-4d22-b4f8-1d3c9e37a606'])] must_not=None, Payload={}
2025-08-03 20:39:45,692 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-03 20:39:45,692 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-03 20:39:45,692 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:39:45,692 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:39:45,692 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['f5227b9a-8ad4-4475-abb1-cad740204786', 'f7e9c5a4-9d26-40a5-9b97-4698177fce16', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '2dace538-c7dd-4d91-8a0c-98d413124556', '414991ea-c107-4d22-b4f8-1d3c9e37a606']}
2025-08-03 20:39:45,692 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:39:45,692 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f5227b9a-8ad4-4475-abb1-cad740204786
2025-08-03 20:39:45,692 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f7e9c5a4-9d26-40a5-9b97-4698177fce16
2025-08-03 20:39:45,692 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 08eb16af-79fe-4148-8a1e-296a970e3fb1
2025-08-03 20:39:45,692 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['f5227b9a-8ad4-4475-abb1-cad740204786', 'f7e9c5a4-9d26-40a5-9b97-4698177fce16', '08eb16af-79fe-4148-8a1e-296a970e3fb1']...
2025-08-03 20:39:45,692 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:39:45,692 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['f5227b9a-8ad4-4475-abb1-cad740204786', 'f7e9c5a4-9d26-40a5-9b97-4698177fce16', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '2dace538-c7dd-4d91-8a0c-98d413124556', '414991ea-c107-4d22-b4f8-1d3c9e37a606'])] must_not=None, Payload={}
2025-08-03 20:39:45,694 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-03 20:39:45,694 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-03 20:39:45,695 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:39:45,695 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:39:45,695 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['f5227b9a-8ad4-4475-abb1-cad740204786', 'f7e9c5a4-9d26-40a5-9b97-4698177fce16', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '2dace538-c7dd-4d91-8a0c-98d413124556', '414991ea-c107-4d22-b4f8-1d3c9e37a606']}
2025-08-03 20:39:45,695 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:39:45,695 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f5227b9a-8ad4-4475-abb1-cad740204786
2025-08-03 20:39:45,695 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f7e9c5a4-9d26-40a5-9b97-4698177fce16
2025-08-03 20:39:45,695 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 08eb16af-79fe-4148-8a1e-296a970e3fb1
2025-08-03 20:39:45,695 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['f5227b9a-8ad4-4475-abb1-cad740204786', 'f7e9c5a4-9d26-40a5-9b97-4698177fce16', '08eb16af-79fe-4148-8a1e-296a970e3fb1']...
2025-08-03 20:39:45,695 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:39:45,695 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['f5227b9a-8ad4-4475-abb1-cad740204786', 'f7e9c5a4-9d26-40a5-9b97-4698177fce16', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '2dace538-c7dd-4d91-8a0c-98d413124556', '414991ea-c107-4d22-b4f8-1d3c9e37a606'])] must_not=None, Payload={}
2025-08-03 20:39:45,713 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-03 20:39:45,730 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=12994 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-03 20:39:45,730 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-03 20:39:45,730 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-03T15:09:45.730214"}
2025-08-03 20:39:45,730 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:39:45,730 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-03 20:39:45,731 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:39:45,731 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: REMOVE_DOCUMENT
2025-08-03 20:39:45,731 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-03 20:39:45,731 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-03 20:39:45,731 - knowledge_base_executor - INFO - [QDRANT] Action details: REMOVE_DOCUMENT
2025-08-03 20:39:45,731 - knowledge_base_executor - INFO - 🗑️ [QDRANT] Executing REMOVE_DOCUMENT action: {"action": "REMOVE_DOCUMENT", "action_type": "REMOVE_DOCUMENT", "action_id": "7f5aef20-dcec-4b45-bc49-540112feef5e", "target_document": "A.P. (DIR Series) Circular No. 17/2024-25", "details": "Paragraph 5.4.II.v has been deleted.", "priority": "MEDIUM", "source_chunk": 1, "timestamp": "2025-08-03T20:39:45.731347", "new_document_url": "https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf", "rbi_page_url": "https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf", "collection_name": ["rbi_master_direction", "rbi_master_circular", "rbi_circular"]}
2025-08-03 20:39:45,731 - knowledge_base_executor - INFO - 🔍 [QDRANT] Target collections for removal: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']
2025-08-03 20:39:45,731 - knowledge_base_executor - INFO - [QDRANT] Normalized document ID: 'A.P. (DIR Series) Circular No. 17/2024-25' → 'A.P.(DIRSeries)CircularNo.17/2024-25'
2025-08-03 20:39:45,731 - knowledge_base_executor - INFO - [QDRANT] 🔍 Trying to find documents to remove using vector search
2025-08-03 20:39:45,731 - knowledge_base_executor - INFO - [QDRANT] 🔍 Search parameters: document_id='A.P.(DIRSeries)CircularNo.17/2024-25', document_number=''
2025-08-03 20:39:48,980 - knowledge_base_executor - INFO - [QDRANT] 🔍 Searching across collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']
2025-08-03 20:39:48,980 - knowledge_base_executor - INFO - [QDRANT] 🔍 SEARCHING BY DOCUMENT ID: 'A.P.(DIRSeries)CircularNo.17/2024-25'
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT] ✅ Found 10 matches by document ID
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT] 📋 DOCUMENT ID SEARCH RESULTS:
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]    1. ID=f7e9c5a4-9d26-40a5-9b97-4698177fce16 | Score=0.7786 | DocID=A.P. (DIR Series) Circular No.25 | Title=unknown
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]    2. ID=f5227b9a-8ad4-4475-abb1-cad740204786 | Score=0.7786 | DocID=A.P. (DIR Series) Circular No.25 | Title=unknown
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]    3. ID=dccb41dd-04c5-4864-b9dd-1a1be9596c19 | Score=0.7783 | DocID=A.P. (DIR Series) Circular No.51 | Title=unknown
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]    4. ID=966c7515-5434-485b-9538-331c0baade38 | Score=0.7783 | DocID=A.P. (DIR Series) Circular No.51 | Title=unknown
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]    5. ID=abcfd947-edcc-4c81-a23a-c0deac0b5318 | Score=0.7783 | DocID=A.P.(DIR Series) Circular No.51 | Title=unknown
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]    6. ID=fc5a77a0-6187-4804-a541-9a4e4bf584dd | Score=0.7780 | DocID=A.P.(DIR.Series) Circular No.36 | Title=unknown
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]    7. ID=ce8ae108-5202-4565-a352-57d360ff577f | Score=0.7780 | DocID=A.P.(DIR.Series) Circular No.36 | Title=unknown
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]    8. ID=156bf711-eb9c-4563-8c0b-0a5392593710 | Score=0.7735 | DocID=A.P.(DIR Series) Circular No.35 | Title=unknown
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]    9. ID=81c29b0a-2952-44fe-8ad3-4020a0fdef04 | Score=0.7735 | DocID=A.P. (DIR Series) Circular No. 35 | Title=unknown
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]    10. ID=a7fff53f-a2d8-4730-9114-6027c52b6367 | Score=0.7735 | DocID=A.P. (DIR Series) Circular No.35 | Title=unknown
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT] 🎯 Found total of 10 potential documents to remove via vector search
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT] 📊 FINAL SEARCH RESULTS SUMMARY:
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT] #1. Score: 0.7786 | ID: f7e9c5a4-9d26-40a5-9b97-4698177fce16 | DocID: A.P. (DIR Series) Circular No.25
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT] #2. Score: 0.7786 | ID: f5227b9a-8ad4-4475-abb1-cad740204786 | DocID: A.P. (DIR Series) Circular No.25
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT] #3. Score: 0.7783 | ID: dccb41dd-04c5-4864-b9dd-1a1be9596c19 | DocID: A.P. (DIR Series) Circular No.51
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT] #4. Score: 0.7783 | ID: 966c7515-5434-485b-9538-331c0baade38 | DocID: A.P. (DIR Series) Circular No.51
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT] #5. Score: 0.7783 | ID: abcfd947-edcc-4c81-a23a-c0deac0b5318 | DocID: A.P.(DIR Series) Circular No.51
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT] #6. Score: 0.7780 | ID: fc5a77a0-6187-4804-a541-9a4e4bf584dd | DocID: A.P.(DIR.Series) Circular No.36
2025-08-03 20:39:49,027 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT] #7. Score: 0.7780 | ID: ce8ae108-5202-4565-a352-57d360ff577f | DocID: A.P.(DIR.Series) Circular No.36
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT] #8. Score: 0.7735 | ID: 156bf711-eb9c-4563-8c0b-0a5392593710 | DocID: A.P.(DIR Series) Circular No.35
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT] #9. Score: 0.7735 | ID: 81c29b0a-2952-44fe-8ad3-4020a0fdef04 | DocID: A.P. (DIR Series) Circular No. 35
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT] #10. Score: 0.7735 | ID: a7fff53f-a2d8-4730-9114-6027c52b6367 | DocID: A.P. (DIR Series) Circular No.35
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT]     Title: unknown
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT]     Preview: ...
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT] 🎯 Using extracted document IDs for filter: ['A.P. (DIR Series) Circular No. 35', 'A.P.(DIR Series) Circular No.35', 'A.P. (DIR Series) Circular No.35', 'A.P.(DIR Series) Circular No.51', 'A.P.(DIR.Series) Circular No.36', 'A.P. (DIR Series) Circular No.51', 'A.P. (DIR Series) Circular No.25']
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for REMOVE_DOCUMENT
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'document_id']
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: No filter_fields provided
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding metadata filter: metadata.document_id = A.P. (DIR Series) Circular No. 35
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Created filter with 2 conditions
2025-08-03 20:39:49,028 - knowledge_base_executor - INFO - [QDRANT] REMOVE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[FieldCondition(key='metadata.document_id', match=MatchValue(value='A.P. (DIR Series) Circular No. 35'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None), FieldCondition(key='document_id', match=MatchValue(value='A.P. (DIR Series) Circular No. 35'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None)] must_not=None
2025-08-03 20:39:49,245 - knowledge_base_executor - WARNING - [QDRANT] ⚠️ No documents found matching filter in rbi_master_direction
2025-08-03 20:39:49,245 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for REMOVE_DOCUMENT
2025-08-03 20:39:49,245 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'document_id']
2025-08-03 20:39:49,245 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: No filter_fields provided
2025-08-03 20:39:49,245 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding metadata filter: metadata.document_id = A.P. (DIR Series) Circular No. 35
2025-08-03 20:39:49,246 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Created filter with 2 conditions
2025-08-03 20:39:49,246 - knowledge_base_executor - INFO - [QDRANT] REMOVE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[FieldCondition(key='metadata.document_id', match=MatchValue(value='A.P. (DIR Series) Circular No. 35'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None), FieldCondition(key='document_id', match=MatchValue(value='A.P. (DIR Series) Circular No. 35'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None)] must_not=None
2025-08-03 20:39:50,688 - knowledge_base_executor - WARNING - [QDRANT] ⚠️ No documents found matching filter in rbi_master_circular
2025-08-03 20:39:50,688 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for REMOVE_DOCUMENT
2025-08-03 20:39:50,688 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'document_id']
2025-08-03 20:39:50,688 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: No filter_fields provided
2025-08-03 20:39:50,688 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding metadata filter: metadata.document_id = A.P. (DIR Series) Circular No. 35
2025-08-03 20:39:50,689 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Created filter with 2 conditions
2025-08-03 20:39:50,689 - knowledge_base_executor - INFO - [QDRANT] REMOVE: Collection=rbi_circular, Filter=should=None min_should=None must=[FieldCondition(key='metadata.document_id', match=MatchValue(value='A.P. (DIR Series) Circular No. 35'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None), FieldCondition(key='document_id', match=MatchValue(value='A.P. (DIR Series) Circular No. 35'), range=None, geo_bounding_box=None, geo_radius=None, geo_polygon=None, values_count=None, is_empty=None, is_null=None)] must_not=None
2025-08-03 20:39:51,306 - knowledge_base_executor - WARNING - [QDRANT] ⚠️ No documents found matching filter in rbi_circular
2025-08-03 20:39:51,307 - knowledge_base_executor - INFO - 🔍 [QDRANT] REMOVE operation completed with result: {"action": "REMOVE_DOCUMENT", "success": false, "details": "No matching documents found for ID: A.P. (DIR Series) Circular No. 35", "timestamp": "2025-08-03T15:09:51.307075"}
2025-08-03 20:39:51,308 - knowledge_base_executor - INFO - [QDRANT] Action REMOVE_DOCUMENT executed with result: {"action": "REMOVE_DOCUMENT", "success": false, "details": "No matching documents found for ID: A.P. (DIR Series) Circular No. 35", "timestamp": "2025-08-03T15:09:51.307075"}
2025-08-03 20:39:51,308 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-03 20:39:51,308 - knowledge_base_executor - INFO - [QDRANT] Action 1: REMOVE_DOCUMENT - ❌ FAILED
2025-08-03 20:39:51,310 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:39:51,315 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:39:51,315 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-03 20:39:51,315 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-03 20:39:51,315 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:39:51,315 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=1b685443-7cd4-4cce-9620-f89c49206563, Document=None, Chunk=1
2025-08-03 20:39:51,316 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-03 20:39:55,262 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 1b685443-7cd4-4cce-9620-f89c49206563)
2025-08-03 20:39:55,262 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 1b685443-7cd4-4cce-9620-f89c49206563) to collections: ['rbi_circular']", "timestamp": "2025-08-03T15:09:55.262904", "document_id": null, "uuid": "1b685443-7cd4-4cce-9620-f89c49206563", "collection": "rbi_circular"}
2025-08-03 20:39:55,262 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:39:55,262 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:39:55,263 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:40:05,535 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:40:05,535 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-03 20:40:05,535 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-03 20:40:05,535 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:40:05,536 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=36f50039-5b3d-5172-bc43-420a2f6a324c, Document=RBI/2024-2025/125 - A.P. (DIR Series) Circular No. 22, Chunk=None
2025-08-03 20:40:05,536 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-2025/125 - A.P. (DIR Series) Circular No. 22 with text: 8000 chars
2025-08-03 20:40:08,981 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-2025/125 - A.P. (DIR Series) Circular No. 22 (ID: 36f50039-5b3d-5172-bc43-420a2f6a324c)
2025-08-03 20:40:08,981 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-2025/125 - A.P. (DIR Series) Circular No. 22 (ID: 36f50039-5b3d-5172-bc43-420a2f6a324c) to collections: ['rbi_other']", "timestamp": "2025-08-03T15:10:08.981146", "document_id": "RBI/2024-2025/125 - A.P. (DIR Series) Circular No. 22", "uuid": "36f50039-5b3d-5172-bc43-420a2f6a324c", "collection": "rbi_other"}
2025-08-03 20:40:08,981 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:40:08,981 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:40:08,981 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:40:18,661 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:40:18,661 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-03 20:40:18,661 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-03 20:40:18,661 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:40:18,661 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=5aae259d-6b2a-532a-b637-5b9a4e47db47, Document=RBI/2024-2025/125, Chunk=0
2025-08-03 20:40:18,661 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-2025/125 with text: 5182 chars
2025-08-03 20:40:21,447 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-2025/125 (ID: 5aae259d-6b2a-532a-b637-5b9a4e47db47)
2025-08-03 20:40:21,447 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-2025/125 (ID: 5aae259d-6b2a-532a-b637-5b9a4e47db47) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-03T15:10:21.447653", "document_id": "RBI/2024-2025/125", "uuid": "5aae259d-6b2a-532a-b637-5b9a4e47db47", "collection": "rbi_master_direction"}
2025-08-03 20:40:21,447 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:40:21,447 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:40:21,448 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:40:21,448 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:40:21,448 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-03 20:40:21,448 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-03 20:40:21,448 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:40:21,449 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=70950d15-aae3-469e-a919-0b9ef74d92d2, Document=None, Chunk=0
2025-08-03 20:40:21,449 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 5089 chars
2025-08-03 20:40:25,067 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 70950d15-aae3-469e-a919-0b9ef74d92d2)
2025-08-03 20:40:25,067 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 70950d15-aae3-469e-a919-0b9ef74d92d2) to collections: ['rbi_circular']", "timestamp": "2025-08-03T15:10:25.067140", "document_id": null, "uuid": "70950d15-aae3-469e-a919-0b9ef74d92d2", "collection": "rbi_circular"}
2025-08-03 20:40:25,067 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:40:25,067 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:40:25,067 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:40:33,679 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:40:33,679 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-03 20:40:33,679 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-03 20:40:33,679 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:40:33,680 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=95e3f97b-2b05-59ef-8442-62a757556168, Document=RBI/2024-25/127 - ACC.REC.No.67/21.04.018/2024-25, Chunk=None
2025-08-03 20:40:33,680 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/127 - ACC.REC.No.67/21.04.018/2024-25 with text: 8000 chars
2025-08-03 20:40:36,640 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/127 - ACC.REC.No.67/21.04.018/2024-25 (ID: 95e3f97b-2b05-59ef-8442-62a757556168)
2025-08-03 20:40:36,640 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/127 - ACC.REC.No.67/21.04.018/2024-25 (ID: 95e3f97b-2b05-59ef-8442-62a757556168) to collections: ['rbi_other']", "timestamp": "2025-08-03T15:10:36.640885", "document_id": "RBI/2024-25/127 - ACC.REC.No.67/21.04.018/2024-25", "uuid": "95e3f97b-2b05-59ef-8442-62a757556168", "collection": "rbi_other"}
2025-08-03 20:40:36,640 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:40:36,640 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:40:36,641 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:40:43,412 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-03 20:40:43,412 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-03 20:40:43,412 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-03 20:40:43,412 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-03 20:40:43,412 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-03 20:40:43,505 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-03 20:40:43,505 - knowledge_base_executor - INFO - [QDRANT] Match: ID=d675122a-f2f8-41f1-8672-a9afc3650b80, Score=0.9491, Document=RBI/2023-24/21
2025-08-03 20:40:43,505 - knowledge_base_executor - INFO - [QDRANT] Match: ID=a4084c05-a76e-45a7-91e8-e4f9c9f1cb65, Score=0.9471, Document=RBI/2023-24/81
2025-08-03 20:40:43,505 - knowledge_base_executor - INFO - [QDRANT] Match: ID=8953ae04-2722-42bc-b33e-e7b31633fdca, Score=0.9432, Document=RBI/2023-24/93
2025-08-03 20:40:43,505 - knowledge_base_executor - INFO - [QDRANT] Match: ID=a6de671a-3e3b-43be-84d8-9a293bbf9a0b, Score=0.9374, Document=RBI/2022-23/44
2025-08-03 20:40:43,505 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2db9b912-a821-46d7-805e-758a4f71d6f9, Score=0.9359, Document=RBI/2022-2023/47
2025-08-03 20:40:43,505 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:40:43,505 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-03 20:40:43,505 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: RBI/2023-24/456
2025-08-03 20:40:43,505 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', '2db9b912-a821-46d7-805e-758a4f71d6f9']}
2025-08-03 20:40:43,505 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:40:43,505 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = d675122a-f2f8-41f1-8672-a9afc3650b80
2025-08-03 20:40:43,505 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a4084c05-a76e-45a7-91e8-e4f9c9f1cb65
2025-08-03 20:40:43,505 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8953ae04-2722-42bc-b33e-e7b31633fdca
2025-08-03 20:40:43,505 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca']...
2025-08-03 20:40:43,505 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:40:43,506 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', '2db9b912-a821-46d7-805e-758a4f71d6f9'])] must_not=None, Payload={}
2025-08-03 20:40:43,508 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-03 20:40:43,508 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-03 20:40:43,508 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:40:43,508 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:40:43,508 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', '2db9b912-a821-46d7-805e-758a4f71d6f9']}
2025-08-03 20:40:43,508 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:40:43,508 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = d675122a-f2f8-41f1-8672-a9afc3650b80
2025-08-03 20:40:43,508 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a4084c05-a76e-45a7-91e8-e4f9c9f1cb65
2025-08-03 20:40:43,508 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8953ae04-2722-42bc-b33e-e7b31633fdca
2025-08-03 20:40:43,508 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca']...
2025-08-03 20:40:43,509 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:40:43,509 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', '2db9b912-a821-46d7-805e-758a4f71d6f9'])] must_not=None, Payload={}
2025-08-03 20:40:43,511 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-03 20:40:43,511 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-03 20:40:43,511 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:40:43,511 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:40:43,511 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', '2db9b912-a821-46d7-805e-758a4f71d6f9']}
2025-08-03 20:40:43,511 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:40:43,511 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = d675122a-f2f8-41f1-8672-a9afc3650b80
2025-08-03 20:40:43,511 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a4084c05-a76e-45a7-91e8-e4f9c9f1cb65
2025-08-03 20:40:43,511 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8953ae04-2722-42bc-b33e-e7b31633fdca
2025-08-03 20:40:43,511 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca']...
2025-08-03 20:40:43,511 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:40:43,511 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', '2db9b912-a821-46d7-805e-758a4f71d6f9'])] must_not=None, Payload={}
2025-08-03 20:40:43,528 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-03 20:40:43,544 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=12997 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-03 20:40:43,544 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-03 20:40:43,544 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-03T15:10:43.544831"}
2025-08-03 20:40:43,544 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:40:43,545 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-03 20:40:43,545 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:40:43,545 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:40:43,545 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-03 20:40:43,545 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-03 20:40:43,546 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:40:43,546 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=1d9d59e3-bc66-44a3-be18-cf168f165976, Document=None, Chunk=0
2025-08-03 20:40:43,546 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 99 chars
2025-08-03 20:40:46,691 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 1d9d59e3-bc66-44a3-be18-cf168f165976)
2025-08-03 20:40:46,691 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 1d9d59e3-bc66-44a3-be18-cf168f165976) to collections: ['rbi_circular']", "timestamp": "2025-08-03T15:10:46.691624", "document_id": null, "uuid": "1d9d59e3-bc66-44a3-be18-cf168f165976", "collection": "rbi_circular"}
2025-08-03 20:40:46,691 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:40:46,691 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:40:46,692 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:40:57,708 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-03 20:40:57,708 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-03 20:40:57,708 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-03 20:40:57,709 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-03 20:40:57,709 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-03 20:40:57,768 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-03 20:40:57,768 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ce48e84b-2a10-41b8-81d4-f481a0678441, Score=0.5963, Document=RPCD.CO No.RRB.BC.104/03.05.34/2006-07
2025-08-03 20:40:57,768 - knowledge_base_executor - INFO - [QDRANT] Match: ID=339cc64b-8da8-42a9-9e61-d377340f5b9e, Score=0.5902, Document=RPCD.CO RRB.BC No. 71/03.05.33/2011-12
2025-08-03 20:40:57,768 - knowledge_base_executor - INFO - [QDRANT] Match: ID=c0f520e1-10bf-4761-9366-1d0439a55e15, Score=0.5902, Document=RPCD.CO RRB.BC No. 71/03.05.33/2011-12
2025-08-03 20:40:57,768 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3169c1f4-9bbd-4c35-8ce0-3cf4896395c7, Score=0.5873, Document=RPCD.CO.RRB.BC.No.115/03.05.33/2008-09
2025-08-03 20:40:57,768 - knowledge_base_executor - INFO - [QDRANT] Match: ID=7b74ed9b-3c97-417f-bdc5-8b1af466308c, Score=0.5873, Document=RPCD.CO.RRB.BC.No.115/03.05.33/2008-09
2025-08-03 20:40:57,768 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:40:57,769 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-03 20:40:57,769 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'NABARD circular NB.DoS.Pol.HO/2533/J-1/2019-20' → 'NABARDcircularNB.DoS.Pol.HO/2533/J-1/2019-20'
2025-08-03 20:40:57,769 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: NABARDcircularNB.DoS.Pol.HO/2533/J-1/2019-20
2025-08-03 20:40:57,769 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15', '3169c1f4-9bbd-4c35-8ce0-3cf4896395c7', '7b74ed9b-3c97-417f-bdc5-8b1af466308c']}
2025-08-03 20:40:57,769 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:40:57,769 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ce48e84b-2a10-41b8-81d4-f481a0678441
2025-08-03 20:40:57,769 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 339cc64b-8da8-42a9-9e61-d377340f5b9e
2025-08-03 20:40:57,769 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c0f520e1-10bf-4761-9366-1d0439a55e15
2025-08-03 20:40:57,769 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15']...
2025-08-03 20:40:57,769 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:40:57,769 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15', '3169c1f4-9bbd-4c35-8ce0-3cf4896395c7', '7b74ed9b-3c97-417f-bdc5-8b1af466308c'])] must_not=None, Payload={}
2025-08-03 20:40:57,774 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-03 20:40:57,774 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-03 20:40:57,774 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:40:57,774 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:40:57,774 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15', '3169c1f4-9bbd-4c35-8ce0-3cf4896395c7', '7b74ed9b-3c97-417f-bdc5-8b1af466308c']}
2025-08-03 20:40:57,774 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:40:57,774 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ce48e84b-2a10-41b8-81d4-f481a0678441
2025-08-03 20:40:57,774 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 339cc64b-8da8-42a9-9e61-d377340f5b9e
2025-08-03 20:40:57,774 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c0f520e1-10bf-4761-9366-1d0439a55e15
2025-08-03 20:40:57,775 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15']...
2025-08-03 20:40:57,775 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:40:57,775 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15', '3169c1f4-9bbd-4c35-8ce0-3cf4896395c7', '7b74ed9b-3c97-417f-bdc5-8b1af466308c'])] must_not=None, Payload={}
2025-08-03 20:40:57,778 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-03 20:40:57,778 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-03 20:40:57,778 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:40:57,778 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:40:57,778 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15', '3169c1f4-9bbd-4c35-8ce0-3cf4896395c7', '7b74ed9b-3c97-417f-bdc5-8b1af466308c']}
2025-08-03 20:40:57,778 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:40:57,778 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ce48e84b-2a10-41b8-81d4-f481a0678441
2025-08-03 20:40:57,778 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 339cc64b-8da8-42a9-9e61-d377340f5b9e
2025-08-03 20:40:57,778 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c0f520e1-10bf-4761-9366-1d0439a55e15
2025-08-03 20:40:57,778 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15']...
2025-08-03 20:40:57,778 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:40:57,778 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15', '3169c1f4-9bbd-4c35-8ce0-3cf4896395c7', '7b74ed9b-3c97-417f-bdc5-8b1af466308c'])] must_not=None, Payload={}
2025-08-03 20:40:57,793 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-03 20:40:57,813 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=12999 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-03 20:40:57,813 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-03 20:40:57,814 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-03T15:10:57.813941"}
2025-08-03 20:40:57,814 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:40:57,814 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-03 20:40:57,815 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:40:57,815 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-03 20:40:57,815 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-03 20:40:57,815 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-03 20:40:57,815 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-03 20:40:57,815 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] Match: ID=759a4091-ddd1-4f1d-8c67-d0fe16e111db, Score=0.5672, Document=RBI/2017-18/203 FIDD.CO.Plan.BC.22/04.09.01/2017-18
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] Match: ID=a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e, Score=0.5672, Document=RBI/2017-18/203  FIDD.CO.Plan.BC.22/04.09.01/2017-18
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] Match: ID=8261f81f-e857-46c4-b9c6-0e3ddca0bc68, Score=0.5672, Document=RBI/2017-18/203  FIDD.CO.Plan.BC.22/04.09.01/2017-18
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ce1d93f5-7444-4a0c-b4e4-c8b3057c085d, Score=0.5672, Document=RBI/2017-18/203 FIDD.CO.Plan.BC.22/04.09.01/2017-18
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] Match: ID=09e5bde4-abd1-49fa-a1fe-36bc3475f196, Score=0.5534, Document=RBI/2007-2008/159 RPCD.CO.Plan.BC.  30 /04.09.01/2007-08
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'Reserve Bank of India (Financial Statements - Presentation and Disclosures) Directions, 2021' → 'ReserveBankofIndia(FinancialStatements-PresentationandDisclosures)Directions,2021'
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: ReserveBankofIndia(FinancialStatements-PresentationandDisclosures)Directions,2021
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['759a4091-ddd1-4f1d-8c67-d0fe16e111db', 'a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '09e5bde4-abd1-49fa-a1fe-36bc3475f196']}
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 759a4091-ddd1-4f1d-8c67-d0fe16e111db
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8261f81f-e857-46c4-b9c6-0e3ddca0bc68
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['759a4091-ddd1-4f1d-8c67-d0fe16e111db', 'a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68']...
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:40:57,864 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['759a4091-ddd1-4f1d-8c67-d0fe16e111db', 'a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '09e5bde4-abd1-49fa-a1fe-36bc3475f196'])] must_not=None, Payload={}
2025-08-03 20:40:57,866 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-03 20:40:57,866 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-03 20:40:57,866 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:40:57,866 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:40:57,866 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['759a4091-ddd1-4f1d-8c67-d0fe16e111db', 'a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '09e5bde4-abd1-49fa-a1fe-36bc3475f196']}
2025-08-03 20:40:57,866 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:40:57,866 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 759a4091-ddd1-4f1d-8c67-d0fe16e111db
2025-08-03 20:40:57,866 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e
2025-08-03 20:40:57,866 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8261f81f-e857-46c4-b9c6-0e3ddca0bc68
2025-08-03 20:40:57,866 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['759a4091-ddd1-4f1d-8c67-d0fe16e111db', 'a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68']...
2025-08-03 20:40:57,866 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:40:57,866 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['759a4091-ddd1-4f1d-8c67-d0fe16e111db', 'a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '09e5bde4-abd1-49fa-a1fe-36bc3475f196'])] must_not=None, Payload={}
2025-08-03 20:40:57,868 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-03 20:40:57,868 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-03 20:40:57,868 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:40:57,868 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:40:57,868 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['759a4091-ddd1-4f1d-8c67-d0fe16e111db', 'a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '09e5bde4-abd1-49fa-a1fe-36bc3475f196']}
2025-08-03 20:40:57,868 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:40:57,868 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 759a4091-ddd1-4f1d-8c67-d0fe16e111db
2025-08-03 20:40:57,868 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e
2025-08-03 20:40:57,868 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8261f81f-e857-46c4-b9c6-0e3ddca0bc68
2025-08-03 20:40:57,868 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['759a4091-ddd1-4f1d-8c67-d0fe16e111db', 'a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68']...
2025-08-03 20:40:57,868 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:40:57,868 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['759a4091-ddd1-4f1d-8c67-d0fe16e111db', 'a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '09e5bde4-abd1-49fa-a1fe-36bc3475f196'])] must_not=None, Payload={}
2025-08-03 20:40:57,877 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-03 20:40:57,891 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13000 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-03 20:40:57,891 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-03 20:40:57,891 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-03T15:10:57.891436"}
2025-08-03 20:40:57,891 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:40:57,891 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-03 20:40:57,892 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:40:57,892 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:40:57,892 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-03 20:40:57,892 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-03 20:40:57,892 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:40:57,892 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=4d4edd05-b0c3-4308-b060-6d8b74092912, Document=None, Chunk=1
2025-08-03 20:40:57,892 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-03 20:41:00,784 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 4d4edd05-b0c3-4308-b060-6d8b74092912)
2025-08-03 20:41:00,784 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 4d4edd05-b0c3-4308-b060-6d8b74092912) to collections: ['rbi_circular']", "timestamp": "2025-08-03T15:11:00.784953", "document_id": null, "uuid": "4d4edd05-b0c3-4308-b060-6d8b74092912", "collection": "rbi_circular"}
2025-08-03 20:41:00,785 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:41:00,785 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:41:00,785 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:41:10,999 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:41:10,999 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-03 20:41:11,000 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-03 20:41:11,000 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:41:11,000 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=d401c0c1-98de-529e-aefe-4e804b28e6c4, Document=RBI/2024-25/124, Chunk=None
2025-08-03 20:41:11,000 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/124 with text: 8000 chars
2025-08-03 20:41:13,979 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/124 (ID: d401c0c1-98de-529e-aefe-4e804b28e6c4)
2025-08-03 20:41:13,980 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/124 (ID: d401c0c1-98de-529e-aefe-4e804b28e6c4) to collections: ['rbi_other']", "timestamp": "2025-08-03T15:11:13.980091", "document_id": "RBI/2024-25/124", "uuid": "d401c0c1-98de-529e-aefe-4e804b28e6c4", "collection": "rbi_other"}
2025-08-03 20:41:13,980 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:41:13,980 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:41:13,981 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:41:22,299 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:41:22,299 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-03 20:41:22,300 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-03 20:41:22,300 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:41:22,300 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=da5081e3-8580-5a45-bcc4-24e53e958f23, Document=RBI/2024-25/124, Chunk=0
2025-08-03 20:41:22,300 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/124 with text: 4506 chars
2025-08-03 20:41:25,104 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/124 (ID: da5081e3-8580-5a45-bcc4-24e53e958f23)
2025-08-03 20:41:25,104 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/124 (ID: da5081e3-8580-5a45-bcc4-24e53e958f23) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-03T15:11:25.104573", "document_id": "RBI/2024-25/124", "uuid": "da5081e3-8580-5a45-bcc4-24e53e958f23", "collection": "rbi_master_direction"}
2025-08-03 20:41:25,104 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:41:25,104 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:41:25,105 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:41:25,105 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:41:25,105 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-03 20:41:25,105 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-03 20:41:25,105 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:41:25,105 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=51e555e8-2923-4406-a616-129f542f80a2, Document=None, Chunk=0
2025-08-03 20:41:25,105 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 4397 chars
2025-08-03 20:41:27,566 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 51e555e8-2923-4406-a616-129f542f80a2)
2025-08-03 20:41:27,566 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 51e555e8-2923-4406-a616-129f542f80a2) to collections: ['rbi_circular']", "timestamp": "2025-08-03T15:11:27.566773", "document_id": null, "uuid": "51e555e8-2923-4406-a616-129f542f80a2", "collection": "rbi_circular"}
2025-08-03 20:41:27,566 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:41:27,566 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:41:27,567 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:41:37,477 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:41:37,477 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-03 20:41:37,477 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-03 20:41:37,478 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:41:37,478 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=504682fe-5184-549d-9fce-822d343bf0d2, Document=RBI/2025-26/28, Chunk=None
2025-08-03 20:41:37,478 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/28 with text: 8000 chars
2025-08-03 20:41:41,047 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/28 (ID: 504682fe-5184-549d-9fce-822d343bf0d2)
2025-08-03 20:41:41,047 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/28 (ID: 504682fe-5184-549d-9fce-822d343bf0d2) to collections: ['rbi_circular']", "timestamp": "2025-08-03T15:11:41.047643", "document_id": "RBI/2025-26/28", "uuid": "504682fe-5184-549d-9fce-822d343bf0d2", "collection": "rbi_circular"}
2025-08-03 20:41:41,047 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:41:41,047 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:41:41,048 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:41:47,725 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:41:47,725 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-03 20:41:47,725 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-03 20:41:47,725 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:41:47,725 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=be3ff522-ece8-570d-a310-7a036fdeb46d, Document=RBI/2025-26/28, Chunk=0
2025-08-03 20:41:47,726 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/28 with text: 4737 chars
2025-08-03 20:41:49,653 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/28 (ID: be3ff522-ece8-570d-a310-7a036fdeb46d)
2025-08-03 20:41:49,653 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/28 (ID: be3ff522-ece8-570d-a310-7a036fdeb46d) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-03T15:11:49.653645", "document_id": "RBI/2025-26/28", "uuid": "be3ff522-ece8-570d-a310-7a036fdeb46d", "collection": "rbi_master_direction"}
2025-08-03 20:41:49,653 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:41:49,653 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:41:49,654 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:41:49,654 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:41:49,654 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-03 20:41:49,654 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-03 20:41:49,654 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:41:49,654 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=38d46e3e-4617-4cfc-bb95-715181977db7, Document=None, Chunk=0
2025-08-03 20:41:49,654 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 4621 chars
2025-08-03 20:41:52,585 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 38d46e3e-4617-4cfc-bb95-715181977db7)
2025-08-03 20:41:52,586 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 38d46e3e-4617-4cfc-bb95-715181977db7) to collections: ['rbi_circular']", "timestamp": "2025-08-03T15:11:52.585982", "document_id": null, "uuid": "38d46e3e-4617-4cfc-bb95-715181977db7", "collection": "rbi_circular"}
2025-08-03 20:41:52,586 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:41:52,586 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:41:52,586 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:42:05,278 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:42:05,278 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-03 20:42:05,278 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-03 20:42:05,279 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:42:05,279 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=0797aece-b5f3-5084-a33d-0ae0532dd841, Document=RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25, Chunk=None
2025-08-03 20:42:05,279 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 with text: 8000 chars
2025-08-03 20:42:09,913 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 (ID: 0797aece-b5f3-5084-a33d-0ae0532dd841)
2025-08-03 20:42:09,913 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 (ID: 0797aece-b5f3-5084-a33d-0ae0532dd841) to collections: ['rbi_other']", "timestamp": "2025-08-03T15:12:09.913782", "document_id": "RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25", "uuid": "0797aece-b5f3-5084-a33d-0ae0532dd841", "collection": "rbi_other"}
2025-08-03 20:42:09,913 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:42:09,913 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:42:09,914 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:42:16,280 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-03 20:42:16,280 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-03 20:42:16,280 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-03 20:42:16,280 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-03 20:42:16,280 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-03 20:42:16,346 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-03 20:42:16,346 - knowledge_base_executor - INFO - [QDRANT] Match: ID=45e1d982-dbae-4bda-9493-52ab74735756, Score=0.9639, Document=DoR.RET.REC.12/12.01.001/2024-25
2025-08-03 20:42:16,346 - knowledge_base_executor - INFO - [QDRANT] Match: ID=c5be7ccb-7df4-4373-b38a-b80bf27765b6, Score=0.8551, Document=DOR.CRE.REC.No.06/08.12.001/2023-24
2025-08-03 20:42:16,346 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3046fedf-70e3-43fe-9f6b-f692f28a8155, Score=0.8551, Document=DOR.CRE.REC.No.06/08.12.001/2023-24
2025-08-03 20:42:16,346 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2c405a9a-7bbb-4593-9431-48dad91c3143, Score=0.8551, Document=DOR.CRE.REC.No.06/08.12.001/2023-24
2025-08-03 20:42:16,346 - knowledge_base_executor - INFO - [QDRANT] Match: ID=68c4dd81-8634-4593-bdbd-002052e819a0, Score=0.8551, Document=DOR.CRE.REC.No.06/08.12.001/2023-24
2025-08-03 20:42:16,346 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:42:16,346 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-03 20:42:16,346 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DOR.RET.REC.101/12.01.001/2022-23
2025-08-03 20:42:16,346 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['45e1d982-dbae-4bda-9493-52ab74735756', 'c5be7ccb-7df4-4373-b38a-b80bf27765b6', '3046fedf-70e3-43fe-9f6b-f692f28a8155', '2c405a9a-7bbb-4593-9431-48dad91c3143', '68c4dd81-8634-4593-bdbd-002052e819a0']}
2025-08-03 20:42:16,346 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:42:16,346 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 45e1d982-dbae-4bda-9493-52ab74735756
2025-08-03 20:42:16,346 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c5be7ccb-7df4-4373-b38a-b80bf27765b6
2025-08-03 20:42:16,347 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3046fedf-70e3-43fe-9f6b-f692f28a8155
2025-08-03 20:42:16,347 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['45e1d982-dbae-4bda-9493-52ab74735756', 'c5be7ccb-7df4-4373-b38a-b80bf27765b6', '3046fedf-70e3-43fe-9f6b-f692f28a8155']...
2025-08-03 20:42:16,347 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:42:16,347 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['45e1d982-dbae-4bda-9493-52ab74735756', 'c5be7ccb-7df4-4373-b38a-b80bf27765b6', '3046fedf-70e3-43fe-9f6b-f692f28a8155', '2c405a9a-7bbb-4593-9431-48dad91c3143', '68c4dd81-8634-4593-bdbd-002052e819a0'])] must_not=None, Payload={}
2025-08-03 20:42:16,350 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-03 20:42:16,350 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-03 20:42:16,351 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:42:16,351 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:42:16,351 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['45e1d982-dbae-4bda-9493-52ab74735756', 'c5be7ccb-7df4-4373-b38a-b80bf27765b6', '3046fedf-70e3-43fe-9f6b-f692f28a8155', '2c405a9a-7bbb-4593-9431-48dad91c3143', '68c4dd81-8634-4593-bdbd-002052e819a0']}
2025-08-03 20:42:16,351 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:42:16,351 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 45e1d982-dbae-4bda-9493-52ab74735756
2025-08-03 20:42:16,351 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c5be7ccb-7df4-4373-b38a-b80bf27765b6
2025-08-03 20:42:16,351 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3046fedf-70e3-43fe-9f6b-f692f28a8155
2025-08-03 20:42:16,351 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['45e1d982-dbae-4bda-9493-52ab74735756', 'c5be7ccb-7df4-4373-b38a-b80bf27765b6', '3046fedf-70e3-43fe-9f6b-f692f28a8155']...
2025-08-03 20:42:16,351 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:42:16,351 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['45e1d982-dbae-4bda-9493-52ab74735756', 'c5be7ccb-7df4-4373-b38a-b80bf27765b6', '3046fedf-70e3-43fe-9f6b-f692f28a8155', '2c405a9a-7bbb-4593-9431-48dad91c3143', '68c4dd81-8634-4593-bdbd-002052e819a0'])] must_not=None, Payload={}
2025-08-03 20:42:16,365 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 4 matching docs in rbi_master_circular
2025-08-03 20:42:16,383 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8284 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-03 20:42:16,383 - knowledge_base_executor - INFO - ✅ Successfully updated 4 documents in collection: rbi_master_circular
2025-08-03 20:42:16,383 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:42:16,383 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:42:16,383 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['45e1d982-dbae-4bda-9493-52ab74735756', 'c5be7ccb-7df4-4373-b38a-b80bf27765b6', '3046fedf-70e3-43fe-9f6b-f692f28a8155', '2c405a9a-7bbb-4593-9431-48dad91c3143', '68c4dd81-8634-4593-bdbd-002052e819a0']}
2025-08-03 20:42:16,383 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:42:16,383 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 45e1d982-dbae-4bda-9493-52ab74735756
2025-08-03 20:42:16,383 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c5be7ccb-7df4-4373-b38a-b80bf27765b6
2025-08-03 20:42:16,383 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3046fedf-70e3-43fe-9f6b-f692f28a8155
2025-08-03 20:42:16,383 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['45e1d982-dbae-4bda-9493-52ab74735756', 'c5be7ccb-7df4-4373-b38a-b80bf27765b6', '3046fedf-70e3-43fe-9f6b-f692f28a8155']...
2025-08-03 20:42:16,383 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:42:16,383 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['45e1d982-dbae-4bda-9493-52ab74735756', 'c5be7ccb-7df4-4373-b38a-b80bf27765b6', '3046fedf-70e3-43fe-9f6b-f692f28a8155', '2c405a9a-7bbb-4593-9431-48dad91c3143', '68c4dd81-8634-4593-bdbd-002052e819a0'])] must_not=None, Payload={}
2025-08-03 20:42:16,388 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 1 matching docs in rbi_circular
2025-08-03 20:42:16,393 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13005 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-03 20:42:16,393 - knowledge_base_executor - INFO - ✅ Successfully updated 1 documents in collection: rbi_circular
2025-08-03 20:42:16,394 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-03T15:12:16.393959"}
2025-08-03 20:42:16,394 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:42:16,394 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-03 20:42:16,395 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:42:16,395 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:42:16,395 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-03 20:42:16,395 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-03 20:42:16,395 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:42:16,395 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=2535cb92-7818-466d-8637-9ed5d1b6cf0e, Document=None, Chunk=0
2025-08-03 20:42:16,395 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 5846 chars
2025-08-03 20:42:20,113 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 2535cb92-7818-466d-8637-9ed5d1b6cf0e)
2025-08-03 20:42:20,113 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 2535cb92-7818-466d-8637-9ed5d1b6cf0e) to collections: ['rbi_circular']", "timestamp": "2025-08-03T15:12:20.113163", "document_id": null, "uuid": "2535cb92-7818-466d-8637-9ed5d1b6cf0e", "collection": "rbi_circular"}
2025-08-03 20:42:20,114 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:42:20,114 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:42:20,115 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:42:29,208 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:42:29,209 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-03 20:42:29,209 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-03 20:42:29,209 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:42:29,209 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=9a4186c2-c510-5e8f-89bd-5da8b8e1710d, Document=RBI/2024-25/129, Chunk=None
2025-08-03 20:42:29,209 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/129 with text: 8000 chars
2025-08-03 20:42:30,630 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/129 (ID: 9a4186c2-c510-5e8f-89bd-5da8b8e1710d)
2025-08-03 20:42:30,630 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/129 (ID: 9a4186c2-c510-5e8f-89bd-5da8b8e1710d) to collections: ['rbi_other']", "timestamp": "2025-08-03T15:12:30.630088", "document_id": "RBI/2024-25/129", "uuid": "9a4186c2-c510-5e8f-89bd-5da8b8e1710d", "collection": "rbi_other"}
2025-08-03 20:42:30,630 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:42:30,630 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:42:30,630 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:42:36,537 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-03 20:42:36,537 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-03 20:42:36,537 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-03 20:42:36,538 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-03 20:42:36,538 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] Match: ID=35918048-dd38-4dc2-897c-c169f5aefc30, Score=0.7409, Document=RBI/2024-25/112 DOR.CO.SOG(Leg) No.59/09.08.024/2024-25
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9741a628-999e-4b3f-aa8d-da299fe6f6c1, Score=0.7197, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3d495fa6-6797-461c-bf81-7de0eb59a24a, Score=0.7197, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2d0ef267-a504-424c-b287-4a442abd8753, Score=0.6951, Document=LEG.BC.114/09.06.002/2000-01
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] Match: ID=318b01bb-01bb-49f9-94b6-ece19001b6ba, Score=0.6893, Document=RBI/2023-24/105  DOR.SOG (LEG).REC/64/09.08.024/2023-24
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DOR.CO.SOG(Leg)No.59/09.08.024/2024-25
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2d0ef267-a504-424c-b287-4a442abd8753', '318b01bb-01bb-49f9-94b6-ece19001b6ba']}
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 35918048-dd38-4dc2-897c-c169f5aefc30
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a']...
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:42:36,625 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2d0ef267-a504-424c-b287-4a442abd8753', '318b01bb-01bb-49f9-94b6-ece19001b6ba'])] must_not=None, Payload={}
2025-08-03 20:42:36,628 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-03 20:42:36,628 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-03 20:42:36,628 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:42:36,629 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:42:36,629 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2d0ef267-a504-424c-b287-4a442abd8753', '318b01bb-01bb-49f9-94b6-ece19001b6ba']}
2025-08-03 20:42:36,629 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:42:36,629 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 35918048-dd38-4dc2-897c-c169f5aefc30
2025-08-03 20:42:36,629 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-03 20:42:36,629 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-03 20:42:36,629 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a']...
2025-08-03 20:42:36,629 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:42:36,629 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2d0ef267-a504-424c-b287-4a442abd8753', '318b01bb-01bb-49f9-94b6-ece19001b6ba'])] must_not=None, Payload={}
2025-08-03 20:42:36,631 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-03 20:42:36,631 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-03 20:42:36,631 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:42:36,631 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:42:36,631 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2d0ef267-a504-424c-b287-4a442abd8753', '318b01bb-01bb-49f9-94b6-ece19001b6ba']}
2025-08-03 20:42:36,631 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:42:36,631 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 35918048-dd38-4dc2-897c-c169f5aefc30
2025-08-03 20:42:36,632 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-03 20:42:36,632 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-03 20:42:36,632 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a']...
2025-08-03 20:42:36,632 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:42:36,632 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2d0ef267-a504-424c-b287-4a442abd8753', '318b01bb-01bb-49f9-94b6-ece19001b6ba'])] must_not=None, Payload={}
2025-08-03 20:42:36,653 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-03 20:42:36,670 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13007 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-03 20:42:36,670 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-03 20:42:36,670 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-03T15:12:36.670234"}
2025-08-03 20:42:36,670 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:42:36,670 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-03 20:42:36,671 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:42:36,671 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:42:36,671 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-03 20:42:36,671 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-03 20:42:36,671 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:42:36,671 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=e9bbc500-b9f2-4cc2-aa1d-3318ba39e4ec, Document=None, Chunk=0
2025-08-03 20:42:36,671 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 6049 chars
2025-08-03 20:42:40,979 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: e9bbc500-b9f2-4cc2-aa1d-3318ba39e4ec)
2025-08-03 20:42:40,979 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: e9bbc500-b9f2-4cc2-aa1d-3318ba39e4ec) to collections: ['rbi_circular']", "timestamp": "2025-08-03T15:12:40.979133", "document_id": null, "uuid": "e9bbc500-b9f2-4cc2-aa1d-3318ba39e4ec", "collection": "rbi_circular"}
2025-08-03 20:42:40,979 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:42:40,979 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:42:40,979 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:42:53,440 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:42:53,440 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-03 20:42:53,440 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-03 20:42:53,440 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:42:53,441 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=0666b696-7330-56a4-a738-f9f2988a85a4, Document=RBI/2024-25/104 - No.Leg.BC.21/09.07.006/2015-16, Chunk=None
2025-08-03 20:42:53,441 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/104 - No.Leg.BC.21/09.07.006/2015-16 with text: 8000 chars
2025-08-03 20:42:58,172 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/104 - No.Leg.BC.21/09.07.006/2015-16 (ID: 0666b696-7330-56a4-a738-f9f2988a85a4)
2025-08-03 20:42:58,172 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/104 - No.Leg.BC.21/09.07.006/2015-16 (ID: 0666b696-7330-56a4-a738-f9f2988a85a4) to collections: ['rbi_other']", "timestamp": "2025-08-03T15:12:58.172724", "document_id": "RBI/2024-25/104 - No.Leg.BC.21/09.07.006/2015-16", "uuid": "0666b696-7330-56a4-a738-f9f2988a85a4", "collection": "rbi_other"}
2025-08-03 20:42:58,172 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:42:58,172 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:42:58,173 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:43:11,192 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-03 20:43:11,192 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-03 20:43:11,192 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-03 20:43:11,192 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-03 20:43:11,193 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c, Score=0.9869, Document=DBR.No.Leg.BC.78/09.07.005/2017-18
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] Match: ID=cf214b82-7743-4bec-869b-256ad6b1bba9, Score=0.9869, Document=DBR.No.Leg.BC.78/09.07.005/2017-18
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] Match: ID=fe594df3-1d72-4ca9-9efd-0b9d0e666759, Score=0.9863, Document=DBR.No.Leg.BC.78/09.07.005/2015-16
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] Match: ID=303eaf02-51f1-4cd7-bbf7-507a487a2987, Score=0.8894, Document=DBOD No.Leg.BC. 22 /09.07.006/2013-14
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] Match: ID=119d2f97-2961-4cc5-92e7-8cb81a30574c, Score=0.8894, Document=DBOD No.Leg.BC. 22 /09.07.006/2013-14
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DBR.No.Leg.BC.21/09.07.006/2015-16
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '303eaf02-51f1-4cd7-bbf7-507a487a2987', '119d2f97-2961-4cc5-92e7-8cb81a30574c']}
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = cf214b82-7743-4bec-869b-256ad6b1bba9
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fe594df3-1d72-4ca9-9efd-0b9d0e666759
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759']...
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:43:11,270 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '303eaf02-51f1-4cd7-bbf7-507a487a2987', '119d2f97-2961-4cc5-92e7-8cb81a30574c'])] must_not=None, Payload={}
2025-08-03 20:43:11,273 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-03 20:43:11,273 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-03 20:43:11,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:43:11,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:43:11,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '303eaf02-51f1-4cd7-bbf7-507a487a2987', '119d2f97-2961-4cc5-92e7-8cb81a30574c']}
2025-08-03 20:43:11,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:43:11,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c
2025-08-03 20:43:11,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = cf214b82-7743-4bec-869b-256ad6b1bba9
2025-08-03 20:43:11,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fe594df3-1d72-4ca9-9efd-0b9d0e666759
2025-08-03 20:43:11,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759']...
2025-08-03 20:43:11,273 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:43:11,274 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '303eaf02-51f1-4cd7-bbf7-507a487a2987', '119d2f97-2961-4cc5-92e7-8cb81a30574c'])] must_not=None, Payload={}
2025-08-03 20:43:11,293 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 2 matching docs in rbi_master_circular
2025-08-03 20:43:11,311 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8285 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-03 20:43:11,311 - knowledge_base_executor - INFO - ✅ Successfully updated 2 documents in collection: rbi_master_circular
2025-08-03 20:43:11,311 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:43:11,311 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:43:11,311 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '303eaf02-51f1-4cd7-bbf7-507a487a2987', '119d2f97-2961-4cc5-92e7-8cb81a30574c']}
2025-08-03 20:43:11,311 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:43:11,312 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c
2025-08-03 20:43:11,312 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = cf214b82-7743-4bec-869b-256ad6b1bba9
2025-08-03 20:43:11,312 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fe594df3-1d72-4ca9-9efd-0b9d0e666759
2025-08-03 20:43:11,312 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759']...
2025-08-03 20:43:11,312 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:43:11,312 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '303eaf02-51f1-4cd7-bbf7-507a487a2987', '119d2f97-2961-4cc5-92e7-8cb81a30574c'])] must_not=None, Payload={}
2025-08-03 20:43:11,321 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 3 matching docs in rbi_circular
2025-08-03 20:43:11,333 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13009 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-03 20:43:11,333 - knowledge_base_executor - INFO - ✅ Successfully updated 3 documents in collection: rbi_circular
2025-08-03 20:43:11,334 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-03T15:13:11.334111"}
2025-08-03 20:43:11,334 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:43:11,334 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-03 20:43:11,335 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:43:11,335 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-03 20:43:11,335 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-03 20:43:11,335 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-03 20:43:11,335 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-03 20:43:11,335 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-03 20:43:11,351 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-03 20:43:11,351 - knowledge_base_executor - INFO - [QDRANT] Match: ID=fb5bf018-4e33-4adb-bc1d-704f39e21c39, Score=0.8130, Document=DCBR. CO. BPD.BC. No. 16/16.05.000/2015-16
2025-08-03 20:43:11,351 - knowledge_base_executor - INFO - [QDRANT] Match: ID=54c5602e-8d0c-4fae-90e4-9686d4e25ec1, Score=0.7138, Document=RPCD.CO.RRB. No. BC. 35 / 03.05.28(B)/2007-08
2025-08-03 20:43:11,351 - knowledge_base_executor - INFO - [QDRANT] Match: ID=6145d9f4-bcf7-41ef-aa0b-c2557b36b026, Score=0.7087, Document=RPCD.CO.RRB.BC.No.38 /03.05.28(B)/2009-10
2025-08-03 20:43:11,351 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0cb7fefb-5ba7-4b14-99e6-02c6109d43f5, Score=0.7058, Document=RPCD.CO.RRB. No. BC. 61/03.05.28(B)/2007-08
2025-08-03 20:43:11,351 - knowledge_base_executor - INFO - [QDRANT] Match: ID=4e513ba5-f8d8-45cc-b25e-4e2378df9c72, Score=0.7053, Document=RPCD.CO. RRB. No. BC. 32 /03.05.33(C) / 2007-08
2025-08-03 20:43:11,351 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:43:11,352 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-03 20:43:11,352 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DCBR.CO.BPD.(PCB).MC.No.15/12.05.001/2015-16
2025-08-03 20:43:11,352 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72']}
2025-08-03 20:43:11,352 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:43:11,352 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fb5bf018-4e33-4adb-bc1d-704f39e21c39
2025-08-03 20:43:11,352 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 54c5602e-8d0c-4fae-90e4-9686d4e25ec1
2025-08-03 20:43:11,352 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 6145d9f4-bcf7-41ef-aa0b-c2557b36b026
2025-08-03 20:43:11,352 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026']...
2025-08-03 20:43:11,352 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:43:11,352 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72'])] must_not=None, Payload={}
2025-08-03 20:43:11,354 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-03 20:43:11,354 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-03 20:43:11,354 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:43:11,354 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:43:11,354 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72']}
2025-08-03 20:43:11,354 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:43:11,354 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fb5bf018-4e33-4adb-bc1d-704f39e21c39
2025-08-03 20:43:11,354 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 54c5602e-8d0c-4fae-90e4-9686d4e25ec1
2025-08-03 20:43:11,354 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 6145d9f4-bcf7-41ef-aa0b-c2557b36b026
2025-08-03 20:43:11,354 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026']...
2025-08-03 20:43:11,354 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:43:11,354 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72'])] must_not=None, Payload={}
2025-08-03 20:43:11,355 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-03 20:43:11,355 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-03 20:43:11,355 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:43:11,355 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:43:11,355 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72']}
2025-08-03 20:43:11,355 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:43:11,355 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fb5bf018-4e33-4adb-bc1d-704f39e21c39
2025-08-03 20:43:11,356 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 54c5602e-8d0c-4fae-90e4-9686d4e25ec1
2025-08-03 20:43:11,356 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 6145d9f4-bcf7-41ef-aa0b-c2557b36b026
2025-08-03 20:43:11,356 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026']...
2025-08-03 20:43:11,356 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:43:11,356 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72'])] must_not=None, Payload={}
2025-08-03 20:43:11,368 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-03 20:43:11,384 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13010 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-03 20:43:11,384 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-03 20:43:11,384 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-03T15:13:11.384414"}
2025-08-03 20:43:11,384 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:43:11,384 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-03 20:43:11,385 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:43:11,385 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-03 20:43:11,385 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-03 20:43:11,385 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-03 20:43:11,385 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-03 20:43:11,385 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-03 20:43:11,404 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-03 20:43:11,404 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0e976e43-8bd5-4b41-90a0-8ff9fc1b467f, Score=0.7672, Document=RBI/DNBR/2016-17/42 Master Direction DNBR.PD.004/03.10.119/2016-17
2025-08-03 20:43:11,404 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0e3b2121-4fbe-43c5-bae3-0e0addc697f2, Score=0.7672, Document=RBI/DNBR/2016-17/42  Master Direction DNBR.PD.004/03.10.119/2016-17
2025-08-03 20:43:11,404 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0ed22a5e-e8b8-48cd-b508-aa1fa47e6139, Score=0.7672, Document=RBI/DNBR/2016-17/42  Master Direction DNBR.PD.004/03.10.119/2016-17
2025-08-03 20:43:11,404 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0e1f66f9-bfbc-4564-b332-6614e7b1a2f7, Score=0.7672, Document=RBI/DNBR/2016-17/42  Master Direction DNBR.PD.004/03.10.119/2016-17
2025-08-03 20:43:11,404 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0e766a3d-cf8d-4403-835e-d7d96ecb9a82, Score=0.7672, Document=RBI/DNBR/2016-17/42  Master Direction DNBR.PD.004/03.10.119/2016-17
2025-08-03 20:43:11,404 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:43:11,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-03 20:43:11,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DNBR.PD.002/03.10.119/2016-17
2025-08-03 20:43:11,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['0e976e43-8bd5-4b41-90a0-8ff9fc1b467f', '0e3b2121-4fbe-43c5-bae3-0e0addc697f2', '0ed22a5e-e8b8-48cd-b508-aa1fa47e6139', '0e1f66f9-bfbc-4564-b332-6614e7b1a2f7', '0e766a3d-cf8d-4403-835e-d7d96ecb9a82']}
2025-08-03 20:43:11,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:43:11,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0e976e43-8bd5-4b41-90a0-8ff9fc1b467f
2025-08-03 20:43:11,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0e3b2121-4fbe-43c5-bae3-0e0addc697f2
2025-08-03 20:43:11,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0ed22a5e-e8b8-48cd-b508-aa1fa47e6139
2025-08-03 20:43:11,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['0e976e43-8bd5-4b41-90a0-8ff9fc1b467f', '0e3b2121-4fbe-43c5-bae3-0e0addc697f2', '0ed22a5e-e8b8-48cd-b508-aa1fa47e6139']...
2025-08-03 20:43:11,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:43:11,405 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['0e976e43-8bd5-4b41-90a0-8ff9fc1b467f', '0e3b2121-4fbe-43c5-bae3-0e0addc697f2', '0ed22a5e-e8b8-48cd-b508-aa1fa47e6139', '0e1f66f9-bfbc-4564-b332-6614e7b1a2f7', '0e766a3d-cf8d-4403-835e-d7d96ecb9a82'])] must_not=None, Payload={}
2025-08-03 20:43:11,413 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-03 20:43:11,429 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3178 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-03 20:43:11,429 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-03 20:43:11,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:43:11,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:43:11,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['0e976e43-8bd5-4b41-90a0-8ff9fc1b467f', '0e3b2121-4fbe-43c5-bae3-0e0addc697f2', '0ed22a5e-e8b8-48cd-b508-aa1fa47e6139', '0e1f66f9-bfbc-4564-b332-6614e7b1a2f7', '0e766a3d-cf8d-4403-835e-d7d96ecb9a82']}
2025-08-03 20:43:11,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:43:11,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0e976e43-8bd5-4b41-90a0-8ff9fc1b467f
2025-08-03 20:43:11,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0e3b2121-4fbe-43c5-bae3-0e0addc697f2
2025-08-03 20:43:11,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0ed22a5e-e8b8-48cd-b508-aa1fa47e6139
2025-08-03 20:43:11,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['0e976e43-8bd5-4b41-90a0-8ff9fc1b467f', '0e3b2121-4fbe-43c5-bae3-0e0addc697f2', '0ed22a5e-e8b8-48cd-b508-aa1fa47e6139']...
2025-08-03 20:43:11,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:43:11,429 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['0e976e43-8bd5-4b41-90a0-8ff9fc1b467f', '0e3b2121-4fbe-43c5-bae3-0e0addc697f2', '0ed22a5e-e8b8-48cd-b508-aa1fa47e6139', '0e1f66f9-bfbc-4564-b332-6614e7b1a2f7', '0e766a3d-cf8d-4403-835e-d7d96ecb9a82'])] must_not=None, Payload={}
2025-08-03 20:43:11,431 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-03 20:43:11,431 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-03 20:43:11,431 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-03 20:43:11,431 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-03 20:43:11,431 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['0e976e43-8bd5-4b41-90a0-8ff9fc1b467f', '0e3b2121-4fbe-43c5-bae3-0e0addc697f2', '0ed22a5e-e8b8-48cd-b508-aa1fa47e6139', '0e1f66f9-bfbc-4564-b332-6614e7b1a2f7', '0e766a3d-cf8d-4403-835e-d7d96ecb9a82']}
2025-08-03 20:43:11,431 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-03 20:43:11,431 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0e976e43-8bd5-4b41-90a0-8ff9fc1b467f
2025-08-03 20:43:11,431 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0e3b2121-4fbe-43c5-bae3-0e0addc697f2
2025-08-03 20:43:11,431 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 0ed22a5e-e8b8-48cd-b508-aa1fa47e6139
2025-08-03 20:43:11,431 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['0e976e43-8bd5-4b41-90a0-8ff9fc1b467f', '0e3b2121-4fbe-43c5-bae3-0e0addc697f2', '0ed22a5e-e8b8-48cd-b508-aa1fa47e6139']...
2025-08-03 20:43:11,431 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-03 20:43:11,431 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['0e976e43-8bd5-4b41-90a0-8ff9fc1b467f', '0e3b2121-4fbe-43c5-bae3-0e0addc697f2', '0ed22a5e-e8b8-48cd-b508-aa1fa47e6139', '0e1f66f9-bfbc-4564-b332-6614e7b1a2f7', '0e766a3d-cf8d-4403-835e-d7d96ecb9a82'])] must_not=None, Payload={}
2025-08-03 20:43:11,432 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-03 20:43:11,432 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-03 20:43:11,433 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-03T15:13:11.433011"}
2025-08-03 20:43:11,433 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:43:11,433 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-03 20:43:11,433 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:43:11,434 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:43:11,434 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-03 20:43:11,434 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-03 20:43:11,434 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:43:11,434 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=fef9f51d-a64a-4b9a-a5a7-01a362bdcf28, Document=None, Chunk=0
2025-08-03 20:43:11,434 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 7884 chars
2025-08-03 20:43:13,128 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: fef9f51d-a64a-4b9a-a5a7-01a362bdcf28)
2025-08-03 20:43:13,128 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: fef9f51d-a64a-4b9a-a5a7-01a362bdcf28) to collections: ['rbi_circular']", "timestamp": "2025-08-03T15:13:13.128306", "document_id": null, "uuid": "fef9f51d-a64a-4b9a-a5a7-01a362bdcf28", "collection": "rbi_circular"}
2025-08-03 20:43:13,128 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:43:13,128 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:43:13,129 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:43:17,227 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:43:17,227 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-03 20:43:17,228 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-03 20:43:17,228 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:43:17,228 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=9d6092ac-d40d-4d28-93a4-c706f6a4b674, Document=None, Chunk=1
2025-08-03 20:43:17,228 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 259 chars
2025-08-03 20:43:19,102 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 9d6092ac-d40d-4d28-93a4-c706f6a4b674)
2025-08-03 20:43:19,102 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 9d6092ac-d40d-4d28-93a4-c706f6a4b674) to collections: ['rbi_circular']", "timestamp": "2025-08-03T15:13:19.102101", "document_id": null, "uuid": "9d6092ac-d40d-4d28-93a4-c706f6a4b674", "collection": "rbi_circular"}
2025-08-03 20:43:19,102 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:43:19,102 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:43:19,102 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:43:31,957 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:43:31,958 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exim Bank_s GOI-supported Line of Credit _LOC_ for USD 700 million to the Govt. of Mongolia _GO-MNG__ for financing construction of Crude Oil Refinery Plant in Mongolia.pdf
2025-08-03 20:43:31,958 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exim Bank_s GOI-supported Line of Credit _LOC_ for USD 700 million to the Govt. of Mongolia _GO-MNG__ for financing construction of Crude Oil Refinery Plant in Mongolia.pdf
2025-08-03 20:43:31,958 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:43:31,958 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=b4e2074a-37cd-59b3-9dab-7a4ea3a1a95b, Document=RBI/2025-2026/37 - A.P. (DIR Series) Circular No. 05, Chunk=None
2025-08-03 20:43:31,958 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-2026/37 - A.P. (DIR Series) Circular No. 05 with text: 8000 chars
2025-08-03 20:43:35,363 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-2026/37 - A.P. (DIR Series) Circular No. 05 (ID: b4e2074a-37cd-59b3-9dab-7a4ea3a1a95b)
2025-08-03 20:43:35,363 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-2026/37 - A.P. (DIR Series) Circular No. 05 (ID: b4e2074a-37cd-59b3-9dab-7a4ea3a1a95b) to collections: ['rbi_other']", "timestamp": "2025-08-03T15:13:35.363827", "document_id": "RBI/2025-2026/37 - A.P. (DIR Series) Circular No. 05", "uuid": "b4e2074a-37cd-59b3-9dab-7a4ea3a1a95b", "collection": "rbi_other"}
2025-08-03 20:43:35,363 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:43:35,363 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:43:35,364 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:43:45,352 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:43:45,353 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exim Bank_s GOI-supported Line of Credit _LOC_ for USD 700 million to the Govt. of Mongolia _GO-MNG__ for financing construction of Crude Oil Refinery Plant in Mongolia.pdf
2025-08-03 20:43:45,353 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exim Bank_s GOI-supported Line of Credit _LOC_ for USD 700 million to the Govt. of Mongolia _GO-MNG__ for financing construction of Crude Oil Refinery Plant in Mongolia.pdf
2025-08-03 20:43:45,353 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:43:45,353 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=d597f903-24da-57b6-be64-ae4dc3ce1767, Document=RBI/2025-2026/37, Chunk=0
2025-08-03 20:43:45,354 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-2026/37 with text: 8000 chars
2025-08-03 20:43:48,241 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-2026/37 (ID: d597f903-24da-57b6-be64-ae4dc3ce1767)
2025-08-03 20:43:48,241 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-2026/37 (ID: d597f903-24da-57b6-be64-ae4dc3ce1767) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-03T15:13:48.241120", "document_id": "RBI/2025-2026/37", "uuid": "d597f903-24da-57b6-be64-ae4dc3ce1767", "collection": "rbi_master_direction"}
2025-08-03 20:43:48,241 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:43:48,241 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:43:48,241 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:43:48,242 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:43:48,242 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exim Bank_s GOI-supported Line of Credit _LOC_ for USD 700 million to the Govt. of Mongolia _GO-MNG__ for financing construction of Crude Oil Refinery Plant in Mongolia.pdf
2025-08-03 20:43:48,242 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exim Bank_s GOI-supported Line of Credit _LOC_ for USD 700 million to the Govt. of Mongolia _GO-MNG__ for financing construction of Crude Oil Refinery Plant in Mongolia.pdf
2025-08-03 20:43:48,242 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:43:48,242 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=0ea7b3cb-15b0-4584-ba02-bb5c47f95819, Document=None, Chunk=0
2025-08-03 20:43:48,242 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-03 20:43:52,086 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 0ea7b3cb-15b0-4584-ba02-bb5c47f95819)
2025-08-03 20:43:52,086 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 0ea7b3cb-15b0-4584-ba02-bb5c47f95819) to collections: ['rbi_circular']", "timestamp": "2025-08-03T15:13:52.086294", "document_id": null, "uuid": "0ea7b3cb-15b0-4584-ba02-bb5c47f95819", "collection": "rbi_circular"}
2025-08-03 20:43:52,086 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-03 20:43:52,086 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-03 20:43:52,087 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-03 20:44:02,339 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-03 20:44:02,340 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Dispensation of _100 and _200 denomination banknotes through ATMs.pdf
2025-08-03 20:44:02,340 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Dispensation of _100 and _200 denomination banknotes through ATMs.pdf
2025-08-03 20:44:02,341 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-03 20:44:02,341 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=a0845a53-ba38-5484-82d8-94a4d441b8f2, Document=RBI/2025-26/33, Chunk=None
2025-08-03 20:44:02,341 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/33 with text: 8000 chars
