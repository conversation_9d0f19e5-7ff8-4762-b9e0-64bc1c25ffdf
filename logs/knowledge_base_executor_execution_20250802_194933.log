2025-08-02 19:49:33,930 - knowledge_base_executor - <PERSON><PERSON><PERSON> - <PERSON><PERSON> configured to write logs to: logs/knowledge_base_executor_execution_20250802_194933.log
2025-08-02 19:49:33,930 - knowledge_base_executor - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-02 19:49:37,682 - knowledge_base_executor - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-02 19:49:55,532 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:49:55,532 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:55,532 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:55,533 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:49:55,533 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_circular'], PID=67b8e32f-0047-537b-8852-ae79f77fc0ad, Document=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25, Chunk=None
2025-08-02 19:49:55,534 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 with text: 8000 chars
2025-08-02 19:49:58,587 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 (ID: 67b8e32f-0047-537b-8852-ae79f77fc0ad)
2025-08-02 19:49:58,587 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 (ID: 67b8e32f-0047-537b-8852-ae79f77fc0ad) to collections: ['rbi_master_circular']", "timestamp": "2025-08-02T14:19:58.587466", "document_id": "RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25", "uuid": "67b8e32f-0047-537b-8852-ae79f77fc0ad", "collection": "rbi_master_circular"}
2025-08-02 19:49:58,587 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:49:58,587 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:49:58,588 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:10,599 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:50:10,599 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:10,599 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:10,599 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:50:10,599 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:50:10,735 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:50:10,735 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9741a628-999e-4b3f-aa8d-da299fe6f6c1, Score=0.8026, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-02 19:50:10,735 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3d495fa6-6797-461c-bf81-7de0eb59a24a, Score=0.8026, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-02 19:50:10,735 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2105f02f-10ac-520c-82c1-d91e04509d6d, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:50:10,735 - knowledge_base_executor - INFO - [QDRANT] Match: ID=d2b23822-c5a2-5527-8507-5b3d29d5794c, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:50:10,735 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d04a677-f401-5734-88ac-14846a6cf5b3, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:50:10,735 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:10,736 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:50:10,737 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:50:10,738 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:50:10,738 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:50:10,739 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:50:10,739 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:50:10,739 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:10,739 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:20:10.739150"}
2025-08-02 19:50:10,739 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:50:10,739 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:50:10,740 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:10,741 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:50:10,741 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:10,741 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:10,741 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:50:10,741 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=7d697936-3148-4a15-9f27-2c8304072df9, Document=None, Chunk=0
2025-08-02 19:50:10,741 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 2513 chars
2025-08-02 19:50:12,872 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 7d697936-3148-4a15-9f27-2c8304072df9)
2025-08-02 19:50:12,872 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 7d697936-3148-4a15-9f27-2c8304072df9) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:20:12.872628", "document_id": null, "uuid": "7d697936-3148-4a15-9f27-2c8304072df9", "collection": "rbi_circular"}
2025-08-02 19:50:12,872 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:50:12,872 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:50:12,874 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:26,369 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:50:26,369 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:26,369 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:26,370 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:50:26,370 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:50:26,566 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] Match: ID=521d0371-918a-4608-a972-d5d632952cc8, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] Match: ID=7940f618-ba47-45d9-aa0d-98ebba0fb222, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ccd83ffc-2347-403f-92cc-2aeb36cd3027, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] Match: ID=c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs' → 'MasterCircular-Guarantees,Co-acceptances&LettersofCredit-UCBs'
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: MasterCircular-Guarantees,Co-acceptances&LettersofCredit-UCBs
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:50:26,568 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:50:26,568 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:50:26,569 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:50:26,569 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:26,569 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:20:26.569090"}
2025-08-02 19:50:26,569 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:50:26,569 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:50:26,572 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:26,573 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:50:26,573 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:26,573 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:26,573 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:50:26,573 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=e05cd530-c2a1-437f-9529-47b563692c5a, Document=None, Chunk=1
2025-08-02 19:50:26,573 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-02 19:50:29,081 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: e05cd530-c2a1-437f-9529-47b563692c5a)
2025-08-02 19:50:29,081 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: e05cd530-c2a1-437f-9529-47b563692c5a) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:20:29.081721", "document_id": null, "uuid": "e05cd530-c2a1-437f-9529-47b563692c5a", "collection": "rbi_circular"}
2025-08-02 19:50:29,081 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:50:29,081 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:50:29,082 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:50,025 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:50:50,025 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,025 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,025 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:50:50,025 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:50:50,304 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:50:50,304 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5dd89363-59cc-4134-ada4-eeda877a51b5, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,304 - knowledge_base_executor - INFO - [QDRANT] Match: ID=729eb4c6-a6c3-4aed-9a25-aa597db91127, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,304 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9affc01a-813a-4ab4-afdc-e864c70dd44f, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,304 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ac5ffabf-b12b-4fc4-93be-9684c9047105, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,304 - knowledge_base_executor - INFO - [QDRANT] Match: ID=96f706da-38a1-4bc3-bd09-648ab7d128db, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,304 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.Plan.(PCB)49/09.27.00/96-97
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:50:50,305 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:50:50,305 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:50:50,305 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:20:50.305911"}
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:50:50,306 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:50:50,311 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:50,311 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:50:50,311 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,311 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,311 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:50:50,311 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5dd89363-59cc-4134-ada4-eeda877a51b5, Score=0.7338, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] Match: ID=729eb4c6-a6c3-4aed-9a25-aa597db91127, Score=0.7338, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9affc01a-813a-4ab4-afdc-e864c70dd44f, Score=0.7338, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ac5ffabf-b12b-4fc4-93be-9684c9047105, Score=0.7338, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] Match: ID=96f706da-38a1-4bc3-bd09-648ab7d128db, Score=0.7338, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.Plan.42/09.27.00-93/94
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:50:50,361 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:50:50,361 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:50:50,361 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:20:50.361576"}
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:50:50,362 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:50,362 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:50:50,362 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,362 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,362 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:50:50,362 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9a0db77f-d109-46ff-9dd5-42e6866482c2, Score=0.5587, Document=DBOD.PSBD.BC.88/16.13.100/2005-06
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] Match: ID=529482b7-61a4-4dbc-85f6-082d3171e0ea, Score=0.5521, Document=Ref.No.UBD.DS. 2 /13.02.00/2002-03
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b7aff217-917a-46f2-93ba-ecbc1a1eb565, Score=0.5448, Document=BP.BC.11/21.01.040/99-00
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] Match: ID=998c8368-4404-41db-a8ac-d02d9c525c94, Score=0.5448, Document=BP.BC.11/21.01.040/99-00
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b4358508-03e0-4c1e-b893-bcede5fbbb79, Score=0.5448, Document=BP.BC.11/21.01.040/99-00
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.POT.1/UB.58-92/3
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79']}
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9a0db77f-d109-46ff-9dd5-42e6866482c2
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 529482b7-61a4-4dbc-85f6-082d3171e0ea
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b7aff217-917a-46f2-93ba-ecbc1a1eb565
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565']...
2025-08-02 19:50:50,405 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79']}
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9a0db77f-d109-46ff-9dd5-42e6866482c2
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 529482b7-61a4-4dbc-85f6-082d3171e0ea
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b7aff217-917a-46f2-93ba-ecbc1a1eb565
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565']...
2025-08-02 19:50:50,406 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79']}
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9a0db77f-d109-46ff-9dd5-42e6866482c2
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 529482b7-61a4-4dbc-85f6-082d3171e0ea
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b7aff217-917a-46f2-93ba-ecbc1a1eb565
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565']...
2025-08-02 19:50:50,406 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:20:50.406295"}
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:50,407 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:50:50,407 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,407 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,407 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:50:50,407 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:50:50,454 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:50:50,454 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3d5b542d-4758-4b8b-9adf-56b5ae78f253, Score=0.6118, Document=RPCD.No.RRB.BC.33/03.05.33(E)/2005-06
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ac35390c-19c4-4447-b048-bb917acfa27f, Score=0.6118, Document=RPCD.No.RRB.BC.33/03.05.33(E)/2005-06
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] Match: ID=bfa6e812-0474-4eba-b9d1-25382c83aa14, Score=0.6081, Document=RPCD.No.RRB.BC.76/03.05.33(C)/2005-06
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] Match: ID=c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae, Score=0.6081, Document=RPCD.No.RRB.BC.76/03.05.33(C)/2005-06
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] Match: ID=05e1db56-5a24-45dd-8de5-570e08a4eea9, Score=0.6046, Document=RPCD.No.RRB.BC.76/03.05.34/96-97
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.I&L/PCB/9/12.05.00/95-96
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9']}
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d5b542d-4758-4b8b-9adf-56b5ae78f253
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ac35390c-19c4-4447-b048-bb917acfa27f
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = bfa6e812-0474-4eba-b9d1-25382c83aa14
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14']...
2025-08-02 19:50:50,455 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9']}
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d5b542d-4758-4b8b-9adf-56b5ae78f253
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ac35390c-19c4-4447-b048-bb917acfa27f
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = bfa6e812-0474-4eba-b9d1-25382c83aa14
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14']...
2025-08-02 19:50:50,455 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9']}
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d5b542d-4758-4b8b-9adf-56b5ae78f253
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ac35390c-19c4-4447-b048-bb917acfa27f
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = bfa6e812-0474-4eba-b9d1-25382c83aa14
2025-08-02 19:50:50,456 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14']...
2025-08-02 19:50:50,456 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,456 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:20:50.456065"}
2025-08-02 19:50:50,456 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:50:50,456 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:50:50,456 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:50,457 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:50:50,457 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,457 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,457 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:50:50,457 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=34a8f5ea-beaf-474b-8e3b-073bf3397f33, Document=None, Chunk=2
2025-08-02 19:50:50,457 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-02 19:50:52,950 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 34a8f5ea-beaf-474b-8e3b-073bf3397f33)
2025-08-02 19:50:52,950 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 34a8f5ea-beaf-474b-8e3b-073bf3397f33) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:20:52.950596", "document_id": null, "uuid": "34a8f5ea-beaf-474b-8e3b-073bf3397f33", "collection": "rbi_circular"}
2025-08-02 19:50:52,950 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:50:52,950 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:50:52,951 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
