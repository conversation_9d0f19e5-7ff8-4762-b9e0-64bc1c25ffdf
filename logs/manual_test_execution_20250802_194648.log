2025-08-02 19:46:48,553 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250802_194648.log
2025-08-02 19:46:48,553 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250802_194648.log
2025-08-02 19:46:48,553 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-02 19:46:48,553 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-02 19:46:52,145 - utils.config - INFO - Configuration loaded successfully
2025-08-02 19:46:52,785 - prompts.notification_categorizer - INFO - Processed 0 chunks from content
2025-08-02 19:46:52,785 - prompts.notification_categorizer - INFO - Chunks: []
2025-08-02 19:46:52,785 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250802_194652.log
2025-08-02 19:46:52,785 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250802_194652.log
2025-08-02 19:46:52,785 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-02 19:46:52,785 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-02 19:46:52,798 - qdrant_utils - INFO - Configured Hugging Face HTTP backend to bypass SSL verification
2025-08-02 19:46:52,800 - config - INFO - Configuration loaded successfully
2025-08-02 19:46:54,171 - qdrant_utils - INFO - Loaded SPLADE model: naver/efficient-splade-VI-BT-large-doc
2025-08-02 19:46:54,171 - qdrant_utils - INFO - Initializing QdrantManager with URL: http://localhost:6333
2025-08-02 19:46:54,188 - httpx - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-02 19:46:54,194 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections "HTTP/1.1 200 OK"
2025-08-02 19:46:54,195 - qdrant_utils - INFO - ✅ QdrantClient initialized successfully
2025-08-02 19:46:55,099 - qdrant_utils - INFO - ✅ Sparse embedder initialized with model: Qdrant/bm25
2025-08-02 19:46:55,099 - qdrant_utils - INFO - 🔍 FastEmbed creates sparse vector type: <class 'langchain_qdrant.sparse_embeddings.SparseVector'>
2025-08-02 19:46:55,100 - qdrant_utils - INFO - 🔍 FastEmbed sparse vector indices count: 1
2025-08-02 19:46:55,115 - manual_test - INFO - 🚀 Starting Real Pipeline Test with Enhanced KB Storage...
2025-08-02 19:46:55,115 - manual_test - INFO - 🚀 Starting Real Pipeline Test with Enhanced KB Storage...
2025-08-02 19:46:55,115 - manual_test - INFO - 📄 Using notifications file: rbi_notifications.json
2025-08-02 19:46:55,115 - manual_test - INFO - 📄 Using notifications file: rbi_notifications.json
2025-08-02 19:46:55,115 - manual_test - INFO - Initialized key rotator with 2 keys
2025-08-02 19:46:55,115 - manual_test - INFO - Initialized key rotator with 2 keys
2025-08-02 19:46:55,115 - manual_test - INFO - ✅ Environment setup complete
2025-08-02 19:46:55,115 - manual_test - INFO - ✅ Environment setup complete
2025-08-02 19:46:55,116 - manual_test - INFO - ✅ Real notification processor initialized
2025-08-02 19:46:55,116 - manual_test - INFO - ✅ Real notification processor initialized
2025-08-02 19:46:55,117 - knowledge_base_executor - INFO - Logger configured to write logs to: logs/knowledge_base_executor_execution_20250802_194655.log
2025-08-02 19:46:55,117 - knowledge_base_executor - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-02 19:46:55,130 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-08-02 19:46:55,131 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-02 19:46:58,832 - httpx - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-02 19:46:58,832 - utils.metadata_vector_utils - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-02 19:46:58,832 - knowledge_base_executor - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-02 19:46:58,833 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-08-02 19:46:58,833 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-02 19:47:02,230 - httpx - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-02 19:47:02,231 - utils.metadata_vector_utils - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-02 19:47:02,231 - manual_test - INFO - ✅ Metadata vector search initialized
2025-08-02 19:47:02,231 - manual_test - INFO - ✅ Metadata vector search initialized
2025-08-02 19:47:02,231 - manual_test - INFO - 🧠 Selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-02 19:47:02,231 - manual_test - INFO - 🧠 Selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-02 19:47:02,231 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-08-02 19:47:02,231 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-02 19:47:05,632 - httpx - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-02 19:47:05,632 - manual_test - INFO - ✅ Direct metadata search components initialized
2025-08-02 19:47:05,632 - manual_test - INFO - ✅ Direct metadata search components initialized
2025-08-02 19:47:05,632 - manual_test - INFO - 🚀 Starting ENHANCED pipeline test with comprehensive UUID tracking...
2025-08-02 19:47:05,632 - manual_test - INFO - 🚀 Starting ENHANCED pipeline test with comprehensive UUID tracking...
2025-08-02 19:47:05,632 - manual_test - INFO - 🚀 Starting ENHANCED pipeline test with up to 50 notifications...
2025-08-02 19:47:05,632 - manual_test - INFO - 🚀 Starting ENHANCED pipeline test with up to 50 notifications...
2025-08-02 19:47:05,633 - manual_test - INFO - 📂 Loaded 1 notifications from rbi_notifications.json
2025-08-02 19:47:05,633 - manual_test - INFO - 📂 Loaded 1 notifications from rbi_notifications.json
2025-08-02 19:47:05,633 - manual_test - INFO - 
2025-08-02 19:47:05,633 - manual_test - INFO - 
2025-08-02 19:47:05,633 - manual_test - INFO - 🔄 ═══════════════════════════════════════════════════════════════
2025-08-02 19:47:05,633 - manual_test - INFO - 🔄 ═══════════════════════════════════════════════════════════════
2025-08-02 19:47:05,633 - manual_test - INFO - 🔄 PROCESSING NOTIFICATION 1/1 (100.0%)
2025-08-02 19:47:05,633 - manual_test - INFO - 🔄 PROCESSING NOTIFICATION 1/1 (100.0%)
2025-08-02 19:47:05,633 - manual_test - INFO - 🔄 Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Cr...
2025-08-02 19:47:05,633 - manual_test - INFO - 🔄 Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Cr...
2025-08-02 19:47:05,633 - manual_test - INFO - 🔄 Date: 
2025-08-02 19:47:05,633 - manual_test - INFO - 🔄 Date: 
2025-08-02 19:47:05,633 - manual_test - INFO - 🔄 ═══════════════════════════════════════════════════════════════
2025-08-02 19:47:05,633 - manual_test - INFO - 🔄 ═══════════════════════════════════════════════════════════════
2025-08-02 19:47:05,633 - manual_test - INFO - 
2025-08-02 19:47:05,633 - manual_test - INFO - 
2025-08-02 19:47:05,635 - manual_test - WARNING - ⚠️ Primary code patterns failed, trying fallback extraction
2025-08-02 19:47:05,635 - manual_test - WARNING - ⚠️ Primary code patterns failed, trying fallback extraction
2025-08-02 19:47:05,635 - manual_test - WARNING - ⚠️ No codes extracted from text
2025-08-02 19:47:05,635 - manual_test - WARNING - ⚠️ No codes extracted from text
2025-08-02 19:47:05,635 - manual_test - INFO - 🚀 ENHANCED Processing notification: Master Circular - Guarantees  Co-Acceptances   Let...
2025-08-02 19:47:05,635 - manual_test - INFO - 🚀 ENHANCED Processing notification: Master Circular - Guarantees  Co-Acceptances   Let...
2025-08-02 19:47:05,636 - manual_test - INFO - 📊 Initial notification codes from title: {'short_code': '', 'long_code': '', 'full_code': '', 'year': '', 'all_codes': []}
2025-08-02 19:47:05,636 - manual_test - INFO - 📊 Initial notification codes from title: {'short_code': '', 'long_code': '', 'full_code': '', 'year': '', 'all_codes': []}
2025-08-02 19:47:05,636 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:05,636 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:05,732 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:05,732 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:05,923 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:05,923 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:05,930 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:47:05,930 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:47:05,938 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:05,938 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:05,939 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['DoR.STR.REC.3/09.27.000', 'UBD.PCB.No.59/13.05.000/2008', 'BSD.No.IP.30/12.05.05/2002', '.No.59/13.05.000/2008-09', 'UBD.PCB.No.36/13.05.000/2008-09', 'UBD.PCB.No.59/13.05.000/2008-09', '.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05', 'UBD.No.Plan.42/09.27.00', 'BSD.No.IP.30/12.05.05/2002-03', 'A.P. (DIR Series) Circular No. 18', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000/2024-25', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.36/13.05.000/2008', '.IP.30/12.05.05/2002-03', 'BPD.Cir.No.29/13.05.000', 'UBD.PCB.No.59/13.05.000', '.REC.3/09.27.000/2024-25']}
2025-08-02 19:47:05,939 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['DoR.STR.REC.3/09.27.000', 'UBD.PCB.No.59/13.05.000/2008', 'BSD.No.IP.30/12.05.05/2002', '.No.59/13.05.000/2008-09', 'UBD.PCB.No.36/13.05.000/2008-09', 'UBD.PCB.No.59/13.05.000/2008-09', '.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05', 'UBD.No.Plan.42/09.27.00', 'BSD.No.IP.30/12.05.05/2002-03', 'A.P. (DIR Series) Circular No. 18', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000/2024-25', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.36/13.05.000/2008', '.IP.30/12.05.05/2002-03', 'BPD.Cir.No.29/13.05.000', 'UBD.PCB.No.59/13.05.000', '.REC.3/09.27.000/2024-25']}
2025-08-02 19:47:05,939 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:47:05,939 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:47:05,939 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:47:05,939 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:47:05,939 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:47:05,939 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:47:05,939 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:05,939 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:05,939 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:47:05,939 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:47:05,939 - manual_test - INFO - ✅ Extracted 49457 chars from local PDF
2025-08-02 19:47:05,939 - manual_test - INFO - ✅ Extracted 49457 chars from local PDF
2025-08-02 19:47:05,939 - manual_test - INFO - 📄 Extracting notification codes from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:05,939 - manual_test - INFO - 📄 Extracting notification codes from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:05,939 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:05,939 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:06,010 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:06,010 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:06,197 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:06,197 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:06,204 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:47:06,204 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:47:06,213 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:06,213 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:06,213 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['DoR.STR.REC.3/09.27.000', 'UBD.PCB.No.59/13.05.000/2008', 'BSD.No.IP.30/12.05.05/2002', '.No.59/13.05.000/2008-09', 'UBD.PCB.No.36/13.05.000/2008-09', 'UBD.PCB.No.59/13.05.000/2008-09', '.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05', 'UBD.No.Plan.42/09.27.00', 'BSD.No.IP.30/12.05.05/2002-03', 'A.P. (DIR Series) Circular No. 18', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000/2024-25', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.36/13.05.000/2008', '.IP.30/12.05.05/2002-03', 'BPD.Cir.No.29/13.05.000', 'UBD.PCB.No.59/13.05.000', '.REC.3/09.27.000/2024-25']}
2025-08-02 19:47:06,213 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['DoR.STR.REC.3/09.27.000', 'UBD.PCB.No.59/13.05.000/2008', 'BSD.No.IP.30/12.05.05/2002', '.No.59/13.05.000/2008-09', 'UBD.PCB.No.36/13.05.000/2008-09', 'UBD.PCB.No.59/13.05.000/2008-09', '.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05', 'UBD.No.Plan.42/09.27.00', 'BSD.No.IP.30/12.05.05/2002-03', 'A.P. (DIR Series) Circular No. 18', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000/2024-25', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.36/13.05.000/2008', '.IP.30/12.05.05/2002-03', 'BPD.Cir.No.29/13.05.000', 'UBD.PCB.No.59/13.05.000', '.REC.3/09.27.000/2024-25']}
2025-08-02 19:47:06,213 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:47:06,213 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:47:06,213 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:47:06,213 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:47:06,213 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:47:06,213 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:47:06,214 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:06,214 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:06,214 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:47:06,214 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:47:06,214 - manual_test - INFO - 📋 Enhanced codes after PDF extraction: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['DoR.STR.REC.3/09.27.000', 'UBD.PCB.No.59/13.05.000/2008', 'BSD.No.IP.30/12.05.05/2002', '.No.59/13.05.000/2008-09', 'UBD.PCB.No.36/13.05.000/2008-09', 'UBD.PCB.No.59/13.05.000/2008-09', '.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05', 'UBD.No.Plan.42/09.27.00', 'BSD.No.IP.30/12.05.05/2002-03', 'A.P. (DIR Series) Circular No. 18', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000/2024-25', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.36/13.05.000/2008', '.IP.30/12.05.05/2002-03', 'BPD.Cir.No.29/13.05.000', 'UBD.PCB.No.59/13.05.000', '.REC.3/09.27.000/2024-25'], 'watermark_status': 'ACTIVE', 'is_active': True}
2025-08-02 19:47:06,214 - manual_test - INFO - 📋 Enhanced codes after PDF extraction: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['DoR.STR.REC.3/09.27.000', 'UBD.PCB.No.59/13.05.000/2008', 'BSD.No.IP.30/12.05.05/2002', '.No.59/13.05.000/2008-09', 'UBD.PCB.No.36/13.05.000/2008-09', 'UBD.PCB.No.59/13.05.000/2008-09', '.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05', 'UBD.No.Plan.42/09.27.00', 'BSD.No.IP.30/12.05.05/2002-03', 'A.P. (DIR Series) Circular No. 18', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000/2024-25', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.36/13.05.000/2008', '.IP.30/12.05.05/2002-03', 'BPD.Cir.No.29/13.05.000', 'UBD.PCB.No.59/13.05.000', '.REC.3/09.27.000/2024-25'], 'watermark_status': 'ACTIVE', 'is_active': True}
2025-08-02 19:47:06,214 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:47:06,214 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:47:06,214 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:06,214 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:06,214 - manual_test - INFO -    📋 Full code: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:06,214 - manual_test - INFO -    📋 Full code: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:06,214 - manual_test - INFO -    📅 Year: 2025
2025-08-02 19:47:06,214 - manual_test - INFO -    📅 Year: 2025
2025-08-02 19:47:06,214 - manual_test - INFO -    🏷️ Watermark status: ACTIVE
2025-08-02 19:47:06,214 - manual_test - INFO -    🏷️ Watermark status: ACTIVE
2025-08-02 19:47:06,214 - manual_test - INFO - --- ENHANCED Stage 1: Notification Analysis ---
2025-08-02 19:47:06,214 - manual_test - INFO - --- ENHANCED Stage 1: Notification Analysis ---
2025-08-02 19:47:06,214 - manual_test - INFO - 🔍 Starting notification analysis for: Master Circular - Guarantees  Co-Acceptances   Let...
2025-08-02 19:47:06,214 - manual_test - INFO - 🔍 Starting notification analysis for: Master Circular - Guarantees  Co-Acceptances   Let...
2025-08-02 19:47:06,214 - manual_test - INFO - 📏 Notification length: 49457 characters
2025-08-02 19:47:06,214 - manual_test - INFO - 📏 Notification length: 49457 characters
2025-08-02 19:47:06,214 - manual_test - INFO - 📝 Prompt created, making LLM call...
2025-08-02 19:47:06,214 - manual_test - INFO - 📝 Prompt created, making LLM call...
2025-08-02 19:47:06,217 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-02 19:47:06,217 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-02 19:47:10,958 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:47:10,984 - manual_test - INFO - ✅ Notification categorized as: Review (confidence: high)
2025-08-02 19:47:10,984 - manual_test - INFO - ✅ Notification categorized as: Review (confidence: high)
2025-08-02 19:47:10,988 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4.1 for regulatory_interpretation - Selected critical tier for regulatory_interpretation (context: 7218, priority: quality)
2025-08-02 19:47:10,988 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4.1 for regulatory_interpretation - Selected critical tier for regulatory_interpretation (context: 7218, priority: quality)
2025-08-02 19:47:20,843 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:47:20,861 - manual_test - INFO - 📋 KB Decision: update_existing (store: True, remove: True)
2025-08-02 19:47:20,861 - manual_test - INFO - 📋 KB Decision: update_existing (store: True, remove: True)
2025-08-02 19:47:20,861 - manual_test - INFO - --- ENHANCED Stage 2: Notification-level KB Actions ---
2025-08-02 19:47:20,861 - manual_test - INFO - --- ENHANCED Stage 2: Notification-level KB Actions ---
2025-08-02 19:47:20,861 - manual_test - INFO - notification_codes being passed to KB storage: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['DoR.STR.REC.3/09.27.000', 'UBD.PCB.No.59/13.05.000/2008', 'BSD.No.IP.30/12.05.05/2002', '.No.59/13.05.000/2008-09', 'UBD.PCB.No.36/13.05.000/2008-09', 'UBD.PCB.No.59/13.05.000/2008-09', '.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05', 'UBD.No.Plan.42/09.27.00', 'BSD.No.IP.30/12.05.05/2002-03', 'A.P. (DIR Series) Circular No. 18', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000/2024-25', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.36/13.05.000/2008', '.IP.30/12.05.05/2002-03', 'BPD.Cir.No.29/13.05.000', 'UBD.PCB.No.59/13.05.000', '.REC.3/09.27.000/2024-25'], 'watermark_status': 'ACTIVE', 'is_active': True}
2025-08-02 19:47:20,861 - manual_test - INFO - notification_codes being passed to KB storage: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['DoR.STR.REC.3/09.27.000', 'UBD.PCB.No.59/13.05.000/2008', 'BSD.No.IP.30/12.05.05/2002', '.No.59/13.05.000/2008-09', 'UBD.PCB.No.36/13.05.000/2008-09', 'UBD.PCB.No.59/13.05.000/2008-09', '.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05', 'UBD.No.Plan.42/09.27.00', 'BSD.No.IP.30/12.05.05/2002-03', 'A.P. (DIR Series) Circular No. 18', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000/2024-25', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.36/13.05.000/2008', '.IP.30/12.05.05/2002-03', 'BPD.Cir.No.29/13.05.000', 'UBD.PCB.No.59/13.05.000', '.REC.3/09.27.000/2024-25'], 'watermark_status': 'ACTIVE', 'is_active': True}
2025-08-02 19:47:20,861 - manual_test - INFO - 🔍 Detecting document type for flowchart-based processing...
2025-08-02 19:47:20,861 - manual_test - INFO - 🔍 Detecting document type for flowchart-based processing...
2025-08-02 19:47:20,862 - manual_test - INFO - 🔍 Detecting document type from title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs...
2025-08-02 19:47:20,862 - manual_test - INFO - 🔍 Detecting document type from title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs...
2025-08-02 19:47:20,862 - manual_test - INFO - 📋 Target collection from KB decision: rbi_master_circular
2025-08-02 19:47:20,862 - manual_test - INFO - 📋 Target collection from KB decision: rbi_master_circular
2025-08-02 19:47:20,862 - manual_test - INFO - ✅ Detected: Master Circular
2025-08-02 19:47:20,862 - manual_test - INFO - ✅ Detected: Master Circular
2025-08-02 19:47:20,862 - manual_test - INFO - 📋 Document Type Detected: master_circular
2025-08-02 19:47:20,862 - manual_test - INFO - 📋 Document Type Detected: master_circular
2025-08-02 19:47:20,862 - manual_test - INFO - 🎯 Generating flowchart-based actions for document type: master_circular
2025-08-02 19:47:20,862 - manual_test - INFO - 🎯 Generating flowchart-based actions for document type: master_circular
2025-08-02 19:47:20,862 - manual_test - INFO - 📘 Processing Master Circular actions...
2025-08-02 19:47:20,862 - manual_test - INFO - 📘 Processing Master Circular actions...
2025-08-02 19:47:21,282 - utils.s3_utils - INFO - Successfully uploaded file to https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:47:21,285 - manual_test - INFO - ✅ Uploaded notification PDF to S3: https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:47:21,285 - manual_test - INFO - ✅ Uploaded notification PDF to S3: https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:47:21,285 - manual_test - INFO - ✅ Added: ADD_DOCUMENT action for Master Circular
2025-08-02 19:47:21,285 - manual_test - INFO - ✅ Added: ADD_DOCUMENT action for Master Circular
2025-08-02 19:47:21,285 - manual_test - INFO - 🎯 Generated 1 KB actions based on flowchart:
2025-08-02 19:47:21,285 - manual_test - INFO - 🎯 Generated 1 KB actions based on flowchart:
2025-08-02 19:47:21,286 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT in rbi_master_circular
2025-08-02 19:47:21,286 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT in rbi_master_circular
2025-08-02 19:47:21,286 - manual_test - INFO -       🎯 Target: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:21,286 - manual_test - INFO -       🎯 Target: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:21,286 - manual_test - INFO -       🤔 Reasoning: New Master Circular publication - follows flowchart logic
2025-08-02 19:47:21,286 - manual_test - INFO -       🤔 Reasoning: New Master Circular publication - follows flowchart logic
2025-08-02 19:47:21,286 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-02 19:47:21,286 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-02 19:47:21,753 - utils.s3_utils - INFO - Successfully uploaded file to https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:47:21,757 - manual_test - INFO - ✅ Uploaded notification PDF to S3: https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:47:21,757 - manual_test - INFO - ✅ Uploaded notification PDF to S3: https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:47:21,757 - manual_test - INFO - ✅ Executing 1 flowchart-based KB actions
2025-08-02 19:47:21,757 - manual_test - INFO - ✅ Executing 1 flowchart-based KB actions
2025-08-02 19:47:21,758 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT for collection rbi_master_circular
2025-08-02 19:47:21,758 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT for collection rbi_master_circular
2025-08-02 19:47:21,758 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:47:21,758 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:21,758 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:21,759 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:47:21,759 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_circular'], PID=a7e5e49d-7c9a-519c-9046-6a3cb1da0b9f, Document=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25, Chunk=None
2025-08-02 19:47:21,759 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 with text: 8000 chars
2025-08-02 19:47:21,768 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_master_circular/exists "HTTP/1.1 200 OK"
2025-08-02 19:47:21,778 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_master_circular "HTTP/1.1 200 OK"
2025-08-02 19:47:21,781 - qdrant_utils - INFO - 🔍 Collection rbi_master_circular vectors: size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:47:21,781 - qdrant_utils - INFO - 🔍 Collection rbi_master_circular sparse vectors: {'fast-sparse-bm25': SparseVectorParams(index=SparseIndexParams(full_scan_threshold=None, on_disk=None, datatype=None), modifier=<Modifier.IDF: 'idf'>)}
2025-08-02 19:47:21,781 - qdrant_utils - WARNING - Collection rbi_master_circular exists but uses hybrid/single format. Will work with existing.
2025-08-02 19:47:23,024 - openai_utils - INFO - Embedding 1 documents
2025-08-02 19:47:24,241 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-02 19:47:24,608 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_master_circular "HTTP/1.1 200 OK"
2025-08-02 19:47:24,608 - qdrant_utils - INFO - Collection info for rbi_master_circular: vectors=size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:47:24,608 - qdrant_utils - INFO - Collection rbi_master_circular uses hybrid format (single dense + sparse)
2025-08-02 19:47:24,608 - qdrant_utils - INFO - Collection format: hybrid
2025-08-02 19:47:24,608 - qdrant_utils - INFO - ✅ Added dense vector with 3072 dimensions
2025-08-02 19:47:24,611 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_master_circular "HTTP/1.1 200 OK"
2025-08-02 19:47:24,612 - qdrant_utils - INFO - ✅ Added sparse vector 'fast-sparse-bm25' with 410 indices
2025-08-02 19:47:24,612 - qdrant_utils - WARNING - ⚠️ Skipping sparse vector 'splade' - not configured in collection schema
2025-08-02 19:47:24,612 - qdrant_utils - INFO - 🔍 Final vector keys for point a7e5e49d-7c9a-519c-9046-6a3cb1da0b9f: ['', 'fast-sparse-bm25']
2025-08-02 19:47:24,632 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_master_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:47:24,633 - qdrant_utils - INFO - ✅ Using hybrid format for point a7e5e49d-7c9a-519c-9046-6a3cb1da0b9f
2025-08-02 19:47:24,633 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 (ID: a7e5e49d-7c9a-519c-9046-6a3cb1da0b9f)
2025-08-02 19:47:24,633 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 (ID: a7e5e49d-7c9a-519c-9046-6a3cb1da0b9f) to collections: ['rbi_master_circular']", "timestamp": "2025-08-02T14:17:24.633129", "document_id": "RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25", "uuid": "a7e5e49d-7c9a-519c-9046-6a3cb1da0b9f", "collection": "rbi_master_circular"}
2025-08-02 19:47:24,633 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:47:24,633 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:47:24,634 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:47:24,634 - manual_test - INFO - ✅ Flowchart-based KB Results summary:
2025-08-02 19:47:24,634 - manual_test - INFO - ✅ Flowchart-based KB Results summary:
2025-08-02 19:47:24,634 - manual_test - INFO -   📋 Result 1: ADD_DOCUMENT - SUCCESS
2025-08-02 19:47:24,634 - manual_test - INFO -   📋 Result 1: ADD_DOCUMENT - SUCCESS
2025-08-02 19:47:24,634 - manual_test - INFO -       📦 Collection: rbi_master_circular
2025-08-02 19:47:24,634 - manual_test - INFO -       📦 Collection: rbi_master_circular
2025-08-02 19:47:24,634 - manual_test - INFO -       🆔 UUID: a7e5e49d-7c9a-519c-9046-6a3cb1da0b9f
2025-08-02 19:47:24,634 - manual_test - INFO -       🆔 UUID: a7e5e49d-7c9a-519c-9046-6a3cb1da0b9f
2025-08-02 19:47:24,634 - manual_test - INFO -       🤔 Reasoning: New Master Circular publication - follows flowchart logic
2025-08-02 19:47:24,634 - manual_test - INFO -       🤔 Reasoning: New Master Circular publication - follows flowchart logic
2025-08-02 19:47:24,634 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-02 19:47:24,634 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-02 19:47:24,634 - manual_test - INFO - 📊 Added 1 documents with UUIDs: ['a7e5e49d-7c9a-519c-9046-6a3cb1da0b9f']
2025-08-02 19:47:24,634 - manual_test - INFO - 📊 Added 1 documents with UUIDs: ['a7e5e49d-7c9a-519c-9046-6a3cb1da0b9f']
2025-08-02 19:47:24,634 - manual_test - INFO - 📊 Adding notification to metadata_vectors collection for enhanced search
2025-08-02 19:47:24,634 - manual_test - INFO - 📊 Adding notification to metadata_vectors collection for enhanced search
2025-08-02 19:47:24,636 - utils.qdrant_utils - INFO - Configured Hugging Face HTTP backend to bypass SSL verification
2025-08-02 19:47:25,872 - utils.qdrant_utils - INFO - Loaded SPLADE model: naver/efficient-splade-VI-BT-large-doc
2025-08-02 19:47:25,873 - utils.qdrant_utils - INFO - Initializing QdrantManager with URL: http://localhost:6333
2025-08-02 19:47:25,886 - httpx - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-02 19:47:25,899 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections "HTTP/1.1 200 OK"
2025-08-02 19:47:25,899 - utils.qdrant_utils - INFO - ✅ QdrantClient initialized successfully
2025-08-02 19:47:26,776 - utils.qdrant_utils - INFO - ✅ Sparse embedder initialized with model: Qdrant/bm25
2025-08-02 19:47:26,776 - utils.qdrant_utils - INFO - 🔍 FastEmbed creates sparse vector type: <class 'langchain_qdrant.sparse_embeddings.SparseVector'>
2025-08-02 19:47:26,776 - utils.qdrant_utils - INFO - 🔍 FastEmbed sparse vector indices count: 1
2025-08-02 19:47:26,776 - manual_test - INFO - 🧠 Using smart-selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-02 19:47:26,776 - manual_test - INFO - 🧠 Using smart-selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-02 19:47:26,776 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-08-02 19:47:26,776 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-02 19:47:31,063 - manual_test - INFO - 🔍 Adding notification to metadata_vectors with ID: ceffd7c8-ae16-4b9e-a0c0-350eb45eeca7
2025-08-02 19:47:31,063 - manual_test - INFO - 🔍 Adding notification to metadata_vectors with ID: ceffd7c8-ae16-4b9e-a0c0-350eb45eeca7
2025-08-02 19:47:31,063 - manual_test - INFO - 🔍 Vector keys: ['document_title_vec', 'document_number_vec', 'short_summary_vec', 'document_id_vec']
2025-08-02 19:47:31,063 - manual_test - INFO - 🔍 Vector keys: ['document_title_vec', 'document_number_vec', 'short_summary_vec', 'document_id_vec']
2025-08-02 19:47:31,063 - manual_test - INFO - 🔍 Vector dimensions: [('document_title_vec', 384), ('document_number_vec', 384), ('short_summary_vec', 384), ('document_id_vec', 384)]
2025-08-02 19:47:31,063 - manual_test - INFO - 🔍 Vector dimensions: [('document_title_vec', 384), ('document_number_vec', 384), ('short_summary_vec', 384), ('document_id_vec', 384)]
2025-08-02 19:47:31,070 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/metadata_vectors "HTTP/1.1 200 OK"
2025-08-02 19:47:31,085 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/metadata_vectors/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:47:31,085 - manual_test - INFO - ✅ Added notification to metadata_vectors: ceffd7c8-ae16-4b9e-a0c0-350eb45eeca7
2025-08-02 19:47:31,085 - manual_test - INFO - ✅ Added notification to metadata_vectors: ceffd7c8-ae16-4b9e-a0c0-350eb45eeca7
2025-08-02 19:47:31,085 - manual_test - INFO -    📊 Notification metadata vectors UUIDs: 1
2025-08-02 19:47:31,085 - manual_test - INFO -    📊 Notification metadata vectors UUIDs: 1
2025-08-02 19:47:31,085 - manual_test - INFO - --- ENHANCED Stage 3: STREAMLINED PDF Processing and Action Execution ---
2025-08-02 19:47:31,085 - manual_test - INFO - --- ENHANCED Stage 3: STREAMLINED PDF Processing and Action Execution ---
2025-08-02 19:47:31,085 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:31,085 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:31,159 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:31,159 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:31,347 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:31,347 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:31,354 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:47:31,354 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:47:31,362 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:31,362 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:31,362 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['DoR.STR.REC.3/09.27.000', 'UBD.PCB.No.59/13.05.000/2008', 'BSD.No.IP.30/12.05.05/2002', '.No.59/13.05.000/2008-09', 'UBD.PCB.No.36/13.05.000/2008-09', 'UBD.PCB.No.59/13.05.000/2008-09', '.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05', 'UBD.No.Plan.42/09.27.00', 'BSD.No.IP.30/12.05.05/2002-03', 'A.P. (DIR Series) Circular No. 18', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000/2024-25', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.36/13.05.000/2008', '.IP.30/12.05.05/2002-03', 'BPD.Cir.No.29/13.05.000', 'UBD.PCB.No.59/13.05.000', '.REC.3/09.27.000/2024-25']}
2025-08-02 19:47:31,362 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['DoR.STR.REC.3/09.27.000', 'UBD.PCB.No.59/13.05.000/2008', 'BSD.No.IP.30/12.05.05/2002', '.No.59/13.05.000/2008-09', 'UBD.PCB.No.36/13.05.000/2008-09', 'UBD.PCB.No.59/13.05.000/2008-09', '.No.36/13.05.000/2008-09', 'DoR.STR.REC.3/09.27.000/2024', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000', 'RBI/2025-26/11', 'BSD.No.IP.30/12.05.05', 'UBD.No.Plan.42/09.27.00', 'BSD.No.IP.30/12.05.05/2002-03', 'A.P. (DIR Series) Circular No. 18', 'Plan.Cir.SUB.1/09.27.00', 'DoR.STR.REC.3/09.27.000/2024-25', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.36/13.05.000/2008', '.IP.30/12.05.05/2002-03', 'BPD.Cir.No.29/13.05.000', 'UBD.PCB.No.59/13.05.000', '.REC.3/09.27.000/2024-25']}
2025-08-02 19:47:31,363 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:47:31,363 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:47:31,363 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:47:31,363 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:47:31,363 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:47:31,363 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:47:31,363 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:31,363 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:31,363 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:47:31,363 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:47:31,363 - manual_test - INFO - 🔧 Starting streamlined processing of 3 chunks
2025-08-02 19:47:31,363 - manual_test - INFO - 🔧 Starting streamlined processing of 3 chunks
2025-08-02 19:47:31,363 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 1/3
2025-08-02 19:47:31,363 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 1/3
2025-08-02 19:47:31,363 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 2513 chars
2025-08-02 19:47:31,363 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 2513 chars
2025-08-02 19:47:31,363 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:47:31,363 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:47:31,363 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:47:31,363 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:47:31,363 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 2513 chars
2025-08-02 19:47:31,363 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 2513 chars
2025-08-02 19:47:31,363 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:47:31,363 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:47:31,363 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:47:31,363 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:47:31,363 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o-mini for document_classification - Selected simple tier for document_classification (context: 491, priority: balanced)
2025-08-02 19:47:31,363 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o-mini for document_classification - Selected simple tier for document_classification (context: 491, priority: balanced)
2025-08-02 19:47:34,328 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:47:34,332 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:47:34,332 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:47:34,333 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular issued by the Reserv...
2025-08-02 19:47:34,333 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular issued by the Reserv...
2025-08-02 19:47:34,333 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:47:34,333 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:47:34,333 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:47:34,333 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:47:34,333 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular issued by the Reserve Bank of India, which consolidates existing instructions and guidelines related to guarantees, co-acceptances, and letters of credit for Urban Co-operative Banks (UCBs). It is regulatory in nature as it pertains to compliance obligations for banks, and it should be indexed for reference and compliance purposes."}
2025-08-02 19:47:34,333 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular issued by the Reserve Bank of India, which consolidates existing instructions and guidelines related to guarantees, co-acceptances, and letters of credit for Urban Co-operative Banks (UCBs). It is regulatory in nature as it pertains to compliance obligations for banks, and it should be indexed for reference and compliance purposes."}
2025-08-02 19:47:34,333 - manual_test - INFO - ✅ Chunk 1 classified as regulatory - INDEXING
2025-08-02 19:47:34,333 - manual_test - INFO - ✅ Chunk 1 classified as regulatory - INDEXING
2025-08-02 19:47:34,334 - root - INFO - Sending structured request to extract document actions
2025-08-02 19:47:38,929 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:47:38,935 - root - INFO - Received response type: <class '__main__.DocumentActionsList'>
2025-08-02 19:47:38,935 - root - INFO - ✅ Successfully received structured Pydantic output
2025-08-02 19:47:38,935 - manual_test - INFO - 🎯 Executing 1 actions for chunk 1
2025-08-02 19:47:38,935 - manual_test - INFO - 🎯 Executing 1 actions for chunk 1
2025-08-02 19:47:38,935 - manual_test - INFO - 🎯 Executing 1 chunk-specific actions
2025-08-02 19:47:38,935 - manual_test - INFO - 🎯 Executing 1 chunk-specific actions
2025-08-02 19:47:38,935 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-02 19:47:38,935 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-02 19:47:38,936 - manual_test - INFO - 📝 Sample action: {'document_id': 'DoR.STR.REC.3/09.27.000/2024-25', 'action_type': 'UPDATE_DOCUMENT', 'confidence': 'high', 'document_title': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs', 'document_url': None, 'reference_number': None, 'department': 'DoR', 'original_date': '2024-04-01', 'update_location': 'full-document', 'sunset_withdraw_date': None, 'reasoning': 'The document has been revised to consolidate all instructions issued up to March 31, 2025.'}
2025-08-02 19:47:38,936 - manual_test - INFO - 📝 Sample action: {'document_id': 'DoR.STR.REC.3/09.27.000/2024-25', 'action_type': 'UPDATE_DOCUMENT', 'confidence': 'high', 'document_title': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs', 'document_url': None, 'reference_number': None, 'department': 'DoR', 'original_date': '2024-04-01', 'update_location': 'full-document', 'sunset_withdraw_date': None, 'reasoning': 'The document has been revised to consolidate all instructions issued up to March 31, 2025.'}
2025-08-02 19:47:38,936 - manual_test - INFO - ▶️ Processing action 1/1
2025-08-02 19:47:38,936 - manual_test - INFO - ▶️ Processing action 1/1
2025-08-02 19:47:38,936 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_DoR_STR_REC_3_0_chunk0_da30ad62
2025-08-02 19:47:38,936 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_DoR_STR_REC_3_0_chunk0_da30ad62
2025-08-02 19:47:38,936 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:38,936 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:38,936 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:38,936 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:38,936 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:47:38,937 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:38,937 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:38,937 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:47:38,937 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:47:38,981 - utils.metadata_vector_utils - INFO - Searching metadata_vectors for 'DoR.STR.REC.3/09.27.000/2024-25' in field 'document_id'
2025-08-02 19:47:39,031 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-02 19:47:39,032 - utils.metadata_vector_utils - INFO - Found 5 results for metadata search
2025-08-02 19:47:39,032 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:47:39,032 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9741a628-999e-4b3f-aa8d-da299fe6f6c1, Score=0.8026, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-02 19:47:39,032 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3d495fa6-6797-461c-bf81-7de0eb59a24a, Score=0.8026, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-02 19:47:39,032 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2105f02f-10ac-520c-82c1-d91e04509d6d, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:47:39,032 - knowledge_base_executor - INFO - [QDRANT] Match: ID=d2b23822-c5a2-5527-8507-5b3d29d5794c, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:47:39,032 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d04a677-f401-5734-88ac-14846a6cf5b3, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:47:39,032 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:47:39,032 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:47:39,034 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:47:39,034 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:47:39,034 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:47:39,035 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:47:39,035 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:47:39,035 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:47:39,035 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:47:39,035 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:47:39,035 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:47:39,035 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:17:39.035249"}
2025-08-02 19:47:39,035 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:47:39,035 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:47:39,036 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:47:39,036 - manual_test - WARNING - ⚠️ Action execution failed: None
2025-08-02 19:47:39,036 - manual_test - WARNING - ⚠️ Action execution failed: None
2025-08-02 19:47:39,036 - manual_test - INFO - 📊 Chunk action execution: 0/1 successful
2025-08-02 19:47:39,036 - manual_test - INFO - 📊 Chunk action execution: 0/1 successful
2025-08-02 19:47:39,036 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 1:
2025-08-02 19:47:39,036 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 1:
2025-08-02 19:47:39,036 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2025-26/11
2025-08-02 19:47:39,036 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2025-26/11
2025-08-02 19:47:39,036 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2025-26/11
2025-08-02 19:47:39,036 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2025-26/11
2025-08-02 19:47:39,036 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-02 19:47:39,036 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-02 19:47:39,036 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:47:39,036 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:39,036 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:39,037 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:47:39,037 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=b8179081-4b54-4bb7-9ba5-721eb8f2f9df, Document=None, Chunk=0
2025-08-02 19:47:39,037 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 2513 chars
2025-08-02 19:47:39,039 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular/exists "HTTP/1.1 200 OK"
2025-08-02 19:47:39,041 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:47:39,042 - qdrant_utils - INFO - 🔍 Collection rbi_circular vectors: size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:47:39,042 - qdrant_utils - INFO - 🔍 Collection rbi_circular sparse vectors: {'fast-sparse-bm25': SparseVectorParams(index=SparseIndexParams(full_scan_threshold=None, on_disk=None, datatype=None), modifier=<Modifier.IDF: 'idf'>)}
2025-08-02 19:47:39,042 - qdrant_utils - WARNING - Collection rbi_circular exists but uses hybrid/single format. Will work with existing.
2025-08-02 19:47:41,141 - openai_utils - INFO - Embedding 1 documents
2025-08-02 19:47:42,103 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-02 19:47:42,243 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:47:42,244 - qdrant_utils - INFO - Collection info for rbi_circular: vectors=size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:47:42,244 - qdrant_utils - INFO - Collection rbi_circular uses hybrid format (single dense + sparse)
2025-08-02 19:47:42,244 - qdrant_utils - INFO - Collection format: hybrid
2025-08-02 19:47:42,244 - qdrant_utils - INFO - ✅ Added dense vector with 3072 dimensions
2025-08-02 19:47:42,246 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:47:42,247 - qdrant_utils - INFO - ✅ Added sparse vector 'fast-sparse-bm25' with 162 indices
2025-08-02 19:47:42,247 - qdrant_utils - WARNING - ⚠️ Skipping sparse vector 'splade' - not configured in collection schema
2025-08-02 19:47:42,247 - qdrant_utils - INFO - 🔍 Final vector keys for point b8179081-4b54-4bb7-9ba5-721eb8f2f9df: ['', 'fast-sparse-bm25']
2025-08-02 19:47:42,264 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:47:42,264 - qdrant_utils - INFO - ✅ Using hybrid format for point b8179081-4b54-4bb7-9ba5-721eb8f2f9df
2025-08-02 19:47:42,264 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: b8179081-4b54-4bb7-9ba5-721eb8f2f9df)
2025-08-02 19:47:42,264 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: b8179081-4b54-4bb7-9ba5-721eb8f2f9df) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:17:42.264776", "document_id": null, "uuid": "b8179081-4b54-4bb7-9ba5-721eb8f2f9df", "collection": "rbi_circular"}
2025-08-02 19:47:42,264 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:47:42,264 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:47:42,265 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:47:42,265 - manual_test - INFO - 🆔 CHUNK UUID: b8179081-4b54-4bb7-9ba5-721eb8f2f9df
2025-08-02 19:47:42,265 - manual_test - INFO - 🆔 CHUNK UUID: b8179081-4b54-4bb7-9ba5-721eb8f2f9df
2025-08-02 19:47:42,266 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 1
2025-08-02 19:47:42,266 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 1
2025-08-02 19:47:42,266 - manual_test - INFO -    ✅ Chunk 1 added to processing queue
2025-08-02 19:47:42,266 - manual_test - INFO -    ✅ Chunk 1 added to processing queue
2025-08-02 19:47:42,266 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 2/3
2025-08-02 19:47:42,266 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 2/3
2025-08-02 19:47:42,266 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 8230 chars
2025-08-02 19:47:42,266 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 8230 chars
2025-08-02 19:47:42,266 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:47:42,266 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:47:42,266 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:47:42,266 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:47:42,266 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 8230 chars
2025-08-02 19:47:42,266 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 8230 chars
2025-08-02 19:47:42,267 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:47:42,267 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:47:42,267 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:47:42,267 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:47:42,267 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o for document_classification - Selected medium tier for document_classification (context: 1206, priority: balanced)
2025-08-02 19:47:42,267 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o for document_classification - Selected medium tier for document_classification (context: 1206, priority: balanced)
2025-08-02 19:47:47,225 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:47:47,227 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:47:47,227 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:47:47,227 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular, which typically con...
2025-08-02 19:47:47,227 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular, which typically con...
2025-08-02 19:47:47,227 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:47:47,227 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:47:47,228 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:47:47,228 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:47:47,228 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular, which typically consolidates regulatory guidelines and requirements issued by the RBI. It contains detailed instructions and guidelines on guarantees, co-acceptances, and letters of credit for Urban Co-operative Banks (UCBs), indicating its regulatory nature. Master Circulars are essential for compliance and understanding regulatory obligations, making them suitable for indexing in the knowledge base. The content is not administrative or withdrawn, and the detailed guidelines suggest a high confidence level in this classification."}
2025-08-02 19:47:47,228 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular, which typically consolidates regulatory guidelines and requirements issued by the RBI. It contains detailed instructions and guidelines on guarantees, co-acceptances, and letters of credit for Urban Co-operative Banks (UCBs), indicating its regulatory nature. Master Circulars are essential for compliance and understanding regulatory obligations, making them suitable for indexing in the knowledge base. The content is not administrative or withdrawn, and the detailed guidelines suggest a high confidence level in this classification."}
2025-08-02 19:47:47,228 - manual_test - INFO - ✅ Chunk 2 classified as regulatory - INDEXING
2025-08-02 19:47:47,228 - manual_test - INFO - ✅ Chunk 2 classified as regulatory - INDEXING
2025-08-02 19:47:47,228 - root - INFO - Sending structured request to extract document actions
2025-08-02 19:47:50,501 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:47:50,507 - root - INFO - Received response type: <class '__main__.DocumentActionsList'>
2025-08-02 19:47:50,507 - root - INFO - ✅ Successfully received structured Pydantic output
2025-08-02 19:47:50,507 - manual_test - INFO - 🎯 Executing 1 actions for chunk 2
2025-08-02 19:47:50,507 - manual_test - INFO - 🎯 Executing 1 actions for chunk 2
2025-08-02 19:47:50,507 - manual_test - INFO - 🎯 Executing 1 chunk-specific actions
2025-08-02 19:47:50,507 - manual_test - INFO - 🎯 Executing 1 chunk-specific actions
2025-08-02 19:47:50,508 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-02 19:47:50,508 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-02 19:47:50,508 - manual_test - INFO - 📝 Sample action: {'document_id': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs', 'action_type': 'UPDATE_DOCUMENT', 'confidence': 'high', 'document_title': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit', 'document_url': None, 'reference_number': None, 'department': 'Department of Regulation', 'original_date': None, 'update_location': 'full-document', 'sunset_withdraw_date': None, 'reasoning': 'The document outlines updated guidelines and safeguards for the issuance of guarantees, co-acceptances, and letters of credit by Urban Co-operative Banks (UCBs).'}
2025-08-02 19:47:50,508 - manual_test - INFO - 📝 Sample action: {'document_id': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs', 'action_type': 'UPDATE_DOCUMENT', 'confidence': 'high', 'document_title': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit', 'document_url': None, 'reference_number': None, 'department': 'Department of Regulation', 'original_date': None, 'update_location': 'full-document', 'sunset_withdraw_date': None, 'reasoning': 'The document outlines updated guidelines and safeguards for the issuance of guarantees, co-acceptances, and letters of credit by Urban Co-operative Banks (UCBs).'}
2025-08-02 19:47:50,508 - manual_test - INFO - ▶️ Processing action 1/1
2025-08-02 19:47:50,508 - manual_test - INFO - ▶️ Processing action 1/1
2025-08-02 19:47:50,508 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_Master_Circular_chunk1_099fb094
2025-08-02 19:47:50,508 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_Master_Circular_chunk1_099fb094
2025-08-02 19:47:50,508 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs
2025-08-02 19:47:50,508 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs
2025-08-02 19:47:50,509 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs
2025-08-02 19:47:50,509 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs
2025-08-02 19:47:50,509 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:47:50,510 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:50,510 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:50,510 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:47:50,510 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:47:50,655 - utils.metadata_vector_utils - INFO - Searching metadata_vectors for 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs' in field 'document_id'
2025-08-02 19:47:50,668 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-02 19:47:50,668 - utils.metadata_vector_utils - INFO - Found 5 results for metadata search
2025-08-02 19:47:50,668 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:47:50,668 - knowledge_base_executor - INFO - [QDRANT] Match: ID=521d0371-918a-4608-a972-d5d632952cc8, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:47:50,668 - knowledge_base_executor - INFO - [QDRANT] Match: ID=7940f618-ba47-45d9-aa0d-98ebba0fb222, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:47:50,668 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ccd83ffc-2347-403f-92cc-2aeb36cd3027, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:47:50,668 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] Match: ID=c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs' → 'MasterCircular-Guarantees,Co-acceptances&LettersofCredit-UCBs'
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: MasterCircular-Guarantees,Co-acceptances&LettersofCredit-UCBs
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:47:50,669 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:47:50,669 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:47:50,669 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:47:50,670 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:47:50,670 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:47:50,670 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:47:50,670 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:17:50.670121"}
2025-08-02 19:47:50,670 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:47:50,670 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:47:50,671 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:47:50,671 - manual_test - WARNING - ⚠️ Action execution failed: None
2025-08-02 19:47:50,671 - manual_test - WARNING - ⚠️ Action execution failed: None
2025-08-02 19:47:50,671 - manual_test - INFO - 📊 Chunk action execution: 0/1 successful
2025-08-02 19:47:50,671 - manual_test - INFO - 📊 Chunk action execution: 0/1 successful
2025-08-02 19:47:50,671 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 2:
2025-08-02 19:47:50,671 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 2:
2025-08-02 19:47:50,671 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2025-26/11
2025-08-02 19:47:50,671 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2025-26/11
2025-08-02 19:47:50,671 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2025-26/11
2025-08-02 19:47:50,671 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2025-26/11
2025-08-02 19:47:50,671 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-02 19:47:50,671 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-02 19:47:50,671 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:47:50,671 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:50,671 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:47:50,671 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:47:50,671 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=6c436f7b-2815-4b99-97ca-672a586b1a11, Document=None, Chunk=1
2025-08-02 19:47:50,671 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-02 19:47:50,674 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular/exists "HTTP/1.1 200 OK"
2025-08-02 19:47:50,677 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:47:50,677 - qdrant_utils - INFO - 🔍 Collection rbi_circular vectors: size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:47:50,677 - qdrant_utils - INFO - 🔍 Collection rbi_circular sparse vectors: {'fast-sparse-bm25': SparseVectorParams(index=SparseIndexParams(full_scan_threshold=None, on_disk=None, datatype=None), modifier=<Modifier.IDF: 'idf'>)}
2025-08-02 19:47:50,677 - qdrant_utils - WARNING - Collection rbi_circular exists but uses hybrid/single format. Will work with existing.
2025-08-02 19:47:53,252 - openai_utils - INFO - Embedding 1 documents
2025-08-02 19:47:54,802 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-02 19:47:55,088 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:47:55,089 - qdrant_utils - INFO - Collection info for rbi_circular: vectors=size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:47:55,089 - qdrant_utils - INFO - Collection rbi_circular uses hybrid format (single dense + sparse)
2025-08-02 19:47:55,089 - qdrant_utils - INFO - Collection format: hybrid
2025-08-02 19:47:55,089 - qdrant_utils - INFO - ✅ Added dense vector with 3072 dimensions
2025-08-02 19:47:55,094 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:47:55,094 - qdrant_utils - INFO - ✅ Added sparse vector 'fast-sparse-bm25' with 408 indices
2025-08-02 19:47:55,094 - qdrant_utils - WARNING - ⚠️ Skipping sparse vector 'splade' - not configured in collection schema
2025-08-02 19:47:55,094 - qdrant_utils - INFO - 🔍 Final vector keys for point 6c436f7b-2815-4b99-97ca-672a586b1a11: ['', 'fast-sparse-bm25']
2025-08-02 19:47:55,108 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:47:55,108 - qdrant_utils - INFO - ✅ Using hybrid format for point 6c436f7b-2815-4b99-97ca-672a586b1a11
2025-08-02 19:47:55,108 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 6c436f7b-2815-4b99-97ca-672a586b1a11)
2025-08-02 19:47:55,108 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 6c436f7b-2815-4b99-97ca-672a586b1a11) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:17:55.108747", "document_id": null, "uuid": "6c436f7b-2815-4b99-97ca-672a586b1a11", "collection": "rbi_circular"}
2025-08-02 19:47:55,108 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:47:55,108 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:47:55,109 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:47:55,109 - manual_test - INFO - 🆔 CHUNK UUID: 6c436f7b-2815-4b99-97ca-672a586b1a11
2025-08-02 19:47:55,109 - manual_test - INFO - 🆔 CHUNK UUID: 6c436f7b-2815-4b99-97ca-672a586b1a11
2025-08-02 19:47:55,109 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 2
2025-08-02 19:47:55,109 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 2
2025-08-02 19:47:55,109 - manual_test - INFO -    ✅ Chunk 2 added to processing queue
2025-08-02 19:47:55,109 - manual_test - INFO -    ✅ Chunk 2 added to processing queue
2025-08-02 19:47:55,109 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 3/3
2025-08-02 19:47:55,109 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 3/3
2025-08-02 19:47:55,109 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 38710 chars
2025-08-02 19:47:55,109 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 38710 chars
2025-08-02 19:47:55,109 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:47:55,109 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:47:55,109 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:47:55,109 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:47:55,109 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 38710 chars
2025-08-02 19:47:55,109 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 38710 chars
2025-08-02 19:47:55,110 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:47:55,110 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:47:55,110 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:47:55,110 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:47:55,110 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o for document_classification - Selected medium tier for document_classification (context: 5883, priority: balanced)
2025-08-02 19:47:55,110 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o for document_classification - Selected medium tier for document_classification (context: 5883, priority: balanced)
2025-08-02 19:48:01,972 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:48:01,975 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:48:01,975 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:48:01,975 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular that consolidates va...
2025-08-02 19:48:01,975 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular that consolidates va...
2025-08-02 19:48:01,975 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:48:01,975 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:48:01,976 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:48:01,976 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:48:01,976 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular that consolidates various guidelines and regulatory requirements related to guarantees, co-acceptances, and letters of credit for Urban Co-operative Banks (UCBs). It provides detailed instructions on compliance obligations, exposure norms, and safeguards that banks must adhere to. The content is regulatory in nature, as it outlines specific rules and procedures that banks need to follow, making it essential for compliance understanding. Therefore, it should be indexed in the knowledge base under the 'rbi_master_circular' collection to ensure accessibility for compliance purposes."}
2025-08-02 19:48:01,976 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular that consolidates various guidelines and regulatory requirements related to guarantees, co-acceptances, and letters of credit for Urban Co-operative Banks (UCBs). It provides detailed instructions on compliance obligations, exposure norms, and safeguards that banks must adhere to. The content is regulatory in nature, as it outlines specific rules and procedures that banks need to follow, making it essential for compliance understanding. Therefore, it should be indexed in the knowledge base under the 'rbi_master_circular' collection to ensure accessibility for compliance purposes."}
2025-08-02 19:48:01,976 - manual_test - INFO - ✅ Chunk 3 classified as regulatory - INDEXING
2025-08-02 19:48:01,976 - manual_test - INFO - ✅ Chunk 3 classified as regulatory - INDEXING
2025-08-02 19:48:01,976 - root - INFO - Sending structured request to extract document actions
