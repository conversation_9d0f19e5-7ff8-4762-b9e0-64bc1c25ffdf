2025-08-04 08:31:31,905 - knowledge_base_executor - <PERSON><PERSON><PERSON> - <PERSON><PERSON> configured to write logs to: logs/knowledge_base_executor_execution_20250804_083131.log
2025-08-04 08:31:31,905 - knowledge_base_executor - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-04 08:31:35,532 - knowledge_base_executor - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-04 08:31:48,904 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:31:48,904 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 08:31:48,904 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 08:31:48,904 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:31:48,904 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=e6ff4217-5820-508f-959b-bef2cf73c229, Document=RBI/2024-25/112, Chunk=None
2025-08-04 08:31:48,904 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/112 with text: 6475 chars
2025-08-04 08:31:52,691 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/112 (ID: e6ff4217-5820-508f-959b-bef2cf73c229)
2025-08-04 08:31:52,691 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/112 (ID: e6ff4217-5820-508f-959b-bef2cf73c229) to collections: ['rbi_other']", "timestamp": "2025-08-04T03:01:52.691886", "document_id": "RBI/2024-25/112", "uuid": "e6ff4217-5820-508f-959b-bef2cf73c229", "collection": "rbi_other"}
2025-08-04 08:31:52,691 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:31:52,691 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:31:52,693 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:32:02,746 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:32:02,746 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 08:32:02,746 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 08:32:02,747 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:32:02,747 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=41dea3ce-3b83-5b9b-860c-6f894fb05ef6, Document=RBI/2024-25/112, Chunk=0
2025-08-04 08:32:02,747 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/112 with text: 3340 chars
2025-08-04 08:32:07,298 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/112 (ID: 41dea3ce-3b83-5b9b-860c-6f894fb05ef6)
2025-08-04 08:32:07,298 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/112 (ID: 41dea3ce-3b83-5b9b-860c-6f894fb05ef6) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T03:02:07.298174", "document_id": "RBI/2024-25/112", "uuid": "41dea3ce-3b83-5b9b-860c-6f894fb05ef6", "collection": "rbi_master_direction"}
2025-08-04 08:32:07,298 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:32:07,298 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:32:07,298 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:32:07,299 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:32:07,299 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 08:32:07,299 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/All Agency Banks to remain open for public on March 31_ 2025 _Monday_.pdf
2025-08-04 08:32:07,299 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:32:07,299 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=33d1877c-0c13-4728-bf87-3de1754e6788, Document=None, Chunk=0
2025-08-04 08:32:07,299 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 3229 chars
2025-08-04 08:32:08,534 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 33d1877c-0c13-4728-bf87-3de1754e6788)
2025-08-04 08:32:08,534 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 33d1877c-0c13-4728-bf87-3de1754e6788) to collections: ['rbi_circular']", "timestamp": "2025-08-04T03:02:08.534302", "document_id": null, "uuid": "33d1877c-0c13-4728-bf87-3de1754e6788", "collection": "rbi_circular"}
2025-08-04 08:32:08,534 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:32:08,534 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:32:08,535 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:32:16,805 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:32:16,805 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 08:32:16,805 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 08:32:16,806 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:32:16,806 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=e506dac2-f0bd-522d-8fde-d94a24b336e1, Document=RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26, Chunk=None
2025-08-04 08:32:16,806 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26 with text: 8000 chars
2025-08-04 08:32:21,232 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26 (ID: e506dac2-f0bd-522d-8fde-d94a24b336e1)
2025-08-04 08:32:21,232 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26 (ID: e506dac2-f0bd-522d-8fde-d94a24b336e1) to collections: ['rbi_other']", "timestamp": "2025-08-04T03:02:21.232955", "document_id": "RBI/2025-26/38 - DoR.RET.REC.20/12.07.160/2025-26", "uuid": "e506dac2-f0bd-522d-8fde-d94a24b336e1", "collection": "rbi_other"}
2025-08-04 08:32:21,233 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:32:21,233 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:32:21,233 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:32:32,231 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:32:32,231 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 08:32:32,231 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 08:32:32,231 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:32:32,231 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:32:32,340 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:32:32,340 - knowledge_base_executor - INFO - [QDRANT] Match: ID=aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4, Score=0.9121, Document=DoR.LIC.No.S2196/16.13.215/2025-26
2025-08-04 08:32:32,340 - knowledge_base_executor - INFO - [QDRANT] Match: ID=869f3b04-ec2f-595b-a89d-b1954df61f84, Score=0.9121, Document=DoR.LIC.No.S2196/16.13.215/2025-26
2025-08-04 08:32:32,340 - knowledge_base_executor - INFO - [QDRANT] Match: ID=1fb27723-a8c3-5847-8444-2413ace14ab0, Score=0.9121, Document=DoR.LIC.No.S2196/16.13.215/2025-26
2025-08-04 08:32:32,340 - knowledge_base_executor - INFO - [QDRANT] Match: ID=55c9fe74-886e-5477-8ed6-60c7dfda12b8, Score=0.9121, Document=DoR.LIC.No.S2196/16.13.215/2025-26
2025-08-04 08:32:32,340 - knowledge_base_executor - INFO - [QDRANT] Match: ID=a6aac802-03c2-508c-b910-f3a658bc3425, Score=0.9121, Document=DoR.LIC.No.S2196/16.13.215/2025-26
2025-08-04 08:32:32,340 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:32:32,340 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:32:32,341 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DoR.LIC.No.S1134/16.13.216/2025-26
2025-08-04 08:32:32,341 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', 'a6aac802-03c2-508c-b910-f3a658bc3425']}
2025-08-04 08:32:32,341 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:32:32,341 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4
2025-08-04 08:32:32,342 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 869f3b04-ec2f-595b-a89d-b1954df61f84
2025-08-04 08:32:32,342 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 1fb27723-a8c3-5847-8444-2413ace14ab0
2025-08-04 08:32:32,342 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0']...
2025-08-04 08:32:32,342 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:32:32,342 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', 'a6aac802-03c2-508c-b910-f3a658bc3425'])] must_not=None, Payload={}
2025-08-04 08:32:32,356 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-04 08:32:32,372 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3204 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:32:32,372 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-04 08:32:32,372 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:32:32,372 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:32:32,372 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', 'a6aac802-03c2-508c-b910-f3a658bc3425']}
2025-08-04 08:32:32,372 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:32:32,372 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4
2025-08-04 08:32:32,372 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 869f3b04-ec2f-595b-a89d-b1954df61f84
2025-08-04 08:32:32,372 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 1fb27723-a8c3-5847-8444-2413ace14ab0
2025-08-04 08:32:32,372 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0']...
2025-08-04 08:32:32,372 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:32:32,372 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', 'a6aac802-03c2-508c-b910-f3a658bc3425'])] must_not=None, Payload={}
2025-08-04 08:32:32,374 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:32:32,374 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:32:32,374 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:32:32,374 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:32:32,374 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', 'a6aac802-03c2-508c-b910-f3a658bc3425']}
2025-08-04 08:32:32,374 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:32:32,374 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4
2025-08-04 08:32:32,374 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 869f3b04-ec2f-595b-a89d-b1954df61f84
2025-08-04 08:32:32,374 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 1fb27723-a8c3-5847-8444-2413ace14ab0
2025-08-04 08:32:32,374 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0']...
2025-08-04 08:32:32,374 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:32:32,375 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['aa5a3ff7-8438-5bb0-86ba-1960fca4a6e4', '869f3b04-ec2f-595b-a89d-b1954df61f84', '1fb27723-a8c3-5847-8444-2413ace14ab0', '55c9fe74-886e-5477-8ed6-60c7dfda12b8', 'a6aac802-03c2-508c-b910-f3a658bc3425'])] must_not=None, Payload={}
2025-08-04 08:32:32,376 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:32:32,376 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:32:32,377 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T03:02:32.376948"}
2025-08-04 08:32:32,377 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:32:32,377 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:32:32,377 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:32:32,378 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:32:32,378 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 08:32:32,378 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Alteration in the name of _North East Small Finance Bank Limited_ to _slice Small Finance Bank Limited_ in the Second Schedule to the Reserve Bank of India Act_ 1934.pdf
2025-08-04 08:32:32,378 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:32:32,378 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=141e7498-c9a4-410d-95f4-1b585c878a5d, Document=None, Chunk=0
2025-08-04 08:32:32,378 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 5161 chars
2025-08-04 08:32:35,332 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 141e7498-c9a4-410d-95f4-1b585c878a5d)
2025-08-04 08:32:35,332 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 141e7498-c9a4-410d-95f4-1b585c878a5d) to collections: ['rbi_circular']", "timestamp": "2025-08-04T03:02:35.332912", "document_id": null, "uuid": "141e7498-c9a4-410d-95f4-1b585c878a5d", "collection": "rbi_circular"}
2025-08-04 08:32:35,332 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:32:35,333 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:32:35,333 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:32:43,766 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:32:43,766 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:32:43,767 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:32:43,767 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:32:43,767 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=e06fa685-1b8f-5789-9e01-80d64dfaf530, Document=RBI/FED/2025-26/29 - A.P. (DIR Series) Circular No. 17, Chunk=None
2025-08-04 08:32:43,767 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/FED/2025-26/29 - A.P. (DIR Series) Circular No. 17 with text: 8000 chars
2025-08-04 08:32:47,866 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/FED/2025-26/29 - A.P. (DIR Series) Circular No. 17 (ID: e06fa685-1b8f-5789-9e01-80d64dfaf530)
2025-08-04 08:32:47,866 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/FED/2025-26/29 - A.P. (DIR Series) Circular No. 17 (ID: e06fa685-1b8f-5789-9e01-80d64dfaf530) to collections: ['rbi_other']", "timestamp": "2025-08-04T03:02:47.866711", "document_id": "RBI/FED/2025-26/29 - A.P. (DIR Series) Circular No. 17", "uuid": "e06fa685-1b8f-5789-9e01-80d64dfaf530", "collection": "rbi_other"}
2025-08-04 08:32:47,866 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:32:47,866 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:32:47,867 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:32:52,547 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:32:52,547 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:32:52,548 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:32:52,548 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:32:52,548 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:32:52,637 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:32:52,637 - knowledge_base_executor - INFO - [QDRANT] Match: ID=1a281f72-acde-49f1-9f3a-0cda45df7f8b, Score=0.5246, Document=FEMA 10(R)/2015-RB
2025-08-04 08:32:52,638 - knowledge_base_executor - INFO - [QDRANT] Match: ID=4700aab3-5bc9-5aac-a983-e08bd853f0bb, Score=0.5129, Document=FEMA 23(R)/2015-RB
2025-08-04 08:32:52,638 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d0e725b-aeac-5e6b-80ff-92fe03d7caf2, Score=0.5129, Document=FEMA 23(R)/2015-RB
2025-08-04 08:32:52,638 - knowledge_base_executor - INFO - [QDRANT] Match: ID=81ccb53b-4614-5c94-a95b-f02af834fc91, Score=0.5129, Document=FEMA 23(R)/2015-RB
2025-08-04 08:32:52,638 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06f77fc3-7228-514f-8562-24519f5d9d4a, Score=0.5129, Document=FEMA 23(R)/2015-RB
2025-08-04 08:32:52,638 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:32:52,638 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:32:52,638 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'FEMA 1999 Directions' → 'FEMA1999Directions'
2025-08-04 08:32:52,638 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: FEMA1999Directions
2025-08-04 08:32:52,638 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '81ccb53b-4614-5c94-a95b-f02af834fc91', '06f77fc3-7228-514f-8562-24519f5d9d4a']}
2025-08-04 08:32:52,638 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:32:52,638 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 1a281f72-acde-49f1-9f3a-0cda45df7f8b
2025-08-04 08:32:52,638 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:32:52,638 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:32:52,638 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2']...
2025-08-04 08:32:52,638 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:32:52,638 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '81ccb53b-4614-5c94-a95b-f02af834fc91', '06f77fc3-7228-514f-8562-24519f5d9d4a'])] must_not=None, Payload={}
2025-08-04 08:32:52,648 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 4 matching docs in rbi_master_direction
2025-08-04 08:32:52,664 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3205 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:32:52,664 - knowledge_base_executor - INFO - ✅ Successfully updated 4 documents in collection: rbi_master_direction
2025-08-04 08:32:52,664 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:32:52,664 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:32:52,664 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '81ccb53b-4614-5c94-a95b-f02af834fc91', '06f77fc3-7228-514f-8562-24519f5d9d4a']}
2025-08-04 08:32:52,664 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:32:52,664 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 1a281f72-acde-49f1-9f3a-0cda45df7f8b
2025-08-04 08:32:52,664 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:32:52,664 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:32:52,664 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2']...
2025-08-04 08:32:52,664 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:32:52,664 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '81ccb53b-4614-5c94-a95b-f02af834fc91', '06f77fc3-7228-514f-8562-24519f5d9d4a'])] must_not=None, Payload={}
2025-08-04 08:32:52,666 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:32:52,666 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:32:52,666 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:32:52,666 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:32:52,667 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '81ccb53b-4614-5c94-a95b-f02af834fc91', '06f77fc3-7228-514f-8562-24519f5d9d4a']}
2025-08-04 08:32:52,667 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:32:52,667 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 1a281f72-acde-49f1-9f3a-0cda45df7f8b
2025-08-04 08:32:52,667 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 4700aab3-5bc9-5aac-a983-e08bd853f0bb
2025-08-04 08:32:52,667 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5d0e725b-aeac-5e6b-80ff-92fe03d7caf2
2025-08-04 08:32:52,667 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2']...
2025-08-04 08:32:52,667 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:32:52,667 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['1a281f72-acde-49f1-9f3a-0cda45df7f8b', '4700aab3-5bc9-5aac-a983-e08bd853f0bb', '5d0e725b-aeac-5e6b-80ff-92fe03d7caf2', '81ccb53b-4614-5c94-a95b-f02af834fc91', '06f77fc3-7228-514f-8562-24519f5d9d4a'])] must_not=None, Payload={}
2025-08-04 08:32:52,669 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:32:52,669 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:32:52,670 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T03:02:52.669940"}
2025-08-04 08:32:52,670 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:32:52,670 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:32:52,670 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:32:52,671 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:32:52,671 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:32:52,671 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:32:52,671 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:32:52,671 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=3e937690-315c-48a4-9693-c4b3599bd898, Document=None, Chunk=0
2025-08-04 08:32:52,671 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 1537 chars
2025-08-04 08:32:56,386 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 3e937690-315c-48a4-9693-c4b3599bd898)
2025-08-04 08:32:56,386 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 3e937690-315c-48a4-9693-c4b3599bd898) to collections: ['rbi_circular']", "timestamp": "2025-08-04T03:02:56.386163", "document_id": null, "uuid": "3e937690-315c-48a4-9693-c4b3599bd898", "collection": "rbi_circular"}
2025-08-04 08:32:56,386 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:32:56,386 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:32:56,386 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:33:02,236 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:33:02,236 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:33:02,236 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:33:02,236 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:33:02,236 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] Match: ID=f7e9c5a4-9d26-40a5-9b97-4698177fce16, Score=0.9461, Document=A.P. (DIR Series) Circular No.25
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] Match: ID=f5227b9a-8ad4-4475-abb1-cad740204786, Score=0.9461, Document=A.P. (DIR Series) Circular No.25
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2dace538-c7dd-4d91-8a0c-98d413124556, Score=0.9450, Document=A.P.(DIR Series) Circular No.17
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] Match: ID=08eb16af-79fe-4148-8a1e-296a970e3fb1, Score=0.9450, Document=A.P.(DIR Series) Circular No.17
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] Match: ID=414991ea-c107-4d22-b4f8-1d3c9e37a606, Score=0.9450, Document=A.P.(DIR Series) Circular No.17
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'A.P. (DIR Series) Circular No. 17/2024-25' → 'A.P.(DIRSeries)CircularNo.17/2024-25'
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: A.P.(DIRSeries)CircularNo.17/2024-25
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '414991ea-c107-4d22-b4f8-1d3c9e37a606']}
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f7e9c5a4-9d26-40a5-9b97-4698177fce16
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f5227b9a-8ad4-4475-abb1-cad740204786
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2dace538-c7dd-4d91-8a0c-98d413124556
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556']...
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:33:02,313 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '414991ea-c107-4d22-b4f8-1d3c9e37a606'])] must_not=None, Payload={}
2025-08-04 08:33:02,316 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:33:02,316 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:33:02,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:33:02,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:33:02,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '414991ea-c107-4d22-b4f8-1d3c9e37a606']}
2025-08-04 08:33:02,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:33:02,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f7e9c5a4-9d26-40a5-9b97-4698177fce16
2025-08-04 08:33:02,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f5227b9a-8ad4-4475-abb1-cad740204786
2025-08-04 08:33:02,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2dace538-c7dd-4d91-8a0c-98d413124556
2025-08-04 08:33:02,316 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556']...
2025-08-04 08:33:02,317 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:33:02,317 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '414991ea-c107-4d22-b4f8-1d3c9e37a606'])] must_not=None, Payload={}
2025-08-04 08:33:02,319 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:33:02,319 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:33:02,319 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:33:02,319 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:33:02,319 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '414991ea-c107-4d22-b4f8-1d3c9e37a606']}
2025-08-04 08:33:02,319 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:33:02,319 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f7e9c5a4-9d26-40a5-9b97-4698177fce16
2025-08-04 08:33:02,319 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = f5227b9a-8ad4-4475-abb1-cad740204786
2025-08-04 08:33:02,319 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2dace538-c7dd-4d91-8a0c-98d413124556
2025-08-04 08:33:02,319 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556']...
2025-08-04 08:33:02,319 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:33:02,319 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['f7e9c5a4-9d26-40a5-9b97-4698177fce16', 'f5227b9a-8ad4-4475-abb1-cad740204786', '2dace538-c7dd-4d91-8a0c-98d413124556', '08eb16af-79fe-4148-8a1e-296a970e3fb1', '414991ea-c107-4d22-b4f8-1d3c9e37a606'])] must_not=None, Payload={}
2025-08-04 08:33:02,331 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:33:02,349 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13068 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:33:02,349 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:33:02,349 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T03:03:02.349569"}
2025-08-04 08:33:02,349 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:33:02,349 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:33:02,350 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:33:02,350 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:33:02,350 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:33:02,350 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amendments to Directions - Compounding of Contraventions under FEMA_ 1999.pdf
2025-08-04 08:33:02,350 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:33:02,350 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=8cb9ae28-72cd-462f-ae65-4b92d675c83c, Document=None, Chunk=1
2025-08-04 08:33:02,350 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-04 08:33:04,611 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 8cb9ae28-72cd-462f-ae65-4b92d675c83c)
2025-08-04 08:33:04,613 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 8cb9ae28-72cd-462f-ae65-4b92d675c83c) to collections: ['rbi_circular']", "timestamp": "2025-08-04T03:03:04.613375", "document_id": null, "uuid": "8cb9ae28-72cd-462f-ae65-4b92d675c83c", "collection": "rbi_circular"}
2025-08-04 08:33:04,613 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:33:04,613 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:33:04,614 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:33:13,250 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:33:13,250 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-04 08:33:13,250 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-04 08:33:13,251 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:33:13,251 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=5b14289e-75e9-52cc-962a-e43f1f24d47e, Document=RBI/2024-2025/125 - A.P. (DIR Series) Circular No. 22, Chunk=None
2025-08-04 08:33:13,251 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-2025/125 - A.P. (DIR Series) Circular No. 22 with text: 8000 chars
2025-08-04 08:33:15,969 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-2025/125 - A.P. (DIR Series) Circular No. 22 (ID: 5b14289e-75e9-52cc-962a-e43f1f24d47e)
2025-08-04 08:33:15,969 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-2025/125 - A.P. (DIR Series) Circular No. 22 (ID: 5b14289e-75e9-52cc-962a-e43f1f24d47e) to collections: ['rbi_other']", "timestamp": "2025-08-04T03:03:15.969235", "document_id": "RBI/2024-2025/125 - A.P. (DIR Series) Circular No. 22", "uuid": "5b14289e-75e9-52cc-962a-e43f1f24d47e", "collection": "rbi_other"}
2025-08-04 08:33:15,969 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:33:15,969 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:33:15,970 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:33:20,141 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:33:20,141 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-04 08:33:20,141 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-04 08:33:20,142 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:33:20,142 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:33:20,207 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:33:20,207 - knowledge_base_executor - INFO - [QDRANT] Match: ID=48922c96-9664-4d04-95cd-e792174707cb, Score=1.0000, Document=A. P. (DIR Series) Circular No. 22
2025-08-04 08:33:20,207 - knowledge_base_executor - INFO - [QDRANT] Match: ID=77444bc4-e441-4ce2-8462-e2b203c3664f, Score=1.0000, Document=A.P.(DIR Series) Circular No.22
2025-08-04 08:33:20,207 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3d553a41-a7d4-4e36-9e15-6f19f801a036, Score=1.0000, Document=A.P.(DIR Series) Circular No.22
2025-08-04 08:33:20,207 - knowledge_base_executor - INFO - [QDRANT] Match: ID=75804c21-e0bd-4da1-b11b-eb6e859c090d, Score=1.0000, Document=A. P. (DIR Series) Circular No. 22
2025-08-04 08:33:20,207 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2fd3512c-181b-4ccf-a258-c0bbf378407a, Score=0.9263, Document=A.P. (DIR Series) Circular No.24
2025-08-04 08:33:20,207 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:33:20,208 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:33:20,208 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'A.P. (DIR Series) Circular No. 22' → 'A.P.(DIRSeries)CircularNo.22'
2025-08-04 08:33:20,208 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: A.P.(DIRSeries)CircularNo.22
2025-08-04 08:33:20,208 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['48922c96-9664-4d04-95cd-e792174707cb', '77444bc4-e441-4ce2-8462-e2b203c3664f', '3d553a41-a7d4-4e36-9e15-6f19f801a036', '75804c21-e0bd-4da1-b11b-eb6e859c090d', '2fd3512c-181b-4ccf-a258-c0bbf378407a']}
2025-08-04 08:33:20,208 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:33:20,208 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 48922c96-9664-4d04-95cd-e792174707cb
2025-08-04 08:33:20,208 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 77444bc4-e441-4ce2-8462-e2b203c3664f
2025-08-04 08:33:20,208 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d553a41-a7d4-4e36-9e15-6f19f801a036
2025-08-04 08:33:20,208 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['48922c96-9664-4d04-95cd-e792174707cb', '77444bc4-e441-4ce2-8462-e2b203c3664f', '3d553a41-a7d4-4e36-9e15-6f19f801a036']...
2025-08-04 08:33:20,208 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:33:20,208 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['48922c96-9664-4d04-95cd-e792174707cb', '77444bc4-e441-4ce2-8462-e2b203c3664f', '3d553a41-a7d4-4e36-9e15-6f19f801a036', '75804c21-e0bd-4da1-b11b-eb6e859c090d', '2fd3512c-181b-4ccf-a258-c0bbf378407a'])] must_not=None, Payload={}
2025-08-04 08:33:20,211 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:33:20,211 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:33:20,211 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:33:20,211 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:33:20,212 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['48922c96-9664-4d04-95cd-e792174707cb', '77444bc4-e441-4ce2-8462-e2b203c3664f', '3d553a41-a7d4-4e36-9e15-6f19f801a036', '75804c21-e0bd-4da1-b11b-eb6e859c090d', '2fd3512c-181b-4ccf-a258-c0bbf378407a']}
2025-08-04 08:33:20,212 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:33:20,212 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 48922c96-9664-4d04-95cd-e792174707cb
2025-08-04 08:33:20,212 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 77444bc4-e441-4ce2-8462-e2b203c3664f
2025-08-04 08:33:20,212 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d553a41-a7d4-4e36-9e15-6f19f801a036
2025-08-04 08:33:20,212 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['48922c96-9664-4d04-95cd-e792174707cb', '77444bc4-e441-4ce2-8462-e2b203c3664f', '3d553a41-a7d4-4e36-9e15-6f19f801a036']...
2025-08-04 08:33:20,212 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:33:20,212 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['48922c96-9664-4d04-95cd-e792174707cb', '77444bc4-e441-4ce2-8462-e2b203c3664f', '3d553a41-a7d4-4e36-9e15-6f19f801a036', '75804c21-e0bd-4da1-b11b-eb6e859c090d', '2fd3512c-181b-4ccf-a258-c0bbf378407a'])] must_not=None, Payload={}
2025-08-04 08:33:20,214 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:33:20,214 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:33:20,214 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:33:20,214 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:33:20,214 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['48922c96-9664-4d04-95cd-e792174707cb', '77444bc4-e441-4ce2-8462-e2b203c3664f', '3d553a41-a7d4-4e36-9e15-6f19f801a036', '75804c21-e0bd-4da1-b11b-eb6e859c090d', '2fd3512c-181b-4ccf-a258-c0bbf378407a']}
2025-08-04 08:33:20,214 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:33:20,214 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 48922c96-9664-4d04-95cd-e792174707cb
2025-08-04 08:33:20,214 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 77444bc4-e441-4ce2-8462-e2b203c3664f
2025-08-04 08:33:20,214 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d553a41-a7d4-4e36-9e15-6f19f801a036
2025-08-04 08:33:20,214 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['48922c96-9664-4d04-95cd-e792174707cb', '77444bc4-e441-4ce2-8462-e2b203c3664f', '3d553a41-a7d4-4e36-9e15-6f19f801a036']...
2025-08-04 08:33:20,214 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:33:20,214 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['48922c96-9664-4d04-95cd-e792174707cb', '77444bc4-e441-4ce2-8462-e2b203c3664f', '3d553a41-a7d4-4e36-9e15-6f19f801a036', '75804c21-e0bd-4da1-b11b-eb6e859c090d', '2fd3512c-181b-4ccf-a258-c0bbf378407a'])] must_not=None, Payload={}
2025-08-04 08:33:20,232 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:33:20,254 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13070 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:33:20,254 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:33:20,254 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T03:03:20.254180"}
2025-08-04 08:33:20,254 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:33:20,254 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:33:20,255 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:33:20,255 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:33:20,255 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-04 08:33:20,255 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Asian Clearing Union _ACU_ Mechanism _ Indo-Maldives trade.pdf
2025-08-04 08:33:20,255 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:33:20,255 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=4bc6c3c8-ea85-449c-92cb-232638903aaa, Document=None, Chunk=0
2025-08-04 08:33:20,255 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 5089 chars
2025-08-04 08:33:24,201 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 4bc6c3c8-ea85-449c-92cb-232638903aaa)
2025-08-04 08:33:24,201 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 4bc6c3c8-ea85-449c-92cb-232638903aaa) to collections: ['rbi_circular']", "timestamp": "2025-08-04T03:03:24.201633", "document_id": null, "uuid": "4bc6c3c8-ea85-449c-92cb-232638903aaa", "collection": "rbi_circular"}
2025-08-04 08:33:24,201 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:33:24,201 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:33:24,202 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:33:32,187 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:33:32,188 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:33:32,188 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:33:32,188 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:33:32,188 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=8330f7e6-a507-54a1-a1ef-39220d4988ab, Document=RBI/2024-25/127 - ACC.REC.No.67/21.04.018/2024-25, Chunk=None
2025-08-04 08:33:32,188 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/127 - ACC.REC.No.67/21.04.018/2024-25 with text: 8000 chars
2025-08-04 08:33:36,102 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/127 - ACC.REC.No.67/21.04.018/2024-25 (ID: 8330f7e6-a507-54a1-a1ef-39220d4988ab)
2025-08-04 08:33:36,102 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/127 - ACC.REC.No.67/21.04.018/2024-25 (ID: 8330f7e6-a507-54a1-a1ef-39220d4988ab) to collections: ['rbi_other']", "timestamp": "2025-08-04T03:03:36.102734", "document_id": "RBI/2024-25/127 - ACC.REC.No.67/21.04.018/2024-25", "uuid": "8330f7e6-a507-54a1-a1ef-39220d4988ab", "collection": "rbi_other"}
2025-08-04 08:33:36,102 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:33:36,102 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:33:36,103 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:33:40,076 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:33:40,077 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:33:40,077 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:33:40,077 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:33:40,077 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:33:40,292 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:33:40,292 - knowledge_base_executor - INFO - [QDRANT] Match: ID=d675122a-f2f8-41f1-8672-a9afc3650b80, Score=0.9491, Document=RBI/2023-24/21
2025-08-04 08:33:40,292 - knowledge_base_executor - INFO - [QDRANT] Match: ID=a4084c05-a76e-45a7-91e8-e4f9c9f1cb65, Score=0.9471, Document=RBI/2023-24/81
2025-08-04 08:33:40,292 - knowledge_base_executor - INFO - [QDRANT] Match: ID=8953ae04-2722-42bc-b33e-e7b31633fdca, Score=0.9432, Document=RBI/2023-24/93
2025-08-04 08:33:40,292 - knowledge_base_executor - INFO - [QDRANT] Match: ID=a6de671a-3e3b-43be-84d8-9a293bbf9a0b, Score=0.9374, Document=RBI/2022-23/44
2025-08-04 08:33:40,292 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b81ebcc1-579d-4b90-ad12-3184e517f69f, Score=0.9369, Document=RBI/2025-26/33
2025-08-04 08:33:40,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:33:40,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:33:40,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: RBI/2023-24/456
2025-08-04 08:33:40,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', 'b81ebcc1-579d-4b90-ad12-3184e517f69f']}
2025-08-04 08:33:40,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:33:40,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = d675122a-f2f8-41f1-8672-a9afc3650b80
2025-08-04 08:33:40,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a4084c05-a76e-45a7-91e8-e4f9c9f1cb65
2025-08-04 08:33:40,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8953ae04-2722-42bc-b33e-e7b31633fdca
2025-08-04 08:33:40,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca']...
2025-08-04 08:33:40,292 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:33:40,293 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', 'b81ebcc1-579d-4b90-ad12-3184e517f69f'])] must_not=None, Payload={}
2025-08-04 08:33:40,296 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:33:40,296 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:33:40,296 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:33:40,296 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:33:40,296 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', 'b81ebcc1-579d-4b90-ad12-3184e517f69f']}
2025-08-04 08:33:40,296 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:33:40,296 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = d675122a-f2f8-41f1-8672-a9afc3650b80
2025-08-04 08:33:40,296 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a4084c05-a76e-45a7-91e8-e4f9c9f1cb65
2025-08-04 08:33:40,296 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8953ae04-2722-42bc-b33e-e7b31633fdca
2025-08-04 08:33:40,296 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca']...
2025-08-04 08:33:40,296 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:33:40,297 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', 'b81ebcc1-579d-4b90-ad12-3184e517f69f'])] must_not=None, Payload={}
2025-08-04 08:33:40,299 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:33:40,299 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:33:40,299 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:33:40,299 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:33:40,300 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', 'b81ebcc1-579d-4b90-ad12-3184e517f69f']}
2025-08-04 08:33:40,300 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:33:40,300 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = d675122a-f2f8-41f1-8672-a9afc3650b80
2025-08-04 08:33:40,300 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a4084c05-a76e-45a7-91e8-e4f9c9f1cb65
2025-08-04 08:33:40,300 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 8953ae04-2722-42bc-b33e-e7b31633fdca
2025-08-04 08:33:40,300 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca']...
2025-08-04 08:33:40,300 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:33:40,300 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['d675122a-f2f8-41f1-8672-a9afc3650b80', 'a4084c05-a76e-45a7-91e8-e4f9c9f1cb65', '8953ae04-2722-42bc-b33e-e7b31633fdca', 'a6de671a-3e3b-43be-84d8-9a293bbf9a0b', 'b81ebcc1-579d-4b90-ad12-3184e517f69f'])] must_not=None, Payload={}
2025-08-04 08:33:40,312 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 4 matching docs in rbi_circular
2025-08-04 08:33:40,335 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13072 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:33:40,335 - knowledge_base_executor - INFO - ✅ Successfully updated 4 documents in collection: rbi_circular
2025-08-04 08:33:40,335 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T03:03:40.335795"}
2025-08-04 08:33:40,335 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:33:40,335 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:33:40,343 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:33:40,351 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:33:40,351 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:33:40,351 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:33:40,351 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:33:40,351 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=9a475f4f-5339-4201-a1e9-c0adc9788f75, Document=None, Chunk=0
2025-08-04 08:33:40,351 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 99 chars
2025-08-04 08:33:42,473 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 9a475f4f-5339-4201-a1e9-c0adc9788f75)
2025-08-04 08:33:42,473 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 9a475f4f-5339-4201-a1e9-c0adc9788f75) to collections: ['rbi_circular']", "timestamp": "2025-08-04T03:03:42.473499", "document_id": null, "uuid": "9a475f4f-5339-4201-a1e9-c0adc9788f75", "collection": "rbi_circular"}
2025-08-04 08:33:42,473 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:33:42,473 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:33:42,474 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:51:07,102 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:51:07,102 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:51:07,102 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:51:07,102 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:51:07,102 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ce48e84b-2a10-41b8-81d4-f481a0678441, Score=0.5963, Document=RPCD.CO No.RRB.BC.104/03.05.34/2006-07
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] Match: ID=339cc64b-8da8-42a9-9e61-d377340f5b9e, Score=0.5902, Document=RPCD.CO RRB.BC No. 71/03.05.33/2011-12
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] Match: ID=c0f520e1-10bf-4761-9366-1d0439a55e15, Score=0.5902, Document=RPCD.CO RRB.BC No. 71/03.05.33/2011-12
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] Match: ID=7b74ed9b-3c97-417f-bdc5-8b1af466308c, Score=0.5873, Document=RPCD.CO.RRB.BC.No.115/03.05.33/2008-09
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3169c1f4-9bbd-4c35-8ce0-3cf4896395c7, Score=0.5873, Document=RPCD.CO.RRB.BC.No.115/03.05.33/2008-09
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'NABARD circular NB.DoS.Pol.HO/2533/J-1/2019-20' → 'NABARDcircularNB.DoS.Pol.HO/2533/J-1/2019-20'
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: NABARDcircularNB.DoS.Pol.HO/2533/J-1/2019-20
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15', '7b74ed9b-3c97-417f-bdc5-8b1af466308c', '3169c1f4-9bbd-4c35-8ce0-3cf4896395c7']}
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ce48e84b-2a10-41b8-81d4-f481a0678441
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 339cc64b-8da8-42a9-9e61-d377340f5b9e
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c0f520e1-10bf-4761-9366-1d0439a55e15
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15']...
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:51:07,306 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15', '7b74ed9b-3c97-417f-bdc5-8b1af466308c', '3169c1f4-9bbd-4c35-8ce0-3cf4896395c7'])] must_not=None, Payload={}
2025-08-04 08:51:07,310 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:51:07,310 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:51:07,310 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:51:07,310 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:51:07,310 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15', '7b74ed9b-3c97-417f-bdc5-8b1af466308c', '3169c1f4-9bbd-4c35-8ce0-3cf4896395c7']}
2025-08-04 08:51:07,310 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:51:07,310 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ce48e84b-2a10-41b8-81d4-f481a0678441
2025-08-04 08:51:07,310 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 339cc64b-8da8-42a9-9e61-d377340f5b9e
2025-08-04 08:51:07,310 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c0f520e1-10bf-4761-9366-1d0439a55e15
2025-08-04 08:51:07,310 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15']...
2025-08-04 08:51:07,310 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:51:07,310 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15', '7b74ed9b-3c97-417f-bdc5-8b1af466308c', '3169c1f4-9bbd-4c35-8ce0-3cf4896395c7'])] must_not=None, Payload={}
2025-08-04 08:51:07,313 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:51:07,313 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:51:07,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:51:07,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:51:07,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15', '7b74ed9b-3c97-417f-bdc5-8b1af466308c', '3169c1f4-9bbd-4c35-8ce0-3cf4896395c7']}
2025-08-04 08:51:07,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:51:07,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ce48e84b-2a10-41b8-81d4-f481a0678441
2025-08-04 08:51:07,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 339cc64b-8da8-42a9-9e61-d377340f5b9e
2025-08-04 08:51:07,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = c0f520e1-10bf-4761-9366-1d0439a55e15
2025-08-04 08:51:07,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15']...
2025-08-04 08:51:07,313 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:51:07,313 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['ce48e84b-2a10-41b8-81d4-f481a0678441', '339cc64b-8da8-42a9-9e61-d377340f5b9e', 'c0f520e1-10bf-4761-9366-1d0439a55e15', '7b74ed9b-3c97-417f-bdc5-8b1af466308c', '3169c1f4-9bbd-4c35-8ce0-3cf4896395c7'])] must_not=None, Payload={}
2025-08-04 08:51:07,331 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:51:07,381 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13074 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:51:07,381 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:51:07,381 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T03:21:07.381236"}
2025-08-04 08:51:07,381 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:51:07,381 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:51:07,386 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:51:07,387 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:51:07,387 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:51:07,387 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:51:07,387 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:51:07,387 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:51:07,435 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:51:07,435 - knowledge_base_executor - INFO - [QDRANT] Match: ID=a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e, Score=0.5672, Document=RBI/2017-18/203  FIDD.CO.Plan.BC.22/04.09.01/2017-18
2025-08-04 08:51:07,435 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ce1d93f5-7444-4a0c-b4e4-c8b3057c085d, Score=0.5672, Document=RBI/2017-18/203 FIDD.CO.Plan.BC.22/04.09.01/2017-18
2025-08-04 08:51:07,435 - knowledge_base_executor - INFO - [QDRANT] Match: ID=759a4091-ddd1-4f1d-8c67-d0fe16e111db, Score=0.5672, Document=RBI/2017-18/203 FIDD.CO.Plan.BC.22/04.09.01/2017-18
2025-08-04 08:51:07,435 - knowledge_base_executor - INFO - [QDRANT] Match: ID=8261f81f-e857-46c4-b9c6-0e3ddca0bc68, Score=0.5672, Document=RBI/2017-18/203  FIDD.CO.Plan.BC.22/04.09.01/2017-18
2025-08-04 08:51:07,435 - knowledge_base_executor - INFO - [QDRANT] Match: ID=09e5bde4-abd1-49fa-a1fe-36bc3475f196, Score=0.5534, Document=RBI/2007-2008/159 RPCD.CO.Plan.BC.  30 /04.09.01/2007-08
2025-08-04 08:51:07,436 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:51:07,436 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:51:07,436 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'Reserve Bank of India (Financial Statements - Presentation and Disclosures) Directions, 2021' → 'ReserveBankofIndia(FinancialStatements-PresentationandDisclosures)Directions,2021'
2025-08-04 08:51:07,436 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: ReserveBankofIndia(FinancialStatements-PresentationandDisclosures)Directions,2021
2025-08-04 08:51:07,436 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', '09e5bde4-abd1-49fa-a1fe-36bc3475f196']}
2025-08-04 08:51:07,436 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:51:07,436 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e
2025-08-04 08:51:07,436 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ce1d93f5-7444-4a0c-b4e4-c8b3057c085d
2025-08-04 08:51:07,436 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 759a4091-ddd1-4f1d-8c67-d0fe16e111db
2025-08-04 08:51:07,436 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db']...
2025-08-04 08:51:07,436 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:51:07,436 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', '09e5bde4-abd1-49fa-a1fe-36bc3475f196'])] must_not=None, Payload={}
2025-08-04 08:51:07,438 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:51:07,438 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:51:07,438 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:51:07,439 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:51:07,439 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', '09e5bde4-abd1-49fa-a1fe-36bc3475f196']}
2025-08-04 08:51:07,439 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:51:07,439 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e
2025-08-04 08:51:07,439 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ce1d93f5-7444-4a0c-b4e4-c8b3057c085d
2025-08-04 08:51:07,439 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 759a4091-ddd1-4f1d-8c67-d0fe16e111db
2025-08-04 08:51:07,439 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db']...
2025-08-04 08:51:07,439 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:51:07,439 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', '09e5bde4-abd1-49fa-a1fe-36bc3475f196'])] must_not=None, Payload={}
2025-08-04 08:51:07,441 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:51:07,441 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:51:07,441 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:51:07,441 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:51:07,441 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', '09e5bde4-abd1-49fa-a1fe-36bc3475f196']}
2025-08-04 08:51:07,441 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:51:07,441 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e
2025-08-04 08:51:07,441 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ce1d93f5-7444-4a0c-b4e4-c8b3057c085d
2025-08-04 08:51:07,441 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 759a4091-ddd1-4f1d-8c67-d0fe16e111db
2025-08-04 08:51:07,441 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db']...
2025-08-04 08:51:07,441 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:51:07,441 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['a9edacfd-e2bb-4fe3-84ac-bc0055b3f01e', 'ce1d93f5-7444-4a0c-b4e4-c8b3057c085d', '759a4091-ddd1-4f1d-8c67-d0fe16e111db', '8261f81f-e857-46c4-b9c6-0e3ddca0bc68', '09e5bde4-abd1-49fa-a1fe-36bc3475f196'])] must_not=None, Payload={}
2025-08-04 08:51:07,450 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:51:07,465 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13075 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:51:07,465 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:51:07,465 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T03:21:07.465205"}
2025-08-04 08:51:07,465 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:51:07,465 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:51:07,465 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:51:07,466 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:51:07,466 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:51:07,466 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Amortisation of additional pension liability - Implementation of Pension Scheme in Regional Rural Banks with effect from November 1_ 1993 - Prudential Regulatory Treatment.pdf
2025-08-04 08:51:07,466 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:51:07,466 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=57650b75-24ff-4a90-a6c0-92a887a4d06e, Document=None, Chunk=1
2025-08-04 08:51:07,466 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-04 08:51:11,658 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 57650b75-24ff-4a90-a6c0-92a887a4d06e)
2025-08-04 08:51:11,658 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 57650b75-24ff-4a90-a6c0-92a887a4d06e) to collections: ['rbi_circular']", "timestamp": "2025-08-04T03:21:11.658744", "document_id": null, "uuid": "57650b75-24ff-4a90-a6c0-92a887a4d06e", "collection": "rbi_circular"}
2025-08-04 08:51:11,658 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:51:11,658 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:51:11,659 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:51:19,304 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:51:19,304 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-04 08:51:19,304 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-04 08:51:19,304 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:51:19,304 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=fd7d5df2-8d4b-51ed-a469-c57915906777, Document=RBI/2024-25/124, Chunk=None
2025-08-04 08:51:19,304 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/124 with text: 8000 chars
2025-08-04 08:51:23,955 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/124 (ID: fd7d5df2-8d4b-51ed-a469-c57915906777)
2025-08-04 08:51:23,955 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/124 (ID: fd7d5df2-8d4b-51ed-a469-c57915906777) to collections: ['rbi_other']", "timestamp": "2025-08-04T03:21:23.955816", "document_id": "RBI/2024-25/124", "uuid": "fd7d5df2-8d4b-51ed-a469-c57915906777", "collection": "rbi_other"}
2025-08-04 08:51:23,955 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:51:23,955 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:51:23,956 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:51:29,022 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:51:29,022 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-04 08:51:29,022 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-04 08:51:29,022 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:51:29,022 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=1c096004-445d-5956-92e7-516e3fce6c5c, Document=RBI/2024-25/124, Chunk=0
2025-08-04 08:51:29,022 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/124 with text: 4506 chars
2025-08-04 08:51:31,985 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/124 (ID: 1c096004-445d-5956-92e7-516e3fce6c5c)
2025-08-04 08:51:31,985 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/124 (ID: 1c096004-445d-5956-92e7-516e3fce6c5c) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T03:21:31.985574", "document_id": "RBI/2024-25/124", "uuid": "1c096004-445d-5956-92e7-516e3fce6c5c", "collection": "rbi_master_direction"}
2025-08-04 08:51:31,985 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:51:31,985 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:51:31,986 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:51:31,986 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:51:31,986 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-04 08:51:31,986 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Annual Closing of Government Accounts _ Transactions of Central _ State Governments _ Special Measures for the Current Financial Year _2024-25_.pdf
2025-08-04 08:51:31,986 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:51:31,986 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=78aac02f-f215-418f-b79c-23ab8b9a7cc7, Document=None, Chunk=0
2025-08-04 08:51:31,986 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 4397 chars
2025-08-04 08:51:36,442 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 78aac02f-f215-418f-b79c-23ab8b9a7cc7)
2025-08-04 08:51:36,442 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 78aac02f-f215-418f-b79c-23ab8b9a7cc7) to collections: ['rbi_circular']", "timestamp": "2025-08-04T03:21:36.442273", "document_id": null, "uuid": "78aac02f-f215-418f-b79c-23ab8b9a7cc7", "collection": "rbi_circular"}
2025-08-04 08:51:36,442 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:51:36,442 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:51:36,443 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:51:44,176 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:51:44,176 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-04 08:51:44,176 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-04 08:51:44,177 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:51:44,177 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=51663fb0-3b59-521e-9d3e-17a2c381a8ed, Document=RBI/2025-26/28, Chunk=None
2025-08-04 08:51:44,177 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/28 with text: 8000 chars
2025-08-04 08:51:48,021 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/28 (ID: 51663fb0-3b59-521e-9d3e-17a2c381a8ed)
2025-08-04 08:51:48,021 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/28 (ID: 51663fb0-3b59-521e-9d3e-17a2c381a8ed) to collections: ['rbi_circular']", "timestamp": "2025-08-04T03:21:48.021623", "document_id": "RBI/2025-26/28", "uuid": "51663fb0-3b59-521e-9d3e-17a2c381a8ed", "collection": "rbi_circular"}
2025-08-04 08:51:48,021 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:51:48,021 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:51:48,022 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:51:53,649 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:51:53,650 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-04 08:51:53,650 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-04 08:51:53,650 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:51:53,650 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_direction', 'rbi_master_circular', 'rbi_circular'], PID=f21b1660-1343-5f86-96d1-a10762525df5, Document=RBI/2025-26/28, Chunk=0
2025-08-04 08:51:53,650 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/28 with text: 4737 chars
2025-08-04 08:51:58,499 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/28 (ID: f21b1660-1343-5f86-96d1-a10762525df5)
2025-08-04 08:51:58,499 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/28 (ID: f21b1660-1343-5f86-96d1-a10762525df5) to collections: ['rbi_master_direction', 'rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T03:21:58.499556", "document_id": "RBI/2025-26/28", "uuid": "f21b1660-1343-5f86-96d1-a10762525df5", "collection": "rbi_master_direction"}
2025-08-04 08:51:58,499 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:51:58,499 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:51:58,500 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:51:58,500 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:51:58,500 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-04 08:51:58,500 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Circular - Migration to _.bank.in_ domain.pdf
2025-08-04 08:51:58,500 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:51:58,500 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=57d234da-12f1-4b82-aafc-680f7e34a0db, Document=None, Chunk=0
2025-08-04 08:51:58,500 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 4621 chars
2025-08-04 08:52:02,885 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 57d234da-12f1-4b82-aafc-680f7e34a0db)
2025-08-04 08:52:02,886 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 57d234da-12f1-4b82-aafc-680f7e34a0db) to collections: ['rbi_circular']", "timestamp": "2025-08-04T03:22:02.886332", "document_id": null, "uuid": "57d234da-12f1-4b82-aafc-680f7e34a0db", "collection": "rbi_circular"}
2025-08-04 08:52:02,886 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:52:02,886 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:52:02,887 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:52:09,833 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:52:09,834 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-04 08:52:09,834 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-04 08:52:09,834 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:52:09,834 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=0a47fcb7-bf25-5bfc-bc86-64022ee4c4a6, Document=RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25, Chunk=None
2025-08-04 08:52:09,834 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 with text: 8000 chars
2025-08-04 08:52:14,130 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 (ID: 0a47fcb7-bf25-5bfc-bc86-64022ee4c4a6)
2025-08-04 08:52:14,130 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25 (ID: 0a47fcb7-bf25-5bfc-bc86-64022ee4c4a6) to collections: ['rbi_other']", "timestamp": "2025-08-04T03:22:14.130880", "document_id": "RBI/2024-25/111 - DoR.RET.REC.57/12.01.001/2024-25", "uuid": "0a47fcb7-bf25-5bfc-bc86-64022ee4c4a6", "collection": "rbi_other"}
2025-08-04 08:52:14,130 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:52:14,130 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:52:14,131 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:52:19,636 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:52:19,636 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-04 08:52:19,636 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-04 08:52:19,636 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:52:19,636 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:52:19,682 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:52:19,682 - knowledge_base_executor - INFO - [QDRANT] Match: ID=45e1d982-dbae-4bda-9493-52ab74735756, Score=0.9639, Document=DoR.RET.REC.12/12.01.001/2024-25
2025-08-04 08:52:19,682 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2b03a8d3-091a-479e-a7bd-db44ec4f4400, Score=0.8551, Document=DOR.CRE.REC.No.06/08.12.001/2023-24
2025-08-04 08:52:19,682 - knowledge_base_executor - INFO - [QDRANT] Match: ID=78e63424-31da-4ff2-a793-3897c8a0ce69, Score=0.8551, Document=DOR.CRE.REC.No.06/08.12.001/2023-24
2025-08-04 08:52:19,682 - knowledge_base_executor - INFO - [QDRANT] Match: ID=8076eaa7-c954-4306-962c-9ee9d870752d, Score=0.8551, Document=DOR.CRE.REC.No.06/08.12.001/2023-24
2025-08-04 08:52:19,682 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3814cb01-95e2-465c-aef2-6ae7ebedce7b, Score=0.8551, Document=DOR.CRE.REC.No.06/08.12.001/2023-24
2025-08-04 08:52:19,682 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:52:19,683 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:52:19,683 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DOR.RET.REC.101/12.01.001/2022-23
2025-08-04 08:52:19,683 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69', '8076eaa7-c954-4306-962c-9ee9d870752d', '3814cb01-95e2-465c-aef2-6ae7ebedce7b']}
2025-08-04 08:52:19,683 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:52:19,683 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 45e1d982-dbae-4bda-9493-52ab74735756
2025-08-04 08:52:19,683 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2b03a8d3-091a-479e-a7bd-db44ec4f4400
2025-08-04 08:52:19,683 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 78e63424-31da-4ff2-a793-3897c8a0ce69
2025-08-04 08:52:19,683 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69']...
2025-08-04 08:52:19,683 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:52:19,683 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69', '8076eaa7-c954-4306-962c-9ee9d870752d', '3814cb01-95e2-465c-aef2-6ae7ebedce7b'])] must_not=None, Payload={}
2025-08-04 08:52:19,687 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:52:19,687 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:52:19,687 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:52:19,687 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:52:19,687 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69', '8076eaa7-c954-4306-962c-9ee9d870752d', '3814cb01-95e2-465c-aef2-6ae7ebedce7b']}
2025-08-04 08:52:19,687 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:52:19,687 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 45e1d982-dbae-4bda-9493-52ab74735756
2025-08-04 08:52:19,687 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2b03a8d3-091a-479e-a7bd-db44ec4f4400
2025-08-04 08:52:19,687 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 78e63424-31da-4ff2-a793-3897c8a0ce69
2025-08-04 08:52:19,687 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69']...
2025-08-04 08:52:19,687 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:52:19,687 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69', '8076eaa7-c954-4306-962c-9ee9d870752d', '3814cb01-95e2-465c-aef2-6ae7ebedce7b'])] must_not=None, Payload={}
2025-08-04 08:52:19,702 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 4 matching docs in rbi_master_circular
2025-08-04 08:52:19,726 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8293 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:52:19,726 - knowledge_base_executor - INFO - ✅ Successfully updated 4 documents in collection: rbi_master_circular
2025-08-04 08:52:19,726 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:52:19,726 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:52:19,726 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69', '8076eaa7-c954-4306-962c-9ee9d870752d', '3814cb01-95e2-465c-aef2-6ae7ebedce7b']}
2025-08-04 08:52:19,727 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:52:19,727 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 45e1d982-dbae-4bda-9493-52ab74735756
2025-08-04 08:52:19,727 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2b03a8d3-091a-479e-a7bd-db44ec4f4400
2025-08-04 08:52:19,727 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 78e63424-31da-4ff2-a793-3897c8a0ce69
2025-08-04 08:52:19,727 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69']...
2025-08-04 08:52:19,727 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:52:19,727 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['45e1d982-dbae-4bda-9493-52ab74735756', '2b03a8d3-091a-479e-a7bd-db44ec4f4400', '78e63424-31da-4ff2-a793-3897c8a0ce69', '8076eaa7-c954-4306-962c-9ee9d870752d', '3814cb01-95e2-465c-aef2-6ae7ebedce7b'])] must_not=None, Payload={}
2025-08-04 08:52:19,731 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 1 matching docs in rbi_circular
2025-08-04 08:52:19,737 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13080 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:52:19,737 - knowledge_base_executor - INFO - ✅ Successfully updated 1 documents in collection: rbi_circular
2025-08-04 08:52:19,737 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T03:22:19.737122"}
2025-08-04 08:52:19,737 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:52:19,737 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:52:19,738 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:52:19,739 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:52:19,739 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-04 08:52:19,739 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Change in Bank Rate.pdf
2025-08-04 08:52:19,739 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:52:19,739 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=44c5c09f-897a-4f33-b742-444c270f9752, Document=None, Chunk=0
2025-08-04 08:52:19,739 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 5846 chars
2025-08-04 08:52:22,873 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 44c5c09f-897a-4f33-b742-444c270f9752)
2025-08-04 08:52:22,873 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 44c5c09f-897a-4f33-b742-444c270f9752) to collections: ['rbi_circular']", "timestamp": "2025-08-04T03:22:22.873389", "document_id": null, "uuid": "44c5c09f-897a-4f33-b742-444c270f9752", "collection": "rbi_circular"}
2025-08-04 08:52:22,873 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:52:22,873 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:52:22,874 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:52:28,963 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:52:28,964 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-04 08:52:28,964 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-04 08:52:28,964 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:52:28,965 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=7e6315d9-c886-50f1-aa4f-95cf79e15162, Document=RBI/2024-25/129, Chunk=None
2025-08-04 08:52:28,965 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/129 with text: 8000 chars
2025-08-04 08:52:31,218 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/129 (ID: 7e6315d9-c886-50f1-aa4f-95cf79e15162)
2025-08-04 08:52:31,218 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/129 (ID: 7e6315d9-c886-50f1-aa4f-95cf79e15162) to collections: ['rbi_other']", "timestamp": "2025-08-04T03:22:31.218433", "document_id": "RBI/2024-25/129", "uuid": "7e6315d9-c886-50f1-aa4f-95cf79e15162", "collection": "rbi_other"}
2025-08-04 08:52:31,218 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:52:31,218 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:52:31,219 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:52:35,505 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:52:35,505 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-04 08:52:35,506 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-04 08:52:35,506 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:52:35,506 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:52:35,606 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:52:35,606 - knowledge_base_executor - INFO - [QDRANT] Match: ID=35918048-dd38-4dc2-897c-c169f5aefc30, Score=0.7409, Document=RBI/2024-25/112 DOR.CO.SOG(Leg) No.59/09.08.024/2024-25
2025-08-04 08:52:35,606 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9741a628-999e-4b3f-aa8d-da299fe6f6c1, Score=0.7197, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-04 08:52:35,606 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3d495fa6-6797-461c-bf81-7de0eb59a24a, Score=0.7197, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-04 08:52:35,606 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2d0ef267-a504-424c-b287-4a442abd8753, Score=0.6951, Document=LEG.BC.114/09.06.002/2000-01
2025-08-04 08:52:35,606 - knowledge_base_executor - INFO - [QDRANT] Match: ID=95679ada-2bb1-49a9-a5b7-52db268a310a, Score=0.6893, Document=RBI/2023-24/105  DOR.SOG (LEG).REC/64/09.08.024/2023-24
2025-08-04 08:52:35,606 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:52:35,606 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:52:35,606 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DOR.CO.SOG(Leg)No.59/09.08.024/2024-25
2025-08-04 08:52:35,607 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2d0ef267-a504-424c-b287-4a442abd8753', '95679ada-2bb1-49a9-a5b7-52db268a310a']}
2025-08-04 08:52:35,607 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:52:35,607 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 35918048-dd38-4dc2-897c-c169f5aefc30
2025-08-04 08:52:35,607 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-04 08:52:35,607 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-04 08:52:35,607 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a']...
2025-08-04 08:52:35,607 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:52:35,607 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2d0ef267-a504-424c-b287-4a442abd8753', '95679ada-2bb1-49a9-a5b7-52db268a310a'])] must_not=None, Payload={}
2025-08-04 08:52:35,610 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:52:35,610 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:52:35,610 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:52:35,610 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:52:35,610 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2d0ef267-a504-424c-b287-4a442abd8753', '95679ada-2bb1-49a9-a5b7-52db268a310a']}
2025-08-04 08:52:35,610 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:52:35,610 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 35918048-dd38-4dc2-897c-c169f5aefc30
2025-08-04 08:52:35,610 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-04 08:52:35,610 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-04 08:52:35,610 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a']...
2025-08-04 08:52:35,610 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:52:35,610 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2d0ef267-a504-424c-b287-4a442abd8753', '95679ada-2bb1-49a9-a5b7-52db268a310a'])] must_not=None, Payload={}
2025-08-04 08:52:35,612 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:52:35,612 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:52:35,612 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:52:35,613 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:52:35,613 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2d0ef267-a504-424c-b287-4a442abd8753', '95679ada-2bb1-49a9-a5b7-52db268a310a']}
2025-08-04 08:52:35,613 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:52:35,613 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 35918048-dd38-4dc2-897c-c169f5aefc30
2025-08-04 08:52:35,613 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-04 08:52:35,613 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-04 08:52:35,613 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a']...
2025-08-04 08:52:35,613 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:52:35,613 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['35918048-dd38-4dc2-897c-c169f5aefc30', '9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2d0ef267-a504-424c-b287-4a442abd8753', '95679ada-2bb1-49a9-a5b7-52db268a310a'])] must_not=None, Payload={}
2025-08-04 08:52:35,628 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:52:35,647 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13082 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:52:35,647 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:52:35,647 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T03:22:35.647747"}
2025-08-04 08:52:35,647 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:52:35,647 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:52:35,648 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:52:35,648 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:52:35,648 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-04 08:52:35,648 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Currency Chest operations on March 31_ 2025.pdf
2025-08-04 08:52:35,649 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:52:35,649 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=612a19fa-9505-4a00-a6c3-1d958846104c, Document=None, Chunk=0
2025-08-04 08:52:35,649 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 6049 chars
2025-08-04 08:52:39,227 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 612a19fa-9505-4a00-a6c3-1d958846104c)
2025-08-04 08:52:39,227 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 612a19fa-9505-4a00-a6c3-1d958846104c) to collections: ['rbi_circular']", "timestamp": "2025-08-04T03:22:39.227262", "document_id": null, "uuid": "612a19fa-9505-4a00-a6c3-1d958846104c", "collection": "rbi_circular"}
2025-08-04 08:52:39,227 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:52:39,227 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:52:39,227 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:52:48,016 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:52:48,016 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:52:48,016 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:52:48,017 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:52:48,017 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=fa055446-fc89-549f-9794-e8985213b19b, Document=RBI/2024-25/104 - No.Leg.BC.21/09.07.006/2015-16, Chunk=None
2025-08-04 08:52:48,017 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2024-25/104 - No.Leg.BC.21/09.07.006/2015-16 with text: 8000 chars
2025-08-04 08:52:49,251 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2024-25/104 - No.Leg.BC.21/09.07.006/2015-16 (ID: fa055446-fc89-549f-9794-e8985213b19b)
2025-08-04 08:52:49,251 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2024-25/104 - No.Leg.BC.21/09.07.006/2015-16 (ID: fa055446-fc89-549f-9794-e8985213b19b) to collections: ['rbi_other']", "timestamp": "2025-08-04T03:22:49.251524", "document_id": "RBI/2024-25/104 - No.Leg.BC.21/09.07.006/2015-16", "uuid": "fa055446-fc89-549f-9794-e8985213b19b", "collection": "rbi_other"}
2025-08-04 08:52:49,251 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:52:49,251 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:52:49,252 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:52:59,363 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:52:59,363 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:52:59,363 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:52:59,363 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:52:59,364 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:52:59,428 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:52:59,428 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c, Score=0.9869, Document=DBR.No.Leg.BC.78/09.07.005/2017-18
2025-08-04 08:52:59,428 - knowledge_base_executor - INFO - [QDRANT] Match: ID=cf214b82-7743-4bec-869b-256ad6b1bba9, Score=0.9869, Document=DBR.No.Leg.BC.78/09.07.005/2017-18
2025-08-04 08:52:59,428 - knowledge_base_executor - INFO - [QDRANT] Match: ID=fe594df3-1d72-4ca9-9efd-0b9d0e666759, Score=0.9863, Document=DBR.No.Leg.BC.78/09.07.005/2015-16
2025-08-04 08:52:59,429 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0549d859-1d9f-4ecd-a168-9a178f9c1748, Score=0.8894, Document=DBOD No.Leg.BC. 22 /09.07.006/2013-14
2025-08-04 08:52:59,429 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0a67d3d1-70f1-4f31-8422-f7828240e3b1, Score=0.8894, Document=DBOD No.Leg.BC. 22 /09.07.006/2013-14
2025-08-04 08:52:59,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:52:59,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:52:59,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DBR.No.Leg.BC.21/09.07.006/2015-16
2025-08-04 08:52:59,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '0549d859-1d9f-4ecd-a168-9a178f9c1748', '0a67d3d1-70f1-4f31-8422-f7828240e3b1']}
2025-08-04 08:52:59,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:52:59,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c
2025-08-04 08:52:59,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = cf214b82-7743-4bec-869b-256ad6b1bba9
2025-08-04 08:52:59,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fe594df3-1d72-4ca9-9efd-0b9d0e666759
2025-08-04 08:52:59,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759']...
2025-08-04 08:52:59,429 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:52:59,429 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '0549d859-1d9f-4ecd-a168-9a178f9c1748', '0a67d3d1-70f1-4f31-8422-f7828240e3b1'])] must_not=None, Payload={}
2025-08-04 08:52:59,432 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:52:59,432 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:52:59,432 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:52:59,432 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:52:59,433 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '0549d859-1d9f-4ecd-a168-9a178f9c1748', '0a67d3d1-70f1-4f31-8422-f7828240e3b1']}
2025-08-04 08:52:59,433 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:52:59,433 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c
2025-08-04 08:52:59,433 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = cf214b82-7743-4bec-869b-256ad6b1bba9
2025-08-04 08:52:59,433 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fe594df3-1d72-4ca9-9efd-0b9d0e666759
2025-08-04 08:52:59,433 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759']...
2025-08-04 08:52:59,433 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:52:59,433 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '0549d859-1d9f-4ecd-a168-9a178f9c1748', '0a67d3d1-70f1-4f31-8422-f7828240e3b1'])] must_not=None, Payload={}
2025-08-04 08:52:59,450 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 2 matching docs in rbi_master_circular
2025-08-04 08:52:59,471 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=8294 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:52:59,471 - knowledge_base_executor - INFO - ✅ Successfully updated 2 documents in collection: rbi_master_circular
2025-08-04 08:52:59,471 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:52:59,471 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:52:59,471 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '0549d859-1d9f-4ecd-a168-9a178f9c1748', '0a67d3d1-70f1-4f31-8422-f7828240e3b1']}
2025-08-04 08:52:59,471 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:52:59,471 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c
2025-08-04 08:52:59,471 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = cf214b82-7743-4bec-869b-256ad6b1bba9
2025-08-04 08:52:59,471 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fe594df3-1d72-4ca9-9efd-0b9d0e666759
2025-08-04 08:52:59,471 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759']...
2025-08-04 08:52:59,471 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:52:59,471 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['5f0ce6c1-e17d-44d4-9e8e-d0e8cab86d0c', 'cf214b82-7743-4bec-869b-256ad6b1bba9', 'fe594df3-1d72-4ca9-9efd-0b9d0e666759', '0549d859-1d9f-4ecd-a168-9a178f9c1748', '0a67d3d1-70f1-4f31-8422-f7828240e3b1'])] must_not=None, Payload={}
2025-08-04 08:52:59,480 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 3 matching docs in rbi_circular
2025-08-04 08:52:59,497 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13084 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:52:59,497 - knowledge_base_executor - INFO - ✅ Successfully updated 3 documents in collection: rbi_circular
2025-08-04 08:52:59,497 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_circular', 'rbi_circular']", "timestamp": "2025-08-04T03:22:59.497230"}
2025-08-04 08:52:59,497 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:52:59,497 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:52:59,498 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:52:59,498 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:52:59,498 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:52:59,498 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:52:59,499 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:52:59,499 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] Match: ID=fb5bf018-4e33-4adb-bc1d-704f39e21c39, Score=0.8130, Document=DCBR. CO. BPD.BC. No. 16/16.05.000/2015-16
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] Match: ID=54c5602e-8d0c-4fae-90e4-9686d4e25ec1, Score=0.7138, Document=RPCD.CO.RRB. No. BC. 35 / 03.05.28(B)/2007-08
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] Match: ID=6145d9f4-bcf7-41ef-aa0b-c2557b36b026, Score=0.7087, Document=RPCD.CO.RRB.BC.No.38 /03.05.28(B)/2009-10
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] Match: ID=0cb7fefb-5ba7-4b14-99e6-02c6109d43f5, Score=0.7058, Document=RPCD.CO.RRB. No. BC. 61/03.05.28(B)/2007-08
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] Match: ID=4e513ba5-f8d8-45cc-b25e-4e2378df9c72, Score=0.7053, Document=RPCD.CO. RRB. No. BC. 32 /03.05.33(C) / 2007-08
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DCBR.CO.BPD.(PCB).MC.No.15/12.05.001/2015-16
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72']}
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fb5bf018-4e33-4adb-bc1d-704f39e21c39
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 54c5602e-8d0c-4fae-90e4-9686d4e25ec1
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 6145d9f4-bcf7-41ef-aa0b-c2557b36b026
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026']...
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:52:59,514 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72'])] must_not=None, Payload={}
2025-08-04 08:52:59,516 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_direction
2025-08-04 08:52:59,516 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_direction
2025-08-04 08:52:59,516 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:52:59,516 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:52:59,516 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72']}
2025-08-04 08:52:59,516 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:52:59,517 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fb5bf018-4e33-4adb-bc1d-704f39e21c39
2025-08-04 08:52:59,517 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 54c5602e-8d0c-4fae-90e4-9686d4e25ec1
2025-08-04 08:52:59,517 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 6145d9f4-bcf7-41ef-aa0b-c2557b36b026
2025-08-04 08:52:59,517 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026']...
2025-08-04 08:52:59,517 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:52:59,517 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72'])] must_not=None, Payload={}
2025-08-04 08:52:59,518 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:52:59,519 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:52:59,519 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:52:59,519 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:52:59,519 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72']}
2025-08-04 08:52:59,519 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:52:59,519 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = fb5bf018-4e33-4adb-bc1d-704f39e21c39
2025-08-04 08:52:59,519 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 54c5602e-8d0c-4fae-90e4-9686d4e25ec1
2025-08-04 08:52:59,519 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 6145d9f4-bcf7-41ef-aa0b-c2557b36b026
2025-08-04 08:52:59,519 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026']...
2025-08-04 08:52:59,519 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:52:59,519 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['fb5bf018-4e33-4adb-bc1d-704f39e21c39', '54c5602e-8d0c-4fae-90e4-9686d4e25ec1', '6145d9f4-bcf7-41ef-aa0b-c2557b36b026', '0cb7fefb-5ba7-4b14-99e6-02c6109d43f5', '4e513ba5-f8d8-45cc-b25e-4e2378df9c72'])] must_not=None, Payload={}
2025-08-04 08:52:59,528 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_circular
2025-08-04 08:52:59,544 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=13085 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:52:59,544 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_circular
2025-08-04 08:52:59,545 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_circular']", "timestamp": "2025-08-04T03:22:59.545014"}
2025-08-04 08:52:59,545 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:52:59,545 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:52:59,545 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:52:59,546 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-04 08:52:59,546 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:52:59,546 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:52:59,546 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-04 08:52:59,546 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-04 08:52:59,559 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-04 08:52:59,559 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06da554e-d1df-4798-806d-64095c33026e, Score=0.7672, Document=RBI/DNBR/2016-17/42  Master Direction DNBR.PD.004/03.10.119/2016-17
2025-08-04 08:52:59,560 - knowledge_base_executor - INFO - [QDRANT] Match: ID=06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b, Score=0.7672, Document=RBI/DNBR/2016-17/42  Master Direction DNBR.PD.004/03.10.119/2016-17
2025-08-04 08:52:59,560 - knowledge_base_executor - INFO - [QDRANT] Match: ID=070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92, Score=0.7672, Document=RBI/DNBR/2016-17/42  Master Direction DNBR.PD.004/03.10.119/2016-17
2025-08-04 08:52:59,560 - knowledge_base_executor - INFO - [QDRANT] Match: ID=070d6bfc-e68f-478b-b770-e41eb204f88e, Score=0.7672, Document=RBI/DNBR/2016-17/42 Master Direction DNBR.PD.004/03.10.119/2016-17
2025-08-04 08:52:59,560 - knowledge_base_executor - INFO - [QDRANT] Match: ID=07158b66-777a-4efa-84bc-b4f4d3e3b7ab, Score=0.7672, Document=RBI/DNBR/2016-17/42  Master Direction DNBR.PD.004/03.10.119/2016-17
2025-08-04 08:52:59,560 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:52:59,560 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-04 08:52:59,560 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DNBR.PD.002/03.10.119/2016-17
2025-08-04 08:52:59,560 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92', '070d6bfc-e68f-478b-b770-e41eb204f88e', '07158b66-777a-4efa-84bc-b4f4d3e3b7ab']}
2025-08-04 08:52:59,560 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:52:59,560 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06da554e-d1df-4798-806d-64095c33026e
2025-08-04 08:52:59,560 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b
2025-08-04 08:52:59,560 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92
2025-08-04 08:52:59,560 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92']...
2025-08-04 08:52:59,560 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:52:59,560 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_direction, Filter=should=None min_should=None must=[HasIdCondition(has_id=['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92', '070d6bfc-e68f-478b-b770-e41eb204f88e', '07158b66-777a-4efa-84bc-b4f4d3e3b7ab'])] must_not=None, Payload={}
2025-08-04 08:52:59,568 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 5 matching docs in rbi_master_direction
2025-08-04 08:52:59,593 - knowledge_base_executor - INFO - [QDRANT] UPDATE response: operation_id=3208 status=<UpdateStatus.COMPLETED: 'completed'>
2025-08-04 08:52:59,593 - knowledge_base_executor - INFO - ✅ Successfully updated 5 documents in collection: rbi_master_direction
2025-08-04 08:52:59,593 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:52:59,593 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:52:59,593 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92', '070d6bfc-e68f-478b-b770-e41eb204f88e', '07158b66-777a-4efa-84bc-b4f4d3e3b7ab']}
2025-08-04 08:52:59,594 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:52:59,594 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06da554e-d1df-4798-806d-64095c33026e
2025-08-04 08:52:59,594 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b
2025-08-04 08:52:59,594 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92
2025-08-04 08:52:59,594 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92']...
2025-08-04 08:52:59,594 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:52:59,594 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_master_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92', '070d6bfc-e68f-478b-b770-e41eb204f88e', '07158b66-777a-4efa-84bc-b4f4d3e3b7ab'])] must_not=None, Payload={}
2025-08-04 08:52:59,595 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_master_circular
2025-08-04 08:52:59,595 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_master_circular
2025-08-04 08:52:59,596 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-04 08:52:59,596 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-04 08:52:59,596 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92', '070d6bfc-e68f-478b-b770-e41eb204f88e', '07158b66-777a-4efa-84bc-b4f4d3e3b7ab']}
2025-08-04 08:52:59,596 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-04 08:52:59,596 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06da554e-d1df-4798-806d-64095c33026e
2025-08-04 08:52:59,596 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b
2025-08-04 08:52:59,596 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92
2025-08-04 08:52:59,596 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92']...
2025-08-04 08:52:59,596 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Creating filter with HasIdCondition for 5 point IDs
2025-08-04 08:52:59,596 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Collection=rbi_circular, Filter=should=None min_should=None must=[HasIdCondition(has_id=['06da554e-d1df-4798-806d-64095c33026e', '06f6a0a3-baaa-4bcb-b714-cbd1d1e2ab7b', '070ad95e-da0b-4e43-bf8b-bf6dbfe1ff92', '070d6bfc-e68f-478b-b770-e41eb204f88e', '07158b66-777a-4efa-84bc-b4f4d3e3b7ab'])] must_not=None, Payload={}
2025-08-04 08:52:59,597 - knowledge_base_executor - INFO - [QDRANT] UPDATE: Found 0 matching docs in rbi_circular
2025-08-04 08:52:59,597 - knowledge_base_executor - WARNING - [QDRANT] UPDATE: No documents found matching filter in collection: rbi_circular
2025-08-04 08:52:59,597 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": true, "details": "Updated documents in collections: ['rbi_master_direction']", "timestamp": "2025-08-04T03:22:59.597843"}
2025-08-04 08:52:59,597 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:52:59,598 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ✅ SUCCESS
2025-08-04 08:52:59,598 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:52:59,599 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:52:59,599 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:52:59,599 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:52:59,599 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:52:59,599 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=49d8d822-1d1b-4759-811a-9712b10fa2f0, Document=None, Chunk=0
2025-08-04 08:52:59,599 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 7884 chars
2025-08-04 08:53:04,032 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 49d8d822-1d1b-4759-811a-9712b10fa2f0)
2025-08-04 08:53:04,032 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 49d8d822-1d1b-4759-811a-9712b10fa2f0) to collections: ['rbi_circular']", "timestamp": "2025-08-04T03:23:04.032630", "document_id": null, "uuid": "49d8d822-1d1b-4759-811a-9712b10fa2f0", "collection": "rbi_circular"}
2025-08-04 08:53:04,032 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:53:04,032 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:53:04,033 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:53:07,187 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:53:07,188 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:53:07,188 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Coverage of customers under the nomination facility.pdf
2025-08-04 08:53:07,188 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:53:07,188 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=5edf7d2f-c551-4b93-a64c-5cb73792f95c, Document=None, Chunk=1
2025-08-04 08:53:07,188 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 259 chars
2025-08-04 08:53:10,304 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 5edf7d2f-c551-4b93-a64c-5cb73792f95c)
2025-08-04 08:53:10,304 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 5edf7d2f-c551-4b93-a64c-5cb73792f95c) to collections: ['rbi_circular']", "timestamp": "2025-08-04T03:23:10.304471", "document_id": null, "uuid": "5edf7d2f-c551-4b93-a64c-5cb73792f95c", "collection": "rbi_circular"}
2025-08-04 08:53:10,304 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-04 08:53:10,304 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-04 08:53:10,305 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-04 08:53:18,544 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-04 08:53:18,545 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exim Bank_s GOI-supported Line of Credit _LOC_ for USD 700 million to the Govt. of Mongolia _GO-MNG__ for financing construction of Crude Oil Refinery Plant in Mongolia.pdf
2025-08-04 08:53:18,545 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Exim Bank_s GOI-supported Line of Credit _LOC_ for USD 700 million to the Govt. of Mongolia _GO-MNG__ for financing construction of Crude Oil Refinery Plant in Mongolia.pdf
2025-08-04 08:53:18,545 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-04 08:53:18,546 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_other'], PID=7c5b1889-85f5-567e-b2cb-edde5da1e955, Document=RBI/2025-2026/37 - A.P. (DIR Series) Circular No. 05, Chunk=None
2025-08-04 08:53:18,546 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-2026/37 - A.P. (DIR Series) Circular No. 05 with text: 8000 chars
