2025-08-03 20:37:27,957 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250803_203727.log
2025-08-03 20:37:27,957 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250803_203727.log
2025-08-03 20:37:27,957 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-03 20:37:27,957 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-03 20:37:31,199 - utils.config - ERROR - No OpenAI API keys found in any location
2025-08-03 20:37:31,199 - utils.config - ERROR - Please set your API key using one of these methods:
2025-08-03 20:37:31,199 - utils.config - ERROR - 1. Run the set_openai_key.py script: python set_openai_key.py YOUR_API_KEY
2025-08-03 20:37:31,199 - utils.config - ERROR - 2. Set the OPENAI_API_KEY environment variable: export OPENAI_API_KEY=your_api_key
2025-08-03 20:37:31,199 - utils.config - ERROR - 3. Create a config file at ~/.selkea/config.json with {'OPENAI_API_KEY': 'your_api_key'}
2025-08-03 20:37:31,199 - utils.config - ERROR - Failed to load configuration: No OpenAI API keys configured
