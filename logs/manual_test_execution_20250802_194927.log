2025-08-02 19:49:27,254 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250802_194927.log
2025-08-02 19:49:27,254 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250802_194927.log
2025-08-02 19:49:27,254 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-02 19:49:27,254 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-02 19:49:31,054 - utils.config - INFO - Configuration loaded successfully
2025-08-02 19:49:31,589 - prompts.notification_categorizer - INFO - Processed 0 chunks from content
2025-08-02 19:49:31,589 - prompts.notification_categorizer - INFO - Chunks: []
2025-08-02 19:49:31,590 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250802_194931.log
2025-08-02 19:49:31,590 - manual_test - INFO - <PERSON><PERSON> configured to write logs to: logs/manual_test_execution_20250802_194931.log
2025-08-02 19:49:31,590 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-02 19:49:31,590 - manual_test - INFO - 📝 Pipeline execution log will be written to: logs/manual_test_execution.log
2025-08-02 19:49:31,602 - qdrant_utils - INFO - Configured Hugging Face HTTP backend to bypass SSL verification
2025-08-02 19:49:31,603 - config - INFO - Configuration loaded successfully
2025-08-02 19:49:33,004 - qdrant_utils - INFO - Loaded SPLADE model: naver/efficient-splade-VI-BT-large-doc
2025-08-02 19:49:33,005 - qdrant_utils - INFO - Initializing QdrantManager with URL: http://localhost:6333
2025-08-02 19:49:33,021 - httpx - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-02 19:49:33,027 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections "HTTP/1.1 200 OK"
2025-08-02 19:49:33,028 - qdrant_utils - INFO - ✅ QdrantClient initialized successfully
2025-08-02 19:49:33,909 - qdrant_utils - INFO - ✅ Sparse embedder initialized with model: Qdrant/bm25
2025-08-02 19:49:33,910 - qdrant_utils - INFO - 🔍 FastEmbed creates sparse vector type: <class 'langchain_qdrant.sparse_embeddings.SparseVector'>
2025-08-02 19:49:33,910 - qdrant_utils - INFO - 🔍 FastEmbed sparse vector indices count: 1
2025-08-02 19:49:33,927 - manual_test - INFO - 🚀 Starting Real Pipeline Test with Enhanced KB Storage...
2025-08-02 19:49:33,927 - manual_test - INFO - 🚀 Starting Real Pipeline Test with Enhanced KB Storage...
2025-08-02 19:49:33,928 - manual_test - INFO - 📄 Using notifications file: rbi_notifications.json
2025-08-02 19:49:33,928 - manual_test - INFO - 📄 Using notifications file: rbi_notifications.json
2025-08-02 19:49:33,928 - manual_test - INFO - Initialized key rotator with 2 keys
2025-08-02 19:49:33,928 - manual_test - INFO - Initialized key rotator with 2 keys
2025-08-02 19:49:33,928 - manual_test - INFO - ✅ Environment setup complete
2025-08-02 19:49:33,928 - manual_test - INFO - ✅ Environment setup complete
2025-08-02 19:49:33,929 - manual_test - INFO - ✅ Real notification processor initialized
2025-08-02 19:49:33,929 - manual_test - INFO - ✅ Real notification processor initialized
2025-08-02 19:49:33,930 - knowledge_base_executor - INFO - Logger configured to write logs to: logs/knowledge_base_executor_execution_20250802_194933.log
2025-08-02 19:49:33,930 - knowledge_base_executor - INFO - ✅ Sparse vectorizer initialized successfully
2025-08-02 19:49:33,944 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-08-02 19:49:33,944 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-02 19:49:37,682 - httpx - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-02 19:49:37,682 - utils.metadata_vector_utils - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-02 19:49:37,682 - knowledge_base_executor - INFO - ✅ Metadata vector search initialized in KB executor
2025-08-02 19:49:37,683 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-08-02 19:49:37,683 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-02 19:49:40,844 - httpx - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-02 19:49:40,844 - utils.metadata_vector_utils - INFO - Initialized MetadataVectorSearch with model all-MiniLM-L6-v2 and collection metadata_vectors
2025-08-02 19:49:40,844 - manual_test - INFO - ✅ Metadata vector search initialized
2025-08-02 19:49:40,844 - manual_test - INFO - ✅ Metadata vector search initialized
2025-08-02 19:49:40,844 - manual_test - INFO - 🧠 Selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-02 19:49:40,844 - manual_test - INFO - 🧠 Selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-02 19:49:40,845 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-08-02 19:49:40,845 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-02 19:49:44,148 - httpx - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-02 19:49:44,148 - manual_test - INFO - ✅ Direct metadata search components initialized
2025-08-02 19:49:44,148 - manual_test - INFO - ✅ Direct metadata search components initialized
2025-08-02 19:49:44,148 - manual_test - INFO - 🚀 Starting ENHANCED pipeline test with comprehensive UUID tracking...
2025-08-02 19:49:44,148 - manual_test - INFO - 🚀 Starting ENHANCED pipeline test with comprehensive UUID tracking...
2025-08-02 19:49:44,148 - manual_test - INFO - 🚀 Starting ENHANCED pipeline test with up to 50 notifications...
2025-08-02 19:49:44,148 - manual_test - INFO - 🚀 Starting ENHANCED pipeline test with up to 50 notifications...
2025-08-02 19:49:44,148 - manual_test - INFO - 📂 Loaded 1 notifications from rbi_notifications.json
2025-08-02 19:49:44,148 - manual_test - INFO - 📂 Loaded 1 notifications from rbi_notifications.json
2025-08-02 19:49:44,148 - manual_test - INFO - 
2025-08-02 19:49:44,148 - manual_test - INFO - 
2025-08-02 19:49:44,148 - manual_test - INFO - 🔄 ═══════════════════════════════════════════════════════════════
2025-08-02 19:49:44,148 - manual_test - INFO - 🔄 ═══════════════════════════════════════════════════════════════
2025-08-02 19:49:44,148 - manual_test - INFO - 🔄 PROCESSING NOTIFICATION 1/1 (100.0%)
2025-08-02 19:49:44,148 - manual_test - INFO - 🔄 PROCESSING NOTIFICATION 1/1 (100.0%)
2025-08-02 19:49:44,148 - manual_test - INFO - 🔄 Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Cr...
2025-08-02 19:49:44,148 - manual_test - INFO - 🔄 Title: Master Circular - Guarantees  Co-Acceptances   Letters Of Cr...
2025-08-02 19:49:44,148 - manual_test - INFO - 🔄 Date: 
2025-08-02 19:49:44,148 - manual_test - INFO - 🔄 Date: 
2025-08-02 19:49:44,148 - manual_test - INFO - 🔄 ═══════════════════════════════════════════════════════════════
2025-08-02 19:49:44,148 - manual_test - INFO - 🔄 ═══════════════════════════════════════════════════════════════
2025-08-02 19:49:44,148 - manual_test - INFO - 
2025-08-02 19:49:44,148 - manual_test - INFO - 
2025-08-02 19:49:44,151 - manual_test - WARNING - ⚠️ Primary code patterns failed, trying fallback extraction
2025-08-02 19:49:44,151 - manual_test - WARNING - ⚠️ Primary code patterns failed, trying fallback extraction
2025-08-02 19:49:44,151 - manual_test - WARNING - ⚠️ No codes extracted from text
2025-08-02 19:49:44,151 - manual_test - WARNING - ⚠️ No codes extracted from text
2025-08-02 19:49:44,151 - manual_test - INFO - 🚀 ENHANCED Processing notification: Master Circular - Guarantees  Co-Acceptances   Let...
2025-08-02 19:49:44,151 - manual_test - INFO - 🚀 ENHANCED Processing notification: Master Circular - Guarantees  Co-Acceptances   Let...
2025-08-02 19:49:44,151 - manual_test - INFO - 📊 Initial notification codes from title: {'short_code': '', 'long_code': '', 'full_code': '', 'year': '', 'all_codes': []}
2025-08-02 19:49:44,151 - manual_test - INFO - 📊 Initial notification codes from title: {'short_code': '', 'long_code': '', 'full_code': '', 'year': '', 'all_codes': []}
2025-08-02 19:49:44,151 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:44,151 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:44,263 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:44,263 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:44,455 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:44,455 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:44,461 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:49:44,461 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:49:44,471 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:49:44,471 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:49:44,471 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.59/13.05.000/2008-09', 'BSD.No.IP.30/12.05.05', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000/2008', 'A.P. (DIR Series) Circular No. 18', '.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024-25', 'UBD.No.Plan.42/09.27.00', 'UBD.PCB.No.59/13.05.000', 'DoR.STR.REC.3/09.27.000', 'BSD.No.IP.30/12.05.05/2002-03', 'RBI/2025-26/11', '.No.36/13.05.000/2008-09', '.IP.30/12.05.05/2002-03', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000', '.No.59/13.05.000/2008-09']}
2025-08-02 19:49:44,471 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.59/13.05.000/2008-09', 'BSD.No.IP.30/12.05.05', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000/2008', 'A.P. (DIR Series) Circular No. 18', '.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024-25', 'UBD.No.Plan.42/09.27.00', 'UBD.PCB.No.59/13.05.000', 'DoR.STR.REC.3/09.27.000', 'BSD.No.IP.30/12.05.05/2002-03', 'RBI/2025-26/11', '.No.36/13.05.000/2008-09', '.IP.30/12.05.05/2002-03', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000', '.No.59/13.05.000/2008-09']}
2025-08-02 19:49:44,471 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:49:44,471 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:49:44,471 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:49:44,471 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:49:44,471 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:49:44,471 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:49:44,472 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:49:44,472 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:49:44,472 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:49:44,472 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:49:44,472 - manual_test - INFO - ✅ Extracted 49457 chars from local PDF
2025-08-02 19:49:44,472 - manual_test - INFO - ✅ Extracted 49457 chars from local PDF
2025-08-02 19:49:44,472 - manual_test - INFO - 📄 Extracting notification codes from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:44,472 - manual_test - INFO - 📄 Extracting notification codes from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:44,472 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:44,472 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:44,544 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:44,544 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:44,737 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:44,737 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:44,744 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:49:44,744 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:49:44,753 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:49:44,753 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:49:44,753 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.59/13.05.000/2008-09', 'BSD.No.IP.30/12.05.05', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000/2008', 'A.P. (DIR Series) Circular No. 18', '.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024-25', 'UBD.No.Plan.42/09.27.00', 'UBD.PCB.No.59/13.05.000', 'DoR.STR.REC.3/09.27.000', 'BSD.No.IP.30/12.05.05/2002-03', 'RBI/2025-26/11', '.No.36/13.05.000/2008-09', '.IP.30/12.05.05/2002-03', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000', '.No.59/13.05.000/2008-09']}
2025-08-02 19:49:44,753 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.59/13.05.000/2008-09', 'BSD.No.IP.30/12.05.05', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000/2008', 'A.P. (DIR Series) Circular No. 18', '.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024-25', 'UBD.No.Plan.42/09.27.00', 'UBD.PCB.No.59/13.05.000', 'DoR.STR.REC.3/09.27.000', 'BSD.No.IP.30/12.05.05/2002-03', 'RBI/2025-26/11', '.No.36/13.05.000/2008-09', '.IP.30/12.05.05/2002-03', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000', '.No.59/13.05.000/2008-09']}
2025-08-02 19:49:44,753 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:49:44,753 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:49:44,753 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:49:44,753 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:49:44,753 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:49:44,753 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:49:44,753 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:49:44,753 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:49:44,753 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:49:44,753 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:49:44,753 - manual_test - INFO - 📋 Enhanced codes after PDF extraction: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.59/13.05.000/2008-09', 'BSD.No.IP.30/12.05.05', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000/2008', 'A.P. (DIR Series) Circular No. 18', '.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024-25', 'UBD.No.Plan.42/09.27.00', 'UBD.PCB.No.59/13.05.000', 'DoR.STR.REC.3/09.27.000', 'BSD.No.IP.30/12.05.05/2002-03', 'RBI/2025-26/11', '.No.36/13.05.000/2008-09', '.IP.30/12.05.05/2002-03', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000', '.No.59/13.05.000/2008-09'], 'watermark_status': 'ACTIVE', 'is_active': True}
2025-08-02 19:49:44,753 - manual_test - INFO - 📋 Enhanced codes after PDF extraction: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.59/13.05.000/2008-09', 'BSD.No.IP.30/12.05.05', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000/2008', 'A.P. (DIR Series) Circular No. 18', '.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024-25', 'UBD.No.Plan.42/09.27.00', 'UBD.PCB.No.59/13.05.000', 'DoR.STR.REC.3/09.27.000', 'BSD.No.IP.30/12.05.05/2002-03', 'RBI/2025-26/11', '.No.36/13.05.000/2008-09', '.IP.30/12.05.05/2002-03', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000', '.No.59/13.05.000/2008-09'], 'watermark_status': 'ACTIVE', 'is_active': True}
2025-08-02 19:49:44,753 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:49:44,753 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:49:44,753 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:49:44,753 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:49:44,754 - manual_test - INFO -    📋 Full code: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:49:44,754 - manual_test - INFO -    📋 Full code: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:49:44,754 - manual_test - INFO -    📅 Year: 2025
2025-08-02 19:49:44,754 - manual_test - INFO -    📅 Year: 2025
2025-08-02 19:49:44,754 - manual_test - INFO -    🏷️ Watermark status: ACTIVE
2025-08-02 19:49:44,754 - manual_test - INFO -    🏷️ Watermark status: ACTIVE
2025-08-02 19:49:44,754 - manual_test - INFO - --- ENHANCED Stage 1: Notification Analysis ---
2025-08-02 19:49:44,754 - manual_test - INFO - --- ENHANCED Stage 1: Notification Analysis ---
2025-08-02 19:49:44,754 - manual_test - INFO - 🔍 Starting notification analysis for: Master Circular - Guarantees  Co-Acceptances   Let...
2025-08-02 19:49:44,754 - manual_test - INFO - 🔍 Starting notification analysis for: Master Circular - Guarantees  Co-Acceptances   Let...
2025-08-02 19:49:44,754 - manual_test - INFO - 📏 Notification length: 49457 characters
2025-08-02 19:49:44,754 - manual_test - INFO - 📏 Notification length: 49457 characters
2025-08-02 19:49:44,754 - manual_test - INFO - 📝 Prompt created, making LLM call...
2025-08-02 19:49:44,754 - manual_test - INFO - 📝 Prompt created, making LLM call...
2025-08-02 19:49:44,758 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-02 19:49:44,758 - manual_test - INFO - Making LLM call with model: gpt-4.1
2025-08-02 19:49:48,559 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:49:48,567 - manual_test - INFO - ✅ Notification categorized as: Review (confidence: high)
2025-08-02 19:49:48,567 - manual_test - INFO - ✅ Notification categorized as: Review (confidence: high)
2025-08-02 19:49:48,569 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4.1 for regulatory_interpretation - Selected critical tier for regulatory_interpretation (context: 7218, priority: quality)
2025-08-02 19:49:48,569 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4.1 for regulatory_interpretation - Selected critical tier for regulatory_interpretation (context: 7218, priority: quality)
2025-08-02 19:49:54,615 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:49:54,620 - manual_test - INFO - 📋 KB Decision: update_existing (store: True, remove: True)
2025-08-02 19:49:54,620 - manual_test - INFO - 📋 KB Decision: update_existing (store: True, remove: True)
2025-08-02 19:49:54,621 - manual_test - INFO - --- ENHANCED Stage 2: Notification-level KB Actions ---
2025-08-02 19:49:54,621 - manual_test - INFO - --- ENHANCED Stage 2: Notification-level KB Actions ---
2025-08-02 19:49:54,621 - manual_test - INFO - notification_codes being passed to KB storage: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.59/13.05.000/2008-09', 'BSD.No.IP.30/12.05.05', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000/2008', 'A.P. (DIR Series) Circular No. 18', '.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024-25', 'UBD.No.Plan.42/09.27.00', 'UBD.PCB.No.59/13.05.000', 'DoR.STR.REC.3/09.27.000', 'BSD.No.IP.30/12.05.05/2002-03', 'RBI/2025-26/11', '.No.36/13.05.000/2008-09', '.IP.30/12.05.05/2002-03', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000', '.No.59/13.05.000/2008-09'], 'watermark_status': 'ACTIVE', 'is_active': True}
2025-08-02 19:49:54,621 - manual_test - INFO - notification_codes being passed to KB storage: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.59/13.05.000/2008-09', 'BSD.No.IP.30/12.05.05', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000/2008', 'A.P. (DIR Series) Circular No. 18', '.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024-25', 'UBD.No.Plan.42/09.27.00', 'UBD.PCB.No.59/13.05.000', 'DoR.STR.REC.3/09.27.000', 'BSD.No.IP.30/12.05.05/2002-03', 'RBI/2025-26/11', '.No.36/13.05.000/2008-09', '.IP.30/12.05.05/2002-03', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000', '.No.59/13.05.000/2008-09'], 'watermark_status': 'ACTIVE', 'is_active': True}
2025-08-02 19:49:54,621 - manual_test - INFO - 🔍 Detecting document type for flowchart-based processing...
2025-08-02 19:49:54,621 - manual_test - INFO - 🔍 Detecting document type for flowchart-based processing...
2025-08-02 19:49:54,621 - manual_test - INFO - 🔍 Detecting document type from title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs...
2025-08-02 19:49:54,621 - manual_test - INFO - 🔍 Detecting document type from title: Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs...
2025-08-02 19:49:54,621 - manual_test - INFO - 📋 Target collection from KB decision: rbi_master_circular
2025-08-02 19:49:54,621 - manual_test - INFO - 📋 Target collection from KB decision: rbi_master_circular
2025-08-02 19:49:54,621 - manual_test - INFO - ✅ Detected: Master Circular
2025-08-02 19:49:54,621 - manual_test - INFO - ✅ Detected: Master Circular
2025-08-02 19:49:54,622 - manual_test - INFO - 📋 Document Type Detected: master_circular
2025-08-02 19:49:54,622 - manual_test - INFO - 📋 Document Type Detected: master_circular
2025-08-02 19:49:54,622 - manual_test - INFO - 🎯 Generating flowchart-based actions for document type: master_circular
2025-08-02 19:49:54,622 - manual_test - INFO - 🎯 Generating flowchart-based actions for document type: master_circular
2025-08-02 19:49:54,622 - manual_test - INFO - 📘 Processing Master Circular actions...
2025-08-02 19:49:54,622 - manual_test - INFO - 📘 Processing Master Circular actions...
2025-08-02 19:49:55,068 - utils.s3_utils - INFO - Successfully uploaded file to https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:49:55,071 - manual_test - INFO - ✅ Uploaded notification PDF to S3: https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:49:55,071 - manual_test - INFO - ✅ Uploaded notification PDF to S3: https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:49:55,071 - manual_test - INFO - ✅ Added: ADD_DOCUMENT action for Master Circular
2025-08-02 19:49:55,071 - manual_test - INFO - ✅ Added: ADD_DOCUMENT action for Master Circular
2025-08-02 19:49:55,071 - manual_test - INFO - 🎯 Generated 1 KB actions based on flowchart:
2025-08-02 19:49:55,071 - manual_test - INFO - 🎯 Generated 1 KB actions based on flowchart:
2025-08-02 19:49:55,071 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT in rbi_master_circular
2025-08-02 19:49:55,071 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT in rbi_master_circular
2025-08-02 19:49:55,071 - manual_test - INFO -       🎯 Target: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:49:55,071 - manual_test - INFO -       🎯 Target: RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:49:55,071 - manual_test - INFO -       🤔 Reasoning: New Master Circular publication - follows flowchart logic
2025-08-02 19:49:55,071 - manual_test - INFO -       🤔 Reasoning: New Master Circular publication - follows flowchart logic
2025-08-02 19:49:55,072 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-02 19:49:55,072 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-02 19:49:55,528 - utils.s3_utils - INFO - Successfully uploaded file to https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:49:55,531 - manual_test - INFO - ✅ Uploaded notification PDF to S3: https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:49:55,531 - manual_test - INFO - ✅ Uploaded notification PDF to S3: https://localdocdump.s3.ap-south-1.amazonaws.com/notifications/rbi_master_circular/RBI_2025-26_11_-_DoR.STR.REC.3_09.27.000_2024-25.pdf
2025-08-02 19:49:55,532 - manual_test - INFO - ✅ Executing 1 flowchart-based KB actions
2025-08-02 19:49:55,532 - manual_test - INFO - ✅ Executing 1 flowchart-based KB actions
2025-08-02 19:49:55,532 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT for collection rbi_master_circular
2025-08-02 19:49:55,532 - manual_test - INFO -   📝 Action 1: ADD_DOCUMENT for collection rbi_master_circular
2025-08-02 19:49:55,532 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:49:55,532 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:55,532 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:49:55,533 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:49:55,533 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_master_circular'], PID=67b8e32f-0047-537b-8852-ae79f77fc0ad, Document=RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25, Chunk=None
2025-08-02 19:49:55,534 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 with text: 8000 chars
2025-08-02 19:49:55,543 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_master_circular/exists "HTTP/1.1 200 OK"
2025-08-02 19:49:55,555 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_master_circular "HTTP/1.1 200 OK"
2025-08-02 19:49:55,558 - qdrant_utils - INFO - 🔍 Collection rbi_master_circular vectors: size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:49:55,558 - qdrant_utils - INFO - 🔍 Collection rbi_master_circular sparse vectors: {'fast-sparse-bm25': SparseVectorParams(index=SparseIndexParams(full_scan_threshold=None, on_disk=None, datatype=None), modifier=<Modifier.IDF: 'idf'>)}
2025-08-02 19:49:55,558 - qdrant_utils - WARNING - Collection rbi_master_circular exists but uses hybrid/single format. Will work with existing.
2025-08-02 19:49:57,486 - openai_utils - INFO - Embedding 1 documents
2025-08-02 19:49:58,196 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-02 19:49:58,552 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_master_circular "HTTP/1.1 200 OK"
2025-08-02 19:49:58,553 - qdrant_utils - INFO - Collection info for rbi_master_circular: vectors=size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:49:58,553 - qdrant_utils - INFO - Collection rbi_master_circular uses hybrid format (single dense + sparse)
2025-08-02 19:49:58,553 - qdrant_utils - INFO - Collection format: hybrid
2025-08-02 19:49:58,553 - qdrant_utils - INFO - ✅ Added dense vector with 3072 dimensions
2025-08-02 19:49:58,557 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_master_circular "HTTP/1.1 200 OK"
2025-08-02 19:49:58,557 - qdrant_utils - INFO - ✅ Added sparse vector 'fast-sparse-bm25' with 410 indices
2025-08-02 19:49:58,558 - qdrant_utils - WARNING - ⚠️ Skipping sparse vector 'splade' - not configured in collection schema
2025-08-02 19:49:58,558 - qdrant_utils - INFO - 🔍 Final vector keys for point 67b8e32f-0047-537b-8852-ae79f77fc0ad: ['', 'fast-sparse-bm25']
2025-08-02 19:49:58,586 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_master_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:49:58,587 - qdrant_utils - INFO - ✅ Using hybrid format for point 67b8e32f-0047-537b-8852-ae79f77fc0ad
2025-08-02 19:49:58,587 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 (ID: 67b8e32f-0047-537b-8852-ae79f77fc0ad)
2025-08-02 19:49:58,587 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25 (ID: 67b8e32f-0047-537b-8852-ae79f77fc0ad) to collections: ['rbi_master_circular']", "timestamp": "2025-08-02T14:19:58.587466", "document_id": "RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25", "uuid": "67b8e32f-0047-537b-8852-ae79f77fc0ad", "collection": "rbi_master_circular"}
2025-08-02 19:49:58,587 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:49:58,587 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:49:58,588 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:49:58,589 - manual_test - INFO - ✅ Flowchart-based KB Results summary:
2025-08-02 19:49:58,589 - manual_test - INFO - ✅ Flowchart-based KB Results summary:
2025-08-02 19:49:58,589 - manual_test - INFO -   📋 Result 1: ADD_DOCUMENT - SUCCESS
2025-08-02 19:49:58,589 - manual_test - INFO -   📋 Result 1: ADD_DOCUMENT - SUCCESS
2025-08-02 19:49:58,589 - manual_test - INFO -       📦 Collection: rbi_master_circular
2025-08-02 19:49:58,589 - manual_test - INFO -       📦 Collection: rbi_master_circular
2025-08-02 19:49:58,589 - manual_test - INFO -       🆔 UUID: 67b8e32f-0047-537b-8852-ae79f77fc0ad
2025-08-02 19:49:58,589 - manual_test - INFO -       🆔 UUID: 67b8e32f-0047-537b-8852-ae79f77fc0ad
2025-08-02 19:49:58,589 - manual_test - INFO -       🤔 Reasoning: New Master Circular publication - follows flowchart logic
2025-08-02 19:49:58,589 - manual_test - INFO -       🤔 Reasoning: New Master Circular publication - follows flowchart logic
2025-08-02 19:49:58,589 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-02 19:49:58,589 - manual_test - INFO -       📊 Confidence: 0.9
2025-08-02 19:49:58,589 - manual_test - INFO - 📊 Added 1 documents with UUIDs: ['67b8e32f-0047-537b-8852-ae79f77fc0ad']
2025-08-02 19:49:58,589 - manual_test - INFO - 📊 Added 1 documents with UUIDs: ['67b8e32f-0047-537b-8852-ae79f77fc0ad']
2025-08-02 19:49:58,589 - manual_test - INFO - 📊 Adding notification to metadata_vectors collection for enhanced search
2025-08-02 19:49:58,589 - manual_test - INFO - 📊 Adding notification to metadata_vectors collection for enhanced search
2025-08-02 19:49:58,591 - utils.qdrant_utils - INFO - Configured Hugging Face HTTP backend to bypass SSL verification
2025-08-02 19:49:59,753 - utils.qdrant_utils - INFO - Loaded SPLADE model: naver/efficient-splade-VI-BT-large-doc
2025-08-02 19:49:59,754 - utils.qdrant_utils - INFO - Initializing QdrantManager with URL: http://localhost:6333
2025-08-02 19:49:59,769 - httpx - INFO - HTTP Request: GET http://localhost:6333 "HTTP/1.1 200 OK"
2025-08-02 19:49:59,772 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections "HTTP/1.1 200 OK"
2025-08-02 19:49:59,773 - utils.qdrant_utils - INFO - ✅ QdrantClient initialized successfully
2025-08-02 19:50:00,639 - utils.qdrant_utils - INFO - ✅ Sparse embedder initialized with model: Qdrant/bm25
2025-08-02 19:50:00,639 - utils.qdrant_utils - INFO - 🔍 FastEmbed creates sparse vector type: <class 'langchain_qdrant.sparse_embeddings.SparseVector'>
2025-08-02 19:50:00,639 - utils.qdrant_utils - INFO - 🔍 FastEmbed sparse vector indices count: 1
2025-08-02 19:50:00,639 - manual_test - INFO - 🧠 Using smart-selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-02 19:50:00,639 - manual_test - INFO - 🧠 Using smart-selected embedding model for metadata: all-MiniLM-L6-v2
2025-08-02 19:50:00,640 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-08-02 19:50:00,640 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-02 19:50:04,443 - manual_test - INFO - 🔍 Adding notification to metadata_vectors with ID: 2638f94b-0bc4-4c51-b2c2-aae911025668
2025-08-02 19:50:04,443 - manual_test - INFO - 🔍 Adding notification to metadata_vectors with ID: 2638f94b-0bc4-4c51-b2c2-aae911025668
2025-08-02 19:50:04,443 - manual_test - INFO - 🔍 Vector keys: ['document_title_vec', 'document_number_vec', 'short_summary_vec', 'document_id_vec']
2025-08-02 19:50:04,443 - manual_test - INFO - 🔍 Vector keys: ['document_title_vec', 'document_number_vec', 'short_summary_vec', 'document_id_vec']
2025-08-02 19:50:04,443 - manual_test - INFO - 🔍 Vector dimensions: [('document_title_vec', 384), ('document_number_vec', 384), ('short_summary_vec', 384), ('document_id_vec', 384)]
2025-08-02 19:50:04,443 - manual_test - INFO - 🔍 Vector dimensions: [('document_title_vec', 384), ('document_number_vec', 384), ('short_summary_vec', 384), ('document_id_vec', 384)]
2025-08-02 19:50:04,450 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/metadata_vectors "HTTP/1.1 200 OK"
2025-08-02 19:50:04,469 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/metadata_vectors/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:50:04,469 - manual_test - INFO - ✅ Added notification to metadata_vectors: 2638f94b-0bc4-4c51-b2c2-aae911025668
2025-08-02 19:50:04,469 - manual_test - INFO - ✅ Added notification to metadata_vectors: 2638f94b-0bc4-4c51-b2c2-aae911025668
2025-08-02 19:50:04,469 - manual_test - INFO -    📊 Notification metadata vectors UUIDs: 1
2025-08-02 19:50:04,469 - manual_test - INFO -    📊 Notification metadata vectors UUIDs: 1
2025-08-02 19:50:04,469 - manual_test - INFO - --- ENHANCED Stage 3: STREAMLINED PDF Processing and Action Execution ---
2025-08-02 19:50:04,469 - manual_test - INFO - --- ENHANCED Stage 3: STREAMLINED PDF Processing and Action Execution ---
2025-08-02 19:50:04,469 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:04,469 - manual_test - INFO - 📄 Extracting content from local PDF (HTML AST): rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:04,542 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:04,542 - manual_test - INFO -    ✅ No watermark found - document is ACTIVE: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:04,731 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:04,731 - manual_test - INFO -    📄 Parsed HTML AST from PDF: rbi_docs/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:04,738 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:50:04,738 - manual_test - INFO -    📦 Chunked HTML AST into 3 chunks
2025-08-02 19:50:04,746 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:50:04,746 - manual_test - INFO - ✅ Code extraction successful - Short: RBI/2025-26/11, Long: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:50:04,746 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.59/13.05.000/2008-09', 'BSD.No.IP.30/12.05.05', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000/2008', 'A.P. (DIR Series) Circular No. 18', '.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024-25', 'UBD.No.Plan.42/09.27.00', 'UBD.PCB.No.59/13.05.000', 'DoR.STR.REC.3/09.27.000', 'BSD.No.IP.30/12.05.05/2002-03', 'RBI/2025-26/11', '.No.36/13.05.000/2008-09', '.IP.30/12.05.05/2002-03', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000', '.No.59/13.05.000/2008-09']}
2025-08-02 19:50:04,746 - manual_test - INFO -    📋 Extracted notification codes: {'short_code': 'RBI/2025-26/11', 'long_code': 'DoR.STR.REC.3/09.27.000/2024-25', 'full_code': 'RBI/2025-26/11 - DoR.STR.REC.3/09.27.000/2024-25', 'year': '2025', 'all_codes': ['BPD.Cir.No.29/13.05.000', 'Plan.Cir.SUB.1/09.27.00', 'BPD.Cir.No.29/13.05.000/2011', 'UBD.PCB.No.36/13.05.000/2008-09', 'Plan.PCB.CIR.07/09.27.00', 'UBD.PCB.No.59/13.05.000/2008-09', 'BSD.No.IP.30/12.05.05', 'DoR.STR.REC.3/09.27.000/2024', 'UBD.PCB.No.59/13.05.000/2008', 'UBD.PCB.No.36/13.05.000/2008', 'A.P. (DIR Series) Circular No. 18', '.REC.3/09.27.000/2024-25', 'DoR.STR.REC.3/09.27.000/2024-25', 'UBD.No.Plan.42/09.27.00', 'UBD.PCB.No.59/13.05.000', 'DoR.STR.REC.3/09.27.000', 'BSD.No.IP.30/12.05.05/2002-03', 'RBI/2025-26/11', '.No.36/13.05.000/2008-09', '.IP.30/12.05.05/2002-03', 'BSD.No.IP.30/12.05.05/2002', 'UBD.PCB.No.36/13.05.000', '.No.59/13.05.000/2008-09']}
2025-08-02 19:50:04,746 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:50:04,746 - manual_test - INFO -    ✅ Extracted 3 chunks from local PDF
2025-08-02 19:50:04,746 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:50:04,746 - manual_test - INFO -    🏷️ Watermark Status: ACTIVE
2025-08-02 19:50:04,746 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:50:04,746 - manual_test - INFO -    📋 Short code: RBI/2025-26/11
2025-08-02 19:50:04,746 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:50:04,746 - manual_test - INFO -    📋 Long code: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:50:04,746 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:50:04,746 - manual_test - INFO -    📅 Year from codes: 2025
2025-08-02 19:50:04,746 - manual_test - INFO - 🔧 Starting streamlined processing of 3 chunks
2025-08-02 19:50:04,746 - manual_test - INFO - 🔧 Starting streamlined processing of 3 chunks
2025-08-02 19:50:04,746 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 1/3
2025-08-02 19:50:04,746 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 1/3
2025-08-02 19:50:04,746 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 2513 chars
2025-08-02 19:50:04,746 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 2513 chars
2025-08-02 19:50:04,746 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:50:04,746 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:50:04,747 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:50:04,747 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:50:04,747 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 2513 chars
2025-08-02 19:50:04,747 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 2513 chars
2025-08-02 19:50:04,747 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:50:04,747 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:50:04,747 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:50:04,747 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:50:04,747 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o-mini for document_classification - Selected simple tier for document_classification (context: 491, priority: balanced)
2025-08-02 19:50:04,747 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o-mini for document_classification - Selected simple tier for document_classification (context: 491, priority: balanced)
2025-08-02 19:50:07,311 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:50:07,312 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:50:07,312 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:50:07,313 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular issued by the Reserv...
2025-08-02 19:50:07,313 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular issued by the Reserv...
2025-08-02 19:50:07,313 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:50:07,313 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:50:07,313 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:50:07,313 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:50:07,313 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular issued by the Reserve Bank of India, which consolidates existing instructions and guidelines related to guarantees, co-acceptances, and letters of credit for Urban Co-operative Banks. It is regulatory in nature as it pertains to compliance obligations for banks, and it should be indexed for reference and compliance purposes."}
2025-08-02 19:50:07,313 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular issued by the Reserve Bank of India, which consolidates existing instructions and guidelines related to guarantees, co-acceptances, and letters of credit for Urban Co-operative Banks. It is regulatory in nature as it pertains to compliance obligations for banks, and it should be indexed for reference and compliance purposes."}
2025-08-02 19:50:07,314 - manual_test - INFO - ✅ Chunk 1 classified as regulatory - INDEXING
2025-08-02 19:50:07,314 - manual_test - INFO - ✅ Chunk 1 classified as regulatory - INDEXING
2025-08-02 19:50:07,314 - root - INFO - Sending structured request to extract document actions
2025-08-02 19:50:10,589 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:50:10,597 - root - INFO - Received response type: <class '__main__.DocumentActionsList'>
2025-08-02 19:50:10,597 - root - INFO - ✅ Successfully received structured Pydantic output
2025-08-02 19:50:10,597 - manual_test - INFO - 🎯 Executing 1 actions for chunk 1
2025-08-02 19:50:10,597 - manual_test - INFO - 🎯 Executing 1 actions for chunk 1
2025-08-02 19:50:10,597 - manual_test - INFO - 🎯 Executing 1 chunk-specific actions
2025-08-02 19:50:10,597 - manual_test - INFO - 🎯 Executing 1 chunk-specific actions
2025-08-02 19:50:10,597 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-02 19:50:10,597 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-02 19:50:10,597 - manual_test - INFO - 📝 Sample action: {'document_id': 'DoR.STR.REC.3/09.27.000/2024-25', 'action_type': 'UPDATE_DOCUMENT', 'confidence': 'high', 'document_title': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs', 'document_url': None, 'reference_number': None, 'department': 'DoR', 'original_date': '2024-04-01', 'update_location': 'full-document', 'sunset_withdraw_date': None, 'reasoning': 'The document has been updated to reflect all instructions issued up to March 31, 2025.'}
2025-08-02 19:50:10,597 - manual_test - INFO - 📝 Sample action: {'document_id': 'DoR.STR.REC.3/09.27.000/2024-25', 'action_type': 'UPDATE_DOCUMENT', 'confidence': 'high', 'document_title': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs', 'document_url': None, 'reference_number': None, 'department': 'DoR', 'original_date': '2024-04-01', 'update_location': 'full-document', 'sunset_withdraw_date': None, 'reasoning': 'The document has been updated to reflect all instructions issued up to March 31, 2025.'}
2025-08-02 19:50:10,597 - manual_test - INFO - ▶️ Processing action 1/1
2025-08-02 19:50:10,597 - manual_test - INFO - ▶️ Processing action 1/1
2025-08-02 19:50:10,598 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_DoR_STR_REC_3_0_chunk0_da30ad62
2025-08-02 19:50:10,598 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_DoR_STR_REC_3_0_chunk0_da30ad62
2025-08-02 19:50:10,598 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:50:10,598 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:50:10,598 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:50:10,598 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:50:10,599 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:50:10,599 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:10,599 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:10,599 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:50:10,599 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:50:10,655 - utils.metadata_vector_utils - INFO - Searching metadata_vectors for 'DoR.STR.REC.3/09.27.000/2024-25' in field 'document_id'
2025-08-02 19:50:10,734 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-02 19:50:10,735 - utils.metadata_vector_utils - INFO - Found 5 results for metadata search
2025-08-02 19:50:10,735 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:50:10,735 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9741a628-999e-4b3f-aa8d-da299fe6f6c1, Score=0.8026, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-02 19:50:10,735 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3d495fa6-6797-461c-bf81-7de0eb59a24a, Score=0.8026, Document=DOR.FIN.REC.No.73/03.10.117/2022-23
2025-08-02 19:50:10,735 - knowledge_base_executor - INFO - [QDRANT] Match: ID=2105f02f-10ac-520c-82c1-d91e04509d6d, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:50:10,735 - knowledge_base_executor - INFO - [QDRANT] Match: ID=d2b23822-c5a2-5527-8507-5b3d29d5794c, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:50:10,735 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5d04a677-f401-5734-88ac-14846a6cf5b3, Score=0.7784, Document=DOR.CRE.REC.26/21.01.023/2025-26
2025-08-02 19:50:10,735 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:10,736 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:50:10,737 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: DoR.STR.REC.3/09.27.000/2024-25
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:50:10,738 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:50:10,738 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d', 'd2b23822-c5a2-5527-8507-5b3d29d5794c', '5d04a677-f401-5734-88ac-14846a6cf5b3']}
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9741a628-999e-4b3f-aa8d-da299fe6f6c1
2025-08-02 19:50:10,738 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d495fa6-6797-461c-bf81-7de0eb59a24a
2025-08-02 19:50:10,739 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 2105f02f-10ac-520c-82c1-d91e04509d6d
2025-08-02 19:50:10,739 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9741a628-999e-4b3f-aa8d-da299fe6f6c1', '3d495fa6-6797-461c-bf81-7de0eb59a24a', '2105f02f-10ac-520c-82c1-d91e04509d6d']...
2025-08-02 19:50:10,739 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:10,739 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:20:10.739150"}
2025-08-02 19:50:10,739 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:50:10,739 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:50:10,740 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:10,740 - manual_test - WARNING - ⚠️ Action execution failed: None
2025-08-02 19:50:10,740 - manual_test - WARNING - ⚠️ Action execution failed: None
2025-08-02 19:50:10,740 - manual_test - INFO - 📊 Chunk action execution: 0/1 successful
2025-08-02 19:50:10,740 - manual_test - INFO - 📊 Chunk action execution: 0/1 successful
2025-08-02 19:50:10,741 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 1:
2025-08-02 19:50:10,741 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 1:
2025-08-02 19:50:10,741 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2025-26/11
2025-08-02 19:50:10,741 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2025-26/11
2025-08-02 19:50:10,741 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2025-26/11
2025-08-02 19:50:10,741 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2025-26/11
2025-08-02 19:50:10,741 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-02 19:50:10,741 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-02 19:50:10,741 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:50:10,741 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:10,741 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:10,741 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:50:10,741 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=7d697936-3148-4a15-9f27-2c8304072df9, Document=None, Chunk=0
2025-08-02 19:50:10,741 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 2513 chars
2025-08-02 19:50:10,744 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular/exists "HTTP/1.1 200 OK"
2025-08-02 19:50:10,747 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:50:10,748 - qdrant_utils - INFO - 🔍 Collection rbi_circular vectors: size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:50:10,748 - qdrant_utils - INFO - 🔍 Collection rbi_circular sparse vectors: {'fast-sparse-bm25': SparseVectorParams(index=SparseIndexParams(full_scan_threshold=None, on_disk=None, datatype=None), modifier=<Modifier.IDF: 'idf'>)}
2025-08-02 19:50:10,748 - qdrant_utils - WARNING - Collection rbi_circular exists but uses hybrid/single format. Will work with existing.
2025-08-02 19:50:11,724 - openai_utils - INFO - Embedding 1 documents
2025-08-02 19:50:12,532 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-02 19:50:12,849 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:50:12,849 - qdrant_utils - INFO - Collection info for rbi_circular: vectors=size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:50:12,849 - qdrant_utils - INFO - Collection rbi_circular uses hybrid format (single dense + sparse)
2025-08-02 19:50:12,849 - qdrant_utils - INFO - Collection format: hybrid
2025-08-02 19:50:12,849 - qdrant_utils - INFO - ✅ Added dense vector with 3072 dimensions
2025-08-02 19:50:12,853 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:50:12,853 - qdrant_utils - INFO - ✅ Added sparse vector 'fast-sparse-bm25' with 162 indices
2025-08-02 19:50:12,853 - qdrant_utils - WARNING - ⚠️ Skipping sparse vector 'splade' - not configured in collection schema
2025-08-02 19:50:12,853 - qdrant_utils - INFO - 🔍 Final vector keys for point 7d697936-3148-4a15-9f27-2c8304072df9: ['', 'fast-sparse-bm25']
2025-08-02 19:50:12,872 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:50:12,872 - qdrant_utils - INFO - ✅ Using hybrid format for point 7d697936-3148-4a15-9f27-2c8304072df9
2025-08-02 19:50:12,872 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 7d697936-3148-4a15-9f27-2c8304072df9)
2025-08-02 19:50:12,872 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 7d697936-3148-4a15-9f27-2c8304072df9) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:20:12.872628", "document_id": null, "uuid": "7d697936-3148-4a15-9f27-2c8304072df9", "collection": "rbi_circular"}
2025-08-02 19:50:12,872 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:50:12,872 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:50:12,874 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:12,874 - manual_test - INFO - 🆔 CHUNK UUID: 7d697936-3148-4a15-9f27-2c8304072df9
2025-08-02 19:50:12,874 - manual_test - INFO - 🆔 CHUNK UUID: 7d697936-3148-4a15-9f27-2c8304072df9
2025-08-02 19:50:12,874 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 1
2025-08-02 19:50:12,874 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 1
2025-08-02 19:50:12,874 - manual_test - INFO -    ✅ Chunk 1 added to processing queue
2025-08-02 19:50:12,874 - manual_test - INFO -    ✅ Chunk 1 added to processing queue
2025-08-02 19:50:12,874 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 2/3
2025-08-02 19:50:12,874 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 2/3
2025-08-02 19:50:12,874 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 8230 chars
2025-08-02 19:50:12,874 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 8230 chars
2025-08-02 19:50:12,874 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:50:12,874 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:50:12,874 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:50:12,874 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:50:12,874 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 8230 chars
2025-08-02 19:50:12,874 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 8230 chars
2025-08-02 19:50:12,874 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:50:12,874 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:50:12,875 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:50:12,875 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:50:12,875 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o for document_classification - Selected medium tier for document_classification (context: 1206, priority: balanced)
2025-08-02 19:50:12,875 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o for document_classification - Selected medium tier for document_classification (context: 1206, priority: balanced)
2025-08-02 19:50:17,406 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:50:17,410 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:50:17,410 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:50:17,410 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular, which typically con...
2025-08-02 19:50:17,410 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular, which typically con...
2025-08-02 19:50:17,410 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:50:17,410 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:50:17,410 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:50:17,410 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:50:17,411 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular, which typically consolidates regulatory guidelines and requirements issued by the RBI. It contains detailed regulatory requirements and compliance obligations related to guarantees, co-acceptances, and letters of credit for Urban Co-operative Banks (UCBs). Such documents are crucial for ensuring compliance and understanding of regulatory expectations, making them suitable for indexing in the knowledge base. The content is not administrative or withdrawn, and it provides essential regulatory guidance, justifying its classification and indexing."}
2025-08-02 19:50:17,411 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular, which typically consolidates regulatory guidelines and requirements issued by the RBI. It contains detailed regulatory requirements and compliance obligations related to guarantees, co-acceptances, and letters of credit for Urban Co-operative Banks (UCBs). Such documents are crucial for ensuring compliance and understanding of regulatory expectations, making them suitable for indexing in the knowledge base. The content is not administrative or withdrawn, and it provides essential regulatory guidance, justifying its classification and indexing."}
2025-08-02 19:50:17,411 - manual_test - INFO - ✅ Chunk 2 classified as regulatory - INDEXING
2025-08-02 19:50:17,411 - manual_test - INFO - ✅ Chunk 2 classified as regulatory - INDEXING
2025-08-02 19:50:17,411 - root - INFO - Sending structured request to extract document actions
2025-08-02 19:50:26,358 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:50:26,367 - root - INFO - Received response type: <class '__main__.DocumentActionsList'>
2025-08-02 19:50:26,367 - root - INFO - ✅ Successfully received structured Pydantic output
2025-08-02 19:50:26,368 - manual_test - INFO - 🎯 Executing 1 actions for chunk 2
2025-08-02 19:50:26,368 - manual_test - INFO - 🎯 Executing 1 actions for chunk 2
2025-08-02 19:50:26,368 - manual_test - INFO - 🎯 Executing 1 chunk-specific actions
2025-08-02 19:50:26,368 - manual_test - INFO - 🎯 Executing 1 chunk-specific actions
2025-08-02 19:50:26,368 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-02 19:50:26,368 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-02 19:50:26,368 - manual_test - INFO - 📝 Sample action: {'document_id': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs', 'action_type': 'UPDATE_DOCUMENT', 'confidence': 'high', 'document_title': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit', 'document_url': None, 'reference_number': None, 'department': 'Department of Regulation', 'original_date': None, 'update_location': 'full-document', 'sunset_withdraw_date': None, 'reasoning': 'The document contains updated guidelines and safeguards for the issuance of guarantees, co-acceptances, and letters of credit by UCBs.'}
2025-08-02 19:50:26,368 - manual_test - INFO - 📝 Sample action: {'document_id': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs', 'action_type': 'UPDATE_DOCUMENT', 'confidence': 'high', 'document_title': 'Master Circular - Guarantees, Co-acceptances & Letters of Credit', 'document_url': None, 'reference_number': None, 'department': 'Department of Regulation', 'original_date': None, 'update_location': 'full-document', 'sunset_withdraw_date': None, 'reasoning': 'The document contains updated guidelines and safeguards for the issuance of guarantees, co-acceptances, and letters of credit by UCBs.'}
2025-08-02 19:50:26,368 - manual_test - INFO - ▶️ Processing action 1/1
2025-08-02 19:50:26,368 - manual_test - INFO - ▶️ Processing action 1/1
2025-08-02 19:50:26,368 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_Master_Circular_chunk1_099fb094
2025-08-02 19:50:26,368 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_Master_Circular_chunk1_099fb094
2025-08-02 19:50:26,368 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs
2025-08-02 19:50:26,368 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs
2025-08-02 19:50:26,368 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs
2025-08-02 19:50:26,368 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs
2025-08-02 19:50:26,369 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:50:26,369 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:26,369 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:26,370 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:50:26,370 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:50:26,553 - utils.metadata_vector_utils - INFO - Searching metadata_vectors for 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs' in field 'document_id'
2025-08-02 19:50:26,565 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-02 19:50:26,566 - utils.metadata_vector_utils - INFO - Found 5 results for metadata search
2025-08-02 19:50:26,566 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] Match: ID=521d0371-918a-4608-a972-d5d632952cc8, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] Match: ID=7940f618-ba47-45d9-aa0d-98ebba0fb222, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ccd83ffc-2347-403f-92cc-2aeb36cd3027, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] Match: ID=c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1, Score=0.4042, Document=Master Circular No.2/2003-2004
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Normalized document ID: 'Master Circular - Guarantees, Co-acceptances & Letters of Credit - UCBs' → 'MasterCircular-Guarantees,Co-acceptances&LettersofCredit-UCBs'
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: MasterCircular-Guarantees,Co-acceptances&LettersofCredit-UCBs
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:50:26,567 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:50:26,568 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:50:26,568 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027', 'b3ebd2ba-3bb1-4689-9bc3-bd9a60f2382a', 'c10ac0e6-af32-4a12-bcf7-5b715b8cbfe1']}
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 521d0371-918a-4608-a972-d5d632952cc8
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 7940f618-ba47-45d9-aa0d-98ebba0fb222
2025-08-02 19:50:26,568 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ccd83ffc-2347-403f-92cc-2aeb36cd3027
2025-08-02 19:50:26,569 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['521d0371-918a-4608-a972-d5d632952cc8', '7940f618-ba47-45d9-aa0d-98ebba0fb222', 'ccd83ffc-2347-403f-92cc-2aeb36cd3027']...
2025-08-02 19:50:26,569 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:26,569 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:20:26.569090"}
2025-08-02 19:50:26,569 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:50:26,569 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:50:26,572 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:26,572 - manual_test - WARNING - ⚠️ Action execution failed: None
2025-08-02 19:50:26,572 - manual_test - WARNING - ⚠️ Action execution failed: None
2025-08-02 19:50:26,573 - manual_test - INFO - 📊 Chunk action execution: 0/1 successful
2025-08-02 19:50:26,573 - manual_test - INFO - 📊 Chunk action execution: 0/1 successful
2025-08-02 19:50:26,573 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 2:
2025-08-02 19:50:26,573 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 2:
2025-08-02 19:50:26,573 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2025-26/11
2025-08-02 19:50:26,573 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2025-26/11
2025-08-02 19:50:26,573 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2025-26/11
2025-08-02 19:50:26,573 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2025-26/11
2025-08-02 19:50:26,573 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-02 19:50:26,573 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-02 19:50:26,573 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:50:26,573 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:26,573 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:26,573 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:50:26,573 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=e05cd530-c2a1-437f-9529-47b563692c5a, Document=None, Chunk=1
2025-08-02 19:50:26,573 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-02 19:50:26,577 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular/exists "HTTP/1.1 200 OK"
2025-08-02 19:50:26,580 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:50:26,581 - qdrant_utils - INFO - 🔍 Collection rbi_circular vectors: size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:50:26,581 - qdrant_utils - INFO - 🔍 Collection rbi_circular sparse vectors: {'fast-sparse-bm25': SparseVectorParams(index=SparseIndexParams(full_scan_threshold=None, on_disk=None, datatype=None), modifier=<Modifier.IDF: 'idf'>)}
2025-08-02 19:50:26,581 - qdrant_utils - WARNING - Collection rbi_circular exists but uses hybrid/single format. Will work with existing.
2025-08-02 19:50:28,103 - openai_utils - INFO - Embedding 1 documents
2025-08-02 19:50:28,654 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-02 19:50:29,048 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:50:29,048 - qdrant_utils - INFO - Collection info for rbi_circular: vectors=size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:50:29,048 - qdrant_utils - INFO - Collection rbi_circular uses hybrid format (single dense + sparse)
2025-08-02 19:50:29,048 - qdrant_utils - INFO - Collection format: hybrid
2025-08-02 19:50:29,048 - qdrant_utils - INFO - ✅ Added dense vector with 3072 dimensions
2025-08-02 19:50:29,052 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:50:29,052 - qdrant_utils - INFO - ✅ Added sparse vector 'fast-sparse-bm25' with 408 indices
2025-08-02 19:50:29,052 - qdrant_utils - WARNING - ⚠️ Skipping sparse vector 'splade' - not configured in collection schema
2025-08-02 19:50:29,052 - qdrant_utils - INFO - 🔍 Final vector keys for point e05cd530-c2a1-437f-9529-47b563692c5a: ['', 'fast-sparse-bm25']
2025-08-02 19:50:29,081 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:50:29,081 - qdrant_utils - INFO - ✅ Using hybrid format for point e05cd530-c2a1-437f-9529-47b563692c5a
2025-08-02 19:50:29,081 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: e05cd530-c2a1-437f-9529-47b563692c5a)
2025-08-02 19:50:29,081 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: e05cd530-c2a1-437f-9529-47b563692c5a) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:20:29.081721", "document_id": null, "uuid": "e05cd530-c2a1-437f-9529-47b563692c5a", "collection": "rbi_circular"}
2025-08-02 19:50:29,081 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:50:29,081 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:50:29,082 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:29,082 - manual_test - INFO - 🆔 CHUNK UUID: e05cd530-c2a1-437f-9529-47b563692c5a
2025-08-02 19:50:29,082 - manual_test - INFO - 🆔 CHUNK UUID: e05cd530-c2a1-437f-9529-47b563692c5a
2025-08-02 19:50:29,082 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 2
2025-08-02 19:50:29,082 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 2
2025-08-02 19:50:29,082 - manual_test - INFO -    ✅ Chunk 2 added to processing queue
2025-08-02 19:50:29,082 - manual_test - INFO -    ✅ Chunk 2 added to processing queue
2025-08-02 19:50:29,082 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 3/3
2025-08-02 19:50:29,082 - manual_test - INFO - 🔍 [DEBUG] Processing chunk 3/3
2025-08-02 19:50:29,082 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 38710 chars
2025-08-02 19:50:29,082 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 38710 chars
2025-08-02 19:50:29,082 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:50:29,082 - manual_test - INFO - 🔍 [DEBUG] Starting LLM classification of chunk
2025-08-02 19:50:29,082 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:50:29,082 - manual_test - INFO - 🔍 [DEBUG] Context: {"notification_title": "Master Circular - Guarantees  Co-Acceptances   Letters Of Credit - Ucbs", "notification_date": ""}
2025-08-02 19:50:29,082 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 38710 chars
2025-08-02 19:50:29,082 - manual_test - INFO - 🔍 [DEBUG] Chunk content length: 38710 chars
2025-08-02 19:50:29,083 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:50:29,083 - manual_test - INFO - 🔍 [DEBUG] Successfully got schema from DocumentClassificationModel
2025-08-02 19:50:29,083 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:50:29,083 - manual_test - INFO - 🔍 [DEBUG] Sending LLM request for classification
2025-08-02 19:50:29,083 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o for document_classification - Selected medium tier for document_classification (context: 5883, priority: balanced)
2025-08-02 19:50:29,083 - manual_test - INFO - 🧠 Smart Model Selection: gpt-4o for document_classification - Selected medium tier for document_classification (context: 5883, priority: balanced)
2025-08-02 19:50:35,857 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:50:35,860 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:50:35,860 - manual_test - INFO - 🔍 [DEBUG] Received LLM response for classification
2025-08-02 19:50:35,860 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular that consolidates va...
2025-08-02 19:50:35,860 - manual_test - INFO - 🔍 [DEBUG] Response preview: {
  "classification": "regulatory",
  "should_index": true,
  "confidence": "high",
  "target_collection": "rbi_master_circular",
  "reasoning": "The document is a Master Circular that consolidates va...
2025-08-02 19:50:35,860 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:50:35,860 - manual_test - INFO - 🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel
2025-08-02 19:50:35,861 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:50:35,861 - manual_test - INFO -    🤖 LLM Classification: regulatory (index: True, confidence: high)
2025-08-02 19:50:35,861 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular that consolidates various guidelines and regulatory requirements related to guarantees, co-acceptances, and letters of credit for Urban Cooperative Banks (UCBs). It provides detailed instructions on compliance obligations, exposure norms, and safeguards that banks must follow. The content is regulatory in nature, as it outlines specific requirements and procedures that banks need to adhere to, making it essential for compliance understanding. Therefore, it should be indexed in the knowledge base under the 'rbi_master_circular' collection to ensure accessibility for compliance purposes."}
2025-08-02 19:50:35,861 - manual_test - INFO - 🔍 [DEBUG] Complete classification result: {"classification": "regulatory", "should_index": true, "confidence": "high", "target_collection": "rbi_master_circular", "reasoning": "The document is a Master Circular that consolidates various guidelines and regulatory requirements related to guarantees, co-acceptances, and letters of credit for Urban Cooperative Banks (UCBs). It provides detailed instructions on compliance obligations, exposure norms, and safeguards that banks must follow. The content is regulatory in nature, as it outlines specific requirements and procedures that banks need to adhere to, making it essential for compliance understanding. Therefore, it should be indexed in the knowledge base under the 'rbi_master_circular' collection to ensure accessibility for compliance purposes."}
2025-08-02 19:50:35,862 - manual_test - INFO - ✅ Chunk 3 classified as regulatory - INDEXING
2025-08-02 19:50:35,862 - manual_test - INFO - ✅ Chunk 3 classified as regulatory - INDEXING
2025-08-02 19:50:35,862 - root - INFO - Sending structured request to extract document actions
2025-08-02 19:50:50,015 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 19:50:50,024 - root - INFO - Received response type: <class '__main__.DocumentActionsList'>
2025-08-02 19:50:50,024 - root - INFO - ✅ Successfully received structured Pydantic output
2025-08-02 19:50:50,024 - manual_test - INFO - 🎯 Executing 4 actions for chunk 3
2025-08-02 19:50:50,024 - manual_test - INFO - 🎯 Executing 4 actions for chunk 3
2025-08-02 19:50:50,024 - manual_test - INFO - 🎯 Executing 4 chunk-specific actions
2025-08-02 19:50:50,024 - manual_test - INFO - 🎯 Executing 4 chunk-specific actions
2025-08-02 19:50:50,024 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-02 19:50:50,024 - manual_test - INFO - 📝 Processing Pydantic DocumentAction objects
2025-08-02 19:50:50,025 - manual_test - INFO - 📝 Sample action: {'document_id': 'UBD.No.Plan.(PCB)49/09.27.00/96-97', 'action_type': 'UPDATE_DOCUMENT', 'confidence': 'high', 'document_title': 'Payment under bank guarantee - Immediate settlement of cases', 'document_url': None, 'reference_number': None, 'department': 'UBD', 'original_date': '1997-04-26', 'update_location': 'section 1.4', 'sunset_withdraw_date': None, 'reasoning': 'The document outlines the immediate settlement of cases under bank guarantees, which is reiterated in the current notification.'}
2025-08-02 19:50:50,025 - manual_test - INFO - 📝 Sample action: {'document_id': 'UBD.No.Plan.(PCB)49/09.27.00/96-97', 'action_type': 'UPDATE_DOCUMENT', 'confidence': 'high', 'document_title': 'Payment under bank guarantee - Immediate settlement of cases', 'document_url': None, 'reference_number': None, 'department': 'UBD', 'original_date': '1997-04-26', 'update_location': 'section 1.4', 'sunset_withdraw_date': None, 'reasoning': 'The document outlines the immediate settlement of cases under bank guarantees, which is reiterated in the current notification.'}
2025-08-02 19:50:50,025 - manual_test - INFO - ▶️ Processing action 1/4
2025-08-02 19:50:50,025 - manual_test - INFO - ▶️ Processing action 1/4
2025-08-02 19:50:50,025 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_UBD_No_Plan__PC_chunk2_325b8af5
2025-08-02 19:50:50,025 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_UBD_No_Plan__PC_chunk2_325b8af5
2025-08-02 19:50:50,025 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for UBD.No.Plan.(PCB)49/09.27.00/96-97
2025-08-02 19:50:50,025 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for UBD.No.Plan.(PCB)49/09.27.00/96-97
2025-08-02 19:50:50,025 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: UBD.No.Plan.(PCB)49/09.27.00/96-97
2025-08-02 19:50:50,025 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: UBD.No.Plan.(PCB)49/09.27.00/96-97
2025-08-02 19:50:50,025 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:50:50,025 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,025 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,025 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:50:50,025 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:50:50,255 - utils.metadata_vector_utils - INFO - Searching metadata_vectors for 'UBD.No.Plan.(PCB)49/09.27.00/96-97' in field 'document_id'
2025-08-02 19:50:50,304 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-02 19:50:50,304 - utils.metadata_vector_utils - INFO - Found 5 results for metadata search
2025-08-02 19:50:50,304 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:50:50,304 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5dd89363-59cc-4134-ada4-eeda877a51b5, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,304 - knowledge_base_executor - INFO - [QDRANT] Match: ID=729eb4c6-a6c3-4aed-9a25-aa597db91127, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,304 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9affc01a-813a-4ab4-afdc-e864c70dd44f, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,304 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ac5ffabf-b12b-4fc4-93be-9684c9047105, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,304 - knowledge_base_executor - INFO - [QDRANT] Match: ID=96f706da-38a1-4bc3-bd09-648ab7d128db, Score=0.7114, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,304 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.Plan.(PCB)49/09.27.00/96-97
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:50:50,305 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:50:50,305 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:50:50,305 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:20:50.305911"}
2025-08-02 19:50:50,305 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:50:50,306 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:50:50,311 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:50,311 - manual_test - WARNING - ⚠️ Action execution failed: None
2025-08-02 19:50:50,311 - manual_test - WARNING - ⚠️ Action execution failed: None
2025-08-02 19:50:50,311 - manual_test - INFO - ▶️ Processing action 2/4
2025-08-02 19:50:50,311 - manual_test - INFO - ▶️ Processing action 2/4
2025-08-02 19:50:50,311 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_UBD_No_Plan_42__chunk2_325b8af5
2025-08-02 19:50:50,311 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_UBD_No_Plan_42__chunk2_325b8af5
2025-08-02 19:50:50,311 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for UBD.No.Plan.42/09.27.00-93/94
2025-08-02 19:50:50,311 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for UBD.No.Plan.42/09.27.00-93/94
2025-08-02 19:50:50,311 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: UBD.No.Plan.42/09.27.00-93/94
2025-08-02 19:50:50,311 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: UBD.No.Plan.42/09.27.00-93/94
2025-08-02 19:50:50,311 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:50:50,311 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,311 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,311 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:50:50,311 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:50:50,352 - utils.metadata_vector_utils - INFO - Searching metadata_vectors for 'UBD.No.Plan.42/09.27.00-93/94' in field 'document_id'
2025-08-02 19:50:50,359 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-02 19:50:50,360 - utils.metadata_vector_utils - INFO - Found 5 results for metadata search
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] Match: ID=5dd89363-59cc-4134-ada4-eeda877a51b5, Score=0.7338, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] Match: ID=729eb4c6-a6c3-4aed-9a25-aa597db91127, Score=0.7338, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9affc01a-813a-4ab4-afdc-e864c70dd44f, Score=0.7338, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ac5ffabf-b12b-4fc4-93be-9684c9047105, Score=0.7338, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] Match: ID=96f706da-38a1-4bc3-bd09-648ab7d128db, Score=0.7338, Document=RPCD. No. Plan. BC.05 / 04.09.22/ 2006-07
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.Plan.42/09.27.00-93/94
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:50:50,360 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:50:50,361 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:50:50,361 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f', 'ac5ffabf-b12b-4fc4-93be-9684c9047105', '96f706da-38a1-4bc3-bd09-648ab7d128db']}
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 5dd89363-59cc-4134-ada4-eeda877a51b5
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 729eb4c6-a6c3-4aed-9a25-aa597db91127
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9affc01a-813a-4ab4-afdc-e864c70dd44f
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['5dd89363-59cc-4134-ada4-eeda877a51b5', '729eb4c6-a6c3-4aed-9a25-aa597db91127', '9affc01a-813a-4ab4-afdc-e864c70dd44f']...
2025-08-02 19:50:50,361 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:20:50.361576"}
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:50:50,361 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:50:50,362 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:50,362 - manual_test - WARNING - ⚠️ Action execution failed: None
2025-08-02 19:50:50,362 - manual_test - WARNING - ⚠️ Action execution failed: None
2025-08-02 19:50:50,362 - manual_test - INFO - ▶️ Processing action 3/4
2025-08-02 19:50:50,362 - manual_test - INFO - ▶️ Processing action 3/4
2025-08-02 19:50:50,362 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_UBD_No_POT_1_UB_chunk2_325b8af5
2025-08-02 19:50:50,362 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_UBD_No_POT_1_UB_chunk2_325b8af5
2025-08-02 19:50:50,362 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for UBD.No.POT.1/UB.58-92/3
2025-08-02 19:50:50,362 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for UBD.No.POT.1/UB.58-92/3
2025-08-02 19:50:50,362 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: UBD.No.POT.1/UB.58-92/3
2025-08-02 19:50:50,362 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: UBD.No.POT.1/UB.58-92/3
2025-08-02 19:50:50,362 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:50:50,362 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,362 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,362 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:50:50,362 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:50:50,399 - utils.metadata_vector_utils - INFO - Searching metadata_vectors for 'UBD.No.POT.1/UB.58-92/3' in field 'document_id'
2025-08-02 19:50:50,404 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-02 19:50:50,405 - utils.metadata_vector_utils - INFO - Found 5 results for metadata search
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] Match: ID=9a0db77f-d109-46ff-9dd5-42e6866482c2, Score=0.5587, Document=DBOD.PSBD.BC.88/16.13.100/2005-06
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] Match: ID=529482b7-61a4-4dbc-85f6-082d3171e0ea, Score=0.5521, Document=Ref.No.UBD.DS. 2 /13.02.00/2002-03
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b7aff217-917a-46f2-93ba-ecbc1a1eb565, Score=0.5448, Document=BP.BC.11/21.01.040/99-00
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] Match: ID=998c8368-4404-41db-a8ac-d02d9c525c94, Score=0.5448, Document=BP.BC.11/21.01.040/99-00
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] Match: ID=b4358508-03e0-4c1e-b893-bcede5fbbb79, Score=0.5448, Document=BP.BC.11/21.01.040/99-00
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.POT.1/UB.58-92/3
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79']}
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9a0db77f-d109-46ff-9dd5-42e6866482c2
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 529482b7-61a4-4dbc-85f6-082d3171e0ea
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b7aff217-917a-46f2-93ba-ecbc1a1eb565
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565']...
2025-08-02 19:50:50,405 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79']}
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9a0db77f-d109-46ff-9dd5-42e6866482c2
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 529482b7-61a4-4dbc-85f6-082d3171e0ea
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b7aff217-917a-46f2-93ba-ecbc1a1eb565
2025-08-02 19:50:50,405 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565']...
2025-08-02 19:50:50,406 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565', '998c8368-4404-41db-a8ac-d02d9c525c94', 'b4358508-03e0-4c1e-b893-bcede5fbbb79']}
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 9a0db77f-d109-46ff-9dd5-42e6866482c2
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 529482b7-61a4-4dbc-85f6-082d3171e0ea
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = b7aff217-917a-46f2-93ba-ecbc1a1eb565
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['9a0db77f-d109-46ff-9dd5-42e6866482c2', '529482b7-61a4-4dbc-85f6-082d3171e0ea', 'b7aff217-917a-46f2-93ba-ecbc1a1eb565']...
2025-08-02 19:50:50,406 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:20:50.406295"}
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:50:50,406 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:50,407 - manual_test - WARNING - ⚠️ Action execution failed: None
2025-08-02 19:50:50,407 - manual_test - WARNING - ⚠️ Action execution failed: None
2025-08-02 19:50:50,407 - manual_test - INFO - ▶️ Processing action 4/4
2025-08-02 19:50:50,407 - manual_test - INFO - ▶️ Processing action 4/4
2025-08-02 19:50:50,407 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_UBD_No_I_L_PCB__chunk2_325b8af5
2025-08-02 19:50:50,407 - manual_test - INFO - 📌 Generated action ID for Pydantic model: UPDATE_DOCUMENT_UBD_No_I_L_PCB__chunk2_325b8af5
2025-08-02 19:50:50,407 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for UBD.No.I&L/PCB/9/12.05.00/95-96
2025-08-02 19:50:50,407 - manual_test - INFO - 🔍 Validating Pydantic action: UPDATE_DOCUMENT for UBD.No.I&L/PCB/9/12.05.00/95-96
2025-08-02 19:50:50,407 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: UBD.No.I&L/PCB/9/12.05.00/95-96
2025-08-02 19:50:50,407 - manual_test - INFO - 🎯 Executing UPDATE_DOCUMENT action for Pydantic model with document_id: UBD.No.I&L/PCB/9/12.05.00/95-96
2025-08-02 19:50:50,407 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: UPDATE_DOCUMENT
2025-08-02 19:50:50,407 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,407 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,407 - knowledge_base_executor - INFO - [QDRANT] Action details: UPDATE_DOCUMENT
2025-08-02 19:50:50,407 - knowledge_base_executor - INFO - [QDRANT] Trying to find documents to update using vector search
2025-08-02 19:50:50,444 - utils.metadata_vector_utils - INFO - Searching metadata_vectors for 'UBD.No.I&L/PCB/9/12.05.00/95-96' in field 'document_id'
2025-08-02 19:50:50,454 - httpx - INFO - HTTP Request: POST http://localhost:6333/collections/metadata_vectors/points/search "HTTP/1.1 200 OK"
2025-08-02 19:50:50,454 - utils.metadata_vector_utils - INFO - Found 5 results for metadata search
2025-08-02 19:50:50,454 - knowledge_base_executor - INFO - [QDRANT] Found 5 potential documents to update via vector search
2025-08-02 19:50:50,454 - knowledge_base_executor - INFO - [QDRANT] Match: ID=3d5b542d-4758-4b8b-9adf-56b5ae78f253, Score=0.6118, Document=RPCD.No.RRB.BC.33/03.05.33(E)/2005-06
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] Match: ID=ac35390c-19c4-4447-b048-bb917acfa27f, Score=0.6118, Document=RPCD.No.RRB.BC.33/03.05.33(E)/2005-06
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] Match: ID=bfa6e812-0474-4eba-b9d1-25382c83aa14, Score=0.6081, Document=RPCD.No.RRB.BC.76/03.05.33(C)/2005-06
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] Match: ID=c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae, Score=0.6081, Document=RPCD.No.RRB.BC.76/03.05.33(C)/2005-06
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] Match: ID=05e1db56-5a24-45dd-8de5-570e08a4eea9, Score=0.6046, Document=RPCD.No.RRB.BC.76/03.05.34/96-97
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields']
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using target_document as document_id: UBD.No.I&L/PCB/9/12.05.00/95-96
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9']}
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d5b542d-4758-4b8b-9adf-56b5ae78f253
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ac35390c-19c4-4447-b048-bb917acfa27f
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = bfa6e812-0474-4eba-b9d1-25382c83aa14
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14']...
2025-08-02 19:50:50,455 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9']}
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d5b542d-4758-4b8b-9adf-56b5ae78f253
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ac35390c-19c4-4447-b048-bb917acfa27f
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = bfa6e812-0474-4eba-b9d1-25382c83aa14
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14']...
2025-08-02 19:50:50,455 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Starting filter construction for UPDATE_DOCUMENT
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Available action fields: ['action', 'action_type', 'action_id', 'target_document', 'details', 'priority', 'source_chunk', 'timestamp', 'new_document_url', 'rbi_page_url', 'collection_name', 'filter_fields', 'document_id']
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Processing filter_fields: {'_id': ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14', 'c4af4809-a6be-4b3e-88e9-80d2b6f3e7ae', '05e1db56-5a24-45dd-8de5-570e08a4eea9']}
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Adding list filter: _id = [5 items]
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = 3d5b542d-4758-4b8b-9adf-56b5ae78f253
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = ac35390c-19c4-4447-b048-bb917acfa27f
2025-08-02 19:50:50,455 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: List item sample: _id = bfa6e812-0474-4eba-b9d1-25382c83aa14
2025-08-02 19:50:50,456 - knowledge_base_executor - INFO - [QDRANT] 🔧 BUILD FILTER: Using HasIdCondition for point IDs: ['3d5b542d-4758-4b8b-9adf-56b5ae78f253', 'ac35390c-19c4-4447-b048-bb917acfa27f', 'bfa6e812-0474-4eba-b9d1-25382c83aa14']...
2025-08-02 19:50:50,456 - knowledge_base_executor - ERROR - [QDRANT] UPDATE error in rbi_circular: local variable 'HasIdCondition' referenced before assignment
2025-08-02 19:50:50,456 - knowledge_base_executor - INFO - [QDRANT] Action UPDATE_DOCUMENT executed with result: {"action": "UPDATE_DOCUMENT", "success": false, "details": "Failed to update documents in any collection. Errors: Failed to update in rbi_master_direction: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_master_circular: local variable 'HasIdCondition' referenced before assignment; Failed to update in rbi_circular: local variable 'HasIdCondition' referenced before assignment", "timestamp": "2025-08-02T14:20:50.456065"}
2025-08-02 19:50:50,456 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 0/1 actions succeeded
2025-08-02 19:50:50,456 - knowledge_base_executor - INFO - [QDRANT] Action 1: UPDATE_DOCUMENT - ❌ FAILED
2025-08-02 19:50:50,456 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:50,456 - manual_test - WARNING - ⚠️ Action execution failed: None
2025-08-02 19:50:50,456 - manual_test - WARNING - ⚠️ Action execution failed: None
2025-08-02 19:50:50,456 - manual_test - INFO - 📊 Chunk action execution: 0/4 successful
2025-08-02 19:50:50,456 - manual_test - INFO - 📊 Chunk action execution: 0/4 successful
2025-08-02 19:50:50,456 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 3:
2025-08-02 19:50:50,456 - manual_test - INFO - 🔍 [DEBUG] Creating KB action for chunk 3:
2025-08-02 19:50:50,457 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2025-26/11
2025-08-02 19:50:50,457 - manual_test - INFO - 🔍 [DEBUG] - Document ID: RBI/2025-26/11
2025-08-02 19:50:50,457 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2025-26/11
2025-08-02 19:50:50,457 - manual_test - INFO - 🔍 [DEBUG] - Short ID: RBI/2025-26/11
2025-08-02 19:50:50,457 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-02 19:50:50,457 - manual_test - INFO - 🔍 [DEBUG] - Target collection: rbi_circular
2025-08-02 19:50:50,457 - knowledge_base_executor - INFO - [QDRANT] ▶️ EXECUTING ACTION 1/1: ADD_DOCUMENT
2025-08-02 19:50:50,457 - knowledge_base_executor - INFO - ✅ Fixed new_document_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,457 - knowledge_base_executor - INFO - ✅ Used PDF link as rbi_page_url: https://rbi-docs-full-2025-08-05.s3.ap-south-1.amazonaws.com/Notifications/2025/Master Circular - Guarantees_ Co-acceptances _ Letters of Credit - UCBs.pdf
2025-08-02 19:50:50,457 - knowledge_base_executor - INFO - [QDRANT] Action details: ADD_DOCUMENT
2025-08-02 19:50:50,457 - knowledge_base_executor - INFO - [QDRANT] ADD: Collections=['rbi_circular'], PID=34a8f5ea-beaf-474b-8e3b-073bf3397f33, Document=None, Chunk=2
2025-08-02 19:50:50,457 - knowledge_base_executor - INFO - ✅ Using qdrant_manager.upsert_point for None with text: 8000 chars
2025-08-02 19:50:50,462 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular/exists "HTTP/1.1 200 OK"
2025-08-02 19:50:50,471 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:50:50,472 - qdrant_utils - INFO - 🔍 Collection rbi_circular vectors: size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:50:50,472 - qdrant_utils - INFO - 🔍 Collection rbi_circular sparse vectors: {'fast-sparse-bm25': SparseVectorParams(index=SparseIndexParams(full_scan_threshold=None, on_disk=None, datatype=None), modifier=<Modifier.IDF: 'idf'>)}
2025-08-02 19:50:50,472 - qdrant_utils - WARNING - Collection rbi_circular exists but uses hybrid/single format. Will work with existing.
2025-08-02 19:50:51,745 - openai_utils - INFO - Embedding 1 documents
2025-08-02 19:50:52,515 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-02 19:50:52,923 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:50:52,924 - qdrant_utils - INFO - Collection info for rbi_circular: vectors=size=3072 distance=<Distance.COSINE: 'Cosine'> hnsw_config=None quantization_config=None on_disk=None datatype=None multivector_config=None
2025-08-02 19:50:52,924 - qdrant_utils - INFO - Collection rbi_circular uses hybrid format (single dense + sparse)
2025-08-02 19:50:52,924 - qdrant_utils - INFO - Collection format: hybrid
2025-08-02 19:50:52,924 - qdrant_utils - INFO - ✅ Added dense vector with 3072 dimensions
2025-08-02 19:50:52,929 - httpx - INFO - HTTP Request: GET http://localhost:6333/collections/rbi_circular "HTTP/1.1 200 OK"
2025-08-02 19:50:52,929 - qdrant_utils - INFO - ✅ Added sparse vector 'fast-sparse-bm25' with 358 indices
2025-08-02 19:50:52,929 - qdrant_utils - WARNING - ⚠️ Skipping sparse vector 'splade' - not configured in collection schema
2025-08-02 19:50:52,929 - qdrant_utils - INFO - 🔍 Final vector keys for point 34a8f5ea-beaf-474b-8e3b-073bf3397f33: ['', 'fast-sparse-bm25']
2025-08-02 19:50:52,949 - httpx - INFO - HTTP Request: PUT http://localhost:6333/collections/rbi_circular/points?wait=true "HTTP/1.1 200 OK"
2025-08-02 19:50:52,950 - qdrant_utils - INFO - ✅ Using hybrid format for point 34a8f5ea-beaf-474b-8e3b-073bf3397f33
2025-08-02 19:50:52,950 - knowledge_base_executor - INFO - ✅ Successfully used qdrant_manager.upsert_point for None (ID: 34a8f5ea-beaf-474b-8e3b-073bf3397f33)
2025-08-02 19:50:52,950 - knowledge_base_executor - INFO - [QDRANT] Action ADD_DOCUMENT executed with result: {"action": "ADD_DOCUMENT", "success": true, "details": "Added document None (ID: 34a8f5ea-beaf-474b-8e3b-073bf3397f33) to collections: ['rbi_circular']", "timestamp": "2025-08-02T14:20:52.950596", "document_id": null, "uuid": "34a8f5ea-beaf-474b-8e3b-073bf3397f33", "collection": "rbi_circular"}
2025-08-02 19:50:52,950 - knowledge_base_executor - INFO - [QDRANT] Execution summary: 1/1 actions succeeded
2025-08-02 19:50:52,950 - knowledge_base_executor - INFO - [QDRANT] Action 1: ADD_DOCUMENT - ✅ SUCCESS
2025-08-02 19:50:52,951 - knowledge_base_executor - INFO - [QDRANT] All logs flushed to disk
2025-08-02 19:50:52,951 - manual_test - INFO - 🆔 CHUNK UUID: 34a8f5ea-beaf-474b-8e3b-073bf3397f33
2025-08-02 19:50:52,951 - manual_test - INFO - 🆔 CHUNK UUID: 34a8f5ea-beaf-474b-8e3b-073bf3397f33
2025-08-02 19:50:52,951 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 3
2025-08-02 19:50:52,951 - manual_test - INFO - 🔍 [DEBUG] KB action successfully created for chunk 3
2025-08-02 19:50:52,951 - manual_test - INFO -    ✅ Chunk 3 added to processing queue
2025-08-02 19:50:52,951 - manual_test - INFO -    ✅ Chunk 3 added to processing queue
2025-08-02 19:50:52,951 - manual_test - INFO - � Chunk Processing Summary:
2025-08-02 19:50:52,951 - manual_test - INFO - � Chunk Processing Summary:
2025-08-02 19:50:52,951 - manual_test - INFO -    - Total chunks: 3
2025-08-02 19:50:52,951 - manual_test - INFO -    - Total chunks: 3
2025-08-02 19:50:52,951 - manual_test - INFO -    - Indexed chunks: 3
2025-08-02 19:50:52,951 - manual_test - INFO -    - Indexed chunks: 3
2025-08-02 19:50:52,951 - manual_test - INFO -    - Skipped chunks: 0
2025-08-02 19:50:52,951 - manual_test - INFO -    - Skipped chunks: 0
2025-08-02 19:50:52,951 - manual_test - INFO -    - Chunks producing actions: 3
2025-08-02 19:50:52,951 - manual_test - INFO -    - Chunks producing actions: 3
2025-08-02 19:50:52,951 - manual_test - INFO - ✅ STREAMLINED PDF processing: 3 chunks, 6 actions executed
2025-08-02 19:50:52,951 - manual_test - INFO - ✅ STREAMLINED PDF processing: 3 chunks, 6 actions executed
2025-08-02 19:50:52,952 - manual_test - INFO - ✅ Notification 1/1 processed successfully - 0 documents added
2025-08-02 19:50:52,952 - manual_test - INFO - ✅ Notification 1/1 processed successfully - 0 documents added
2025-08-02 19:50:52,952 - manual_test - INFO - 
2025-08-02 19:50:52,952 - manual_test - INFO - 
2025-08-02 19:50:52,952 - manual_test - INFO - 🎯 ═══════════════════════════════════════════════════════════════
2025-08-02 19:50:52,952 - manual_test - INFO - 🎯 ═══════════════════════════════════════════════════════════════
2025-08-02 19:50:52,952 - manual_test - INFO - 🎯 FINAL ENHANCED PIPELINE TEST SUMMARY
2025-08-02 19:50:52,952 - manual_test - INFO - 🎯 FINAL ENHANCED PIPELINE TEST SUMMARY
2025-08-02 19:50:52,952 - manual_test - INFO - 🎯 ═══════════════════════════════════════════════════════════════
2025-08-02 19:50:52,952 - manual_test - INFO - 🎯 ═══════════════════════════════════════════════════════════════
2025-08-02 19:50:52,952 - manual_test - INFO - 📊 Notifications Processed: 1
2025-08-02 19:50:52,952 - manual_test - INFO - 📊 Notifications Processed: 1
2025-08-02 19:50:52,952 - manual_test - INFO - ✅ Successful: 1
2025-08-02 19:50:52,952 - manual_test - INFO - ✅ Successful: 1
2025-08-02 19:50:52,952 - manual_test - INFO - ❌ Failed: 0
2025-08-02 19:50:52,952 - manual_test - INFO - ❌ Failed: 0
2025-08-02 19:50:52,952 - manual_test - INFO - 🆔 Total Documents Added: 0
2025-08-02 19:50:52,952 - manual_test - INFO - 🆔 Total Documents Added: 0
2025-08-02 19:50:52,952 - manual_test - INFO - 📝 Total Documents Updated: 0
2025-08-02 19:50:52,952 - manual_test - INFO - 📝 Total Documents Updated: 0
2025-08-02 19:50:52,952 - manual_test - INFO - ⚡ Total Operations: 0
2025-08-02 19:50:52,952 - manual_test - INFO - ⚡ Total Operations: 0
2025-08-02 19:50:52,952 - manual_test - INFO - 
2025-08-02 19:50:52,952 - manual_test - INFO - 
2025-08-02 19:50:52,952 - manual_test - INFO - 🆔 ALL ADDED DOCUMENT UUIDs (0):
2025-08-02 19:50:52,952 - manual_test - INFO - 🆔 ALL ADDED DOCUMENT UUIDs (0):
2025-08-02 19:50:52,952 - manual_test - INFO - 
2025-08-02 19:50:52,952 - manual_test - INFO - 
2025-08-02 19:50:52,952 - manual_test - INFO - 🎯 ═══════════════════════════════════════════════════════════════
2025-08-02 19:50:52,952 - manual_test - INFO - 🎯 ═══════════════════════════════════════════════════════════════
2025-08-02 19:50:52,954 - manual_test - INFO - ✅ Enhanced test completed! Results saved to real_pipeline_results_enhanced.json
2025-08-02 19:50:52,954 - manual_test - INFO - ✅ Enhanced test completed! Results saved to real_pipeline_results_enhanced.json
2025-08-02 19:50:52,954 - manual_test - INFO - 📊 Summary: 1 successful, 0 failed
2025-08-02 19:50:52,954 - manual_test - INFO - 📊 Summary: 1 successful, 0 failed
2025-08-02 19:50:52,954 - manual_test - INFO - 
2025-08-02 19:50:52,954 - manual_test - INFO - 
2025-08-02 19:50:52,954 - manual_test - INFO - 📋 DETAILED NOTIFICATION RESULTS:
2025-08-02 19:50:52,954 - manual_test - INFO - 📋 DETAILED NOTIFICATION RESULTS:
2025-08-02 19:50:52,954 - manual_test - INFO -   📋  1. Master Circular - Guarantees  Co-Acceptances   Let...
2025-08-02 19:50:52,954 - manual_test - INFO -   📋  1. Master Circular - Guarantees  Co-Acceptances   Let...
2025-08-02 19:50:52,954 - manual_test - INFO -        Status: success | KB: Process: True, Store: True | No UUIDs
2025-08-02 19:50:52,954 - manual_test - INFO -        Status: success | KB: Process: True, Store: True | No UUIDs
2025-08-02 19:50:52,954 - manual_test - INFO - 
2025-08-02 19:50:52,954 - manual_test - INFO - 
2025-08-02 19:50:52,954 - manual_test - INFO - 🎯 OVERALL UUID SUMMARY:
2025-08-02 19:50:52,954 - manual_test - INFO - 🎯 OVERALL UUID SUMMARY:
2025-08-02 19:50:52,955 - manual_test - INFO -    📈 Total Documents Added: 0
2025-08-02 19:50:52,955 - manual_test - INFO -    📈 Total Documents Added: 0
2025-08-02 19:50:52,955 - manual_test - INFO -    📝 Total Documents Updated: 0
2025-08-02 19:50:52,955 - manual_test - INFO -    📝 Total Documents Updated: 0
2025-08-02 19:50:52,955 - manual_test - INFO -    ⚡ Total Operations: 0
2025-08-02 19:50:52,955 - manual_test - INFO -    ⚡ Total Operations: 0
2025-08-02 19:50:52,955 - manual_test - INFO -    🆔 All UUIDs: 0
2025-08-02 19:50:52,955 - manual_test - INFO -    🆔 All UUIDs: 0
