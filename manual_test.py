"""
Real pipeline test that uses actual LLM processing functions.
This bypasses Airflow but uses the real notification processing logic.
"""

import json
import logging
import os
import re
import sys
import requests
import uuid
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel, ValidationError, Field

# Setup logging
try:
    from utils.logging_utils import setup_logging, force_flush_logs
    logger = setup_logging(log_dir="logs", module_name="manual_test", flush_interval=1)
    logger.info("📝 Pipeline execution log will be written to: logs/manual_test_execution.log")
except ImportError:
    log_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(log_formatter)
    pipeline_log_file = f"pipeline_execution_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    file_handler = logging.FileHandler(pipeline_log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(log_formatter)
    logging.basicConfig(level=logging.INFO, handlers=[console_handler, file_handler])
    logger = logging.getLogger(__name__)
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel, ValidationError, Field
import fitz
from sentence_transformers import SentenceTransformer
from utils.pdf_utils import has_diagonal_withdrawn_watermark as check_pdf_watermark
from qdrant_client import QdrantClient

from prompts.notification_categorizer import DocumentAction

# Define a Pydantic model for structured output from LLM
class DocumentActionsList(BaseModel):
    actions: List[DocumentAction] = Field(default_factory=list, 
                                        description="List of document actions extracted from the notification")

# Instead of manual handler setup, use the logging utility
try:
    from utils.logging_utils import setup_logging, force_flush_logs
    logger = setup_logging(log_dir="logs", module_name="manual_test", flush_interval=1)
    logger.info(f"📝 Pipeline execution log will be written to: logs/manual_test_execution.log")
except ImportError:
    # Fallback to previous setup
    log_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(log_formatter)
    pipeline_log_file = f"pipeline_execution_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    file_handler = logging.FileHandler(pipeline_log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(log_formatter)
    logging.basicConfig(level=logging.INFO, handlers=[console_handler, file_handler])
    logger = logging.getLogger(__name__)
    logger.info(f"📝 Pipeline execution log will be written to: {pipeline_log_file}")

# ----------------------------------------------------------------------------
# Watermark Detection Functions
# ----------------------------------------------------------------------------
def is_diagonal(bbox: fitz.Rect, tol: float = 0.3) -> bool:
    """
    Returns True if the bbox is roughly square (i.e. width ≈ height),
    which is a good heuristic for text rotated ~45° across the page.
    """
    w = bbox.x1 - bbox.x0
    h = bbox.y1 - bbox.y0
    if h == 0:
        return False
    ratio = w / h
    return abs(ratio - 1.0) < tol

def has_diagonal_withdrawn_watermark(
    pdf_path: str,
    size_threshold: float = 20.0,
    ratio_tolerance: float = 0.3
) -> str:
    """
    Open the PDF at pdf_path and scan every text‐span in rawdict mode.
    Return the specific keyword found if:
      1) the span's text, when stripped of whitespace, contains "withdrawn", "superseded", or "repealed"
      2) the span's font size is >= size_threshold
      3) the span's bbox is roughly square (diagonal orientation)
    
    Returns:
        str: The keyword found ("WITHDRAWN", "SUPERSEDED", "REPEALED") or "ACTIVE" if none found
    """
    try:
        doc = fitz.open(pdf_path)
        
        # Define the keywords to check for (in lowercase for comparison)
        keywords = ["withdrawn", "superseded", "repealed"]
        
        for pno, page in enumerate(doc, start=1):
            raw = page.get_text("rawdict")
            for block in raw["blocks"]:
                if block["type"] != 0:
                    continue
                for line in block["lines"]:
                    for span in line["spans"]:
                        # reconstruct the span text from per‐char data
                        text = "".join(ch["c"] for ch in span["chars"])
                        norm = text.lower().replace(" ", "").replace("\n", "")
                        
                        # Check if any of the keywords are present
                        keyword_found = None
                        for keyword in keywords:
                            if keyword in norm:
                                keyword_found = keyword
                                break
                        
                        if keyword_found is None:
                            continue

                        size = span["size"]
                        if size < size_threshold:
                            # too small (likely header/footer or normal text)
                            continue

                        if not is_diagonal(fitz.Rect(span["bbox"]), tol=ratio_tolerance):
                            # it's horizontal or near‐horizontal
                            continue

                        # if we made it here, it's a large, diagonal keyword
                        logger.info(f"→ Found diagonal '{keyword_found.capitalize()}' on page {pno} "
                              f"(size={size:.1f}, bbox={span['bbox']})")
                        doc.close()
                        return keyword_found.upper()  # Return WITHDRAWN, SUPERSEDED, or REPEALED
        
        doc.close()
        return "ACTIVE"  # No watermark found
    except Exception as e:
        logger.error(f"Error checking watermark in {pdf_path}: {e}")
        return "UNKNOWN"

# import KnowledgeBaseUpdateExecutor
from utils.knowledge_base_update_executor import KnowledgeBaseUpdateExecutor
from sklearn.feature_extraction.text import TfidfVectorizer

# Import KB tracking system
from kb_tracking_system import track_kb_action, complete_kb_action, get_kb_summary
from kb_dashboard import show_dashboard, show_summary


# Import the models from our schema module
from utils.llm_schemas import (
    DocumentClassificationModel, 
    KBActionModel, 
    NotificationEvaluationModel,
    DocumentClassification,
    CollectionName,
    ConfidenceLevel,
    ActionType,
    ProcessingType
)

# Legacy model - kept for backward compatibility
class LLMResponseModel(BaseModel):
    action_type: str
    classification: str
    should_store: bool
    should_remove: bool
    confidence: str
    target_collection: str
    reasoning: str


# Add the DAGs directory to Python path
dags_path = Path(__file__).parent / "complai_knowledge_tracker" / "airflow" / "dags"
sys.path.insert(0, str(dags_path))


from openai import OpenAI, RateLimitError
import time

from typing import Callable
import random

class SmartModelSelector:
    """
    Smart model selection based on task complexity and requirements
    """
    def __init__(self):
        self.model_tiers = {
            # Tier 1: Simple/Fast tasks - Basic pattern matching, simple classification
            'simple': {
                'llm': 'gpt-4o-mini',
                'embedding': 'all-MiniLM-L6-v2',  # 384 dimensions, fast
                'cost_per_1k_tokens': 0.00015,
                'max_context': 128000,
                'use_cases': ['chunk_classification', 'simple_extraction', 'basic_qa']
            },
            
            # Tier 2: Medium complexity - Document analysis, relationship detection
            'medium': {
                'llm': 'gpt-4o',
                'embedding': 'all-mpnet-base-v2',  # 768 dimensions, better quality
                'cost_per_1k_tokens': 0.0025,
                'max_context': 128000,
                'use_cases': ['document_analysis', 'relationship_detection', 'structured_extraction']
            },
            
            # Tier 3: Complex reasoning - Multi-step analysis, complex decision making
            'complex': {
                'llm': 'gpt-4.1',
                'embedding': 'all-mpnet-base-v2',  # High quality embeddings for complex tasks
                'cost_per_1k_tokens': 0.006,  # Estimated cost for GPT-4.1
                'max_context': 200000,  # Higher context window for GPT-4.1
                'use_cases': ['complex_reasoning', 'multi_step_analysis', 'expert_decisions']
            },
            
            # Tier 4: Specialized/Critical - Financial regulations, legal analysis
            'critical': {
                'llm': 'gpt-4.1',
                'embedding': 'sentence-transformers/all-roberta-large-v1',  # 1024 dimensions, highest quality
                'cost_per_1k_tokens': 0.006,  # Estimated cost for GPT-4.1
                'max_context': 200000,  # Higher context window for GPT-4.1
                'use_cases': ['regulatory_analysis', 'legal_reasoning', 'critical_decisions']
            }
        }
        
        # Task complexity mapping - Enhanced for GPT-4.1 capabilities
        self.task_complexity = {
            # Simple tasks - Can use fast, cost-effective models
            'chunk_indexing': 'simple',
            'basic_classification': 'simple',
            'metadata_embedding': 'simple',
            'simple_search': 'simple',
            'title_extraction': 'simple',
            
            # Medium complexity tasks - Benefit from GPT-4o quality
            'document_classification': 'medium',
            'kb_action_determination': 'medium',
            'document_relationship_analysis': 'medium',
            'affected_documents_extraction': 'medium',
            'notification_evaluation': 'medium',
            
            # Complex tasks - Leverage GPT-4.1 advanced reasoning
            'content_analysis': 'complex',
            'regulatory_interpretation': 'complex',
            'flowchart_based_actions': 'complex',
            'expert_workflow_simulation': 'complex',
            'multi_document_analysis': 'complex',
            'supersession_detection': 'complex',
            
            # Critical tasks - Use GPT-4.1 for highest accuracy
            'compliance_assessment': 'critical',
            'regulatory_compliance_check': 'critical',
            'supersession_analysis': 'critical',
            'consolidation_detection': 'critical',
            'removal_evidence_validation': 'critical',
            'legal_reasoning': 'critical'
        }
    
    def get_model_for_task(self, task_type: str, context_length: int = 0, 
                          priority: str = 'balanced') -> dict:
        """
        Get optimal model configuration for a specific task
        
        Args:
            task_type: Type of task being performed
            context_length: Expected context length in tokens
            priority: 'speed', 'cost', 'quality', or 'balanced'
        
        Returns:
            dict: Model configuration with llm, embedding, and reasoning
        """
        
        # Get base complexity
        complexity = self.task_complexity.get(task_type, 'medium')
        
        # Adjust based on context length
        if context_length > 64000:
            complexity = self._upgrade_complexity(complexity)
        elif context_length < 1000 and complexity != 'simple':
            complexity = self._downgrade_complexity(complexity)
        
        # Adjust based on priority
        if priority == 'speed' or priority == 'cost':
            complexity = self._downgrade_complexity(complexity)
        elif priority == 'quality':
            complexity = self._upgrade_complexity(complexity)
        
        model_config = self.model_tiers[complexity].copy()
        model_config['complexity'] = complexity
        model_config['reasoning'] = f"Selected {complexity} tier for {task_type} (context: {context_length}, priority: {priority})"
        
        return model_config
    
    def _upgrade_complexity(self, current: str) -> str:
        """Upgrade complexity tier"""
        upgrade_map = {
            'simple': 'medium',
            'medium': 'complex',
            'complex': 'critical',
            'critical': 'critical'  # Already at max
        }
        return upgrade_map.get(current, current)
    
    def _downgrade_complexity(self, current: str) -> str:
        """Downgrade complexity tier"""
        downgrade_map = {
            'critical': 'complex',
            'complex': 'medium',
            'medium': 'simple',
            'simple': 'simple'  # Already at min
        }
        return downgrade_map.get(current, current)

class PipeSeparatedKeyRotator:
    """
    Utility to rotate through pipe-separated OpenAI API keys and handle rate limits
    """
    def __init__(self, keys_str: str):
        self.keys = keys_str.split('|')
        self.current_index = 0
        self.current_key = self.keys[0]
        logger.info(f"Initialized key rotator with {len(self.keys)} keys")

    def rotate_key(self):
        """Rotate to next key"""
        self.current_index = (self.current_index + 1) % len(self.keys)
        self.current_key = self.keys[self.current_index]
        logger.info(f"Rotated to key {self.current_index + 1}/{len(self.keys)}")
        return self.current_key

    def call_with_rotation(self, callable_fn: Callable, *args, **kwargs) -> Any:
        """Call function with automatic key rotation on rate limits"""
        max_retries = len(self.keys)
        retries = 0
        
        while retries < max_retries:
            try:
                return callable_fn()
            except RateLimitError as e:
                retries += 1
                if retries < max_retries:
                    logger.warning(f"Rate limit hit, rotating key ({retries}/{max_retries} retries)")
                    self.rotate_key()
                else:
                    logger.error("All keys exhausted, rate limit persists")
                    raise
            except Exception as e:
                logger.error(f"Non-rate-limit error occurred: {e}")
                raise

class StandaloneConfig:
    """Standalone configuration that doesn't require Airflow Variables"""
    
    def __init__(self):
        # Set up OpenAI configuration from environment (support pipe-separated keys)
        self.openai_api_keys = os.getenv('OPENAI_API_KEYS') or os.getenv('OPENAI_API_KEY')
        if not self.openai_api_keys:
            raise ValueError("OPENAI_API_KEYS or OPENAI_API_KEY environment variable is required")
        
        # Mock other configurations
        self.qdrant_host = "localhost"
        self.database_uri = "mongodb://localhost:27017/"


class MockOpenAIManager:
    """OpenAI manager with key rotation and smart model selection"""
    def __init__(self, api_keys_str: str):
        self.key_rotator = PipeSeparatedKeyRotator(api_keys_str)
        self.model_selector = SmartModelSelector()
        self._openai_client = None
    
    def get_client(self):
        if not self._openai_client or self._openai_client.api_key != self.key_rotator.current_key:
            self._openai_client = OpenAI(api_key=self.key_rotator.current_key)
        return self._openai_client
    
    def with_key_rotation(self, func, *args, **kwargs):
        def wrapped_func():
            # Ensure client uses current key
            self._openai_client = OpenAI(api_key=self.key_rotator.current_key)
            return func()
        return self.key_rotator.call_with_rotation(wrapped_func)
    
    def get_completion(self, prompt: str, model: str = None, temperature: float = 0.1, 
                      response_format=None, task_type: str = 'basic_classification', 
                      priority: str = 'balanced') -> str:
        """
        Get completion with smart model selection
        """
        # Smart model selection if no specific model provided
        if model is None:
            context_length = len(prompt.split()) * 1.3  # Rough token estimate
            model_config = self.model_selector.get_model_for_task(
                task_type, int(context_length), priority
            )
            model = model_config['llm']
            logger.info(f"🧠 Smart Model Selection: {model} for {task_type} - {model_config['reasoning']}")
        
        def _make_completion():
            client = self.get_client()
            try:
                completion_args = {
                    "model": model,
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": temperature
                }
                
                # Add response_format if provided
                if response_format:
                    completion_args["response_format"] = response_format
                
                response = client.chat.completions.create(**completion_args)
                return response.choices[0].message.content
            except Exception as e:
                logger.error(f"Error in completion call: {e}")
                raise
        
        return self.with_key_rotation(_make_completion)

    def get_structured_output(self, prompt: str, response_format: Any, model: str = None,
                              temperature: float = 0.1, task_type: str = 'structured_extraction',
                              priority: str = 'balanced') -> dict:
        """
        Get structured output from LLM response, using OpenAI’s structured‑outputs API.
        `response_format` should follow the JSON‑schema format per docs.
        """
        if model is None:
            context_length = len(prompt.split()) * 1.3
            model_config = self.model_selector.get_model_for_task(task_type, int(context_length), priority)
            model = model_config['llm']
            logging.info(f"🧠 Smart Model Selection: {model} for {task_type}")

        def _call():
            client = self.get_client()
            return client.chat.completions.parse(
                model="gpt-4o",
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature,
                response_format=response_format
            )

        raw_resp = self.with_key_rotation(_call)
        choice = raw_resp.choices[0].message

        # If using SDK structured parsing (e.g. .parsed), return that directly if present
        if hasattr(choice, "parsed") and choice.parsed is not None:
            return choice.parsed

        # Otherwise, parse from content
        content = choice.content
        # strip code fences
        if content.startswith("```"):
            content = content.strip().strip("```json").strip("```").strip()

        try:
            return json.loads(content)
        except json.JSONDecodeError as e:
            logging.error("Failed to parse structured output", exc_info=True)
            raise ValueError(f"Invalid structured output: {e}\nRaw content:\n{content}")

        
        def _make_completion():
            client = self.get_client()
            try:
                response = client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": prompt}],
                    temperature=temperature
                )
                return response.choices[0].message.content
            except Exception as e:
                logger.error(f"Error in completion call: {e}")
                raise

        raw_response = self.with_key_rotation(_make_completion)

        try:
            # Parse the raw response into structured JSON
            structured_response = json.loads(raw_response)
            return structured_response
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing structured output: {e}")
            raise ValueError("Failed to parse structured output from LLM response")


class RealPipelineTest:
    """Test the real notification processing pipeline"""
    
    def __init__(self, notifications_file: str = "rbi_notifications.json"):
        self.notifications_file = Path(notifications_file)
        self.notifications = []
        self.results = []
        
        # Set up standalone configuration
        self.setup_environment()
        
        # Import and initialize the real notification processor
        self.setup_notification_processor()

        # Initialize KnowledgeBaseUpdateExecutor
        self.knowledge_base_executor = KnowledgeBaseUpdateExecutor()
        
        # Initialize smart model selector
        self.model_selector = SmartModelSelector()
        
        # Initialize metadata vector search with smart embedding selection
        try:
            from utils.metadata_vector_utils import MetadataVectorSearch
            self.metadata_search = MetadataVectorSearch()
            logger.info("✅ Metadata vector search initialized")
            
            # Initialize smart embedding model selection for metadata search
            embedding_config = self.model_selector.get_model_for_task('metadata_embedding', priority='speed')
            embedding_model = embedding_config['embedding']
            logger.info(f"🧠 Selected embedding model for metadata: {embedding_model}")
            
            self.metadata_search_model = SentenceTransformer(embedding_model)
            self.qdrant_client = QdrantClient(url=self.config.qdrant_host or "http://localhost:6333")
            self.metadata_collection = "metadata_vectors"  # Default collection name for metadata
            logger.info("✅ Direct metadata search components initialized")
        except Exception as e:
            logger.warning(f"⚠️ Could not initialize metadata vector search: {e}")
            self.metadata_search = None
            self.metadata_search_model = None
            self.qdrant_client = None
    
    def setup_environment(self):
        """Set up the environment for standalone testing"""
        try:
            # Check for OpenAI API key (support both OPENAI_API_KEYS and OPENAI_API_KEY)
            if not (os.getenv('OPENAI_API_KEYS') or os.getenv('OPENAI_API_KEY')):
                raise ValueError(
                    "OPENAI_API_KEYS or OPENAI_API_KEY environment variable is required. "
                    "Please set it with: export OPENAI_API_KEYS='key1|key2|key3' or export OPENAI_API_KEY='your-key-here'"
                )
            
            # Create standalone config
            self.config = StandaloneConfig()
            
            # Initialize the OpenAI manager with key rotation
            self.openai_manager = MockOpenAIManager(self.config.openai_api_keys)
            
            logger.info("✅ Environment setup complete")
            
        except Exception as e:
            logger.error(f"❌ Environment setup failed: {e}")
            raise
    
    def setup_notification_processor(self):
        """Set up the real notification processor"""
        try:
            # Mock the config module to avoid Airflow dependency
            import sys
            from unittest.mock import MagicMock
            
            # Create mock config
            mock_config = MagicMock()
            mock_config.openai.get_next_key.return_value = self.config.openai_api_keys
            
            # Replace the config import
            sys.modules['utils.config'] = MagicMock()
            sys.modules['utils.config'].config = mock_config
            
            # Use the centralized openai_manager instead of creating a new one
            sys.modules['utils.openai_utils'] = MagicMock()
            sys.modules['utils.openai_utils'].openai_manager = self.openai_manager
            
            # Enhanced chunk-level prompt templates
            global NOTIFICATION_CATEGORIZER_PROMPT
            global AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT
            global UPDATE_ACTION_DETERMINER_PROMPT
            NOTIFICATION_CATEGORIZER_PROMPT = (
                """
                You are an expert RBI regulatory analyst. You will receive a notification and its extracted PDF content, broken into chunks with metadata (positions, section, page, reference numbers).
                For each chunk, analyze and categorize its regulatory impact, affected documents, and required knowledge base updates. Propagate all chunk metadata in your output.

                Only use one of: rbi_circular, rbi_master_circular, rbi_master_direction, rbi_other for any collection fields.
                Output must be structured JSON. For each document/action, include a 'collection' field with one of the allowed values.

                Notification Title: {title}
                Notification Content: {content}
                Notification Link: {link}

                For each chunk, provide:
                - Chunk Index and Metadata
                - Category (regulatory, informational, withdrawn, etc.)
                - Confidence
                - Reasoning
                - Affected Documents (with collection field)
                - Required KB Update Actions (with collection field)
                """
            )
            AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT = (
                """
                You are an expert in RBI regulatory document analysis and knowledge base management. You will receive a notification and its PDF chunks with metadata.
                For each chunk, extract the specific documents affected, referencing chunk metadata (positions, section, page, reference numbers).

                **CRITICAL SUPERSESSION DETECTION**: Pay special attention to identifying documents that may supersede or replace existing documents:
                1. If this notification contains terms like "Amendment", "Revised", "Updated", or a year (2025), check if older versions exist
                2. Look for phrases like "supersedes", "replaces", "amends", "revises", "hereby repealed"
                3. If the document title is similar to existing documents but with different years (2024, 2023, etc.), mark older versions for REMOVAL
                4. For identical document content published in different years, the NEWEST version should supersede ALL older versions

                **DOCUMENT VERSIONING RULES**:
                - Same document title with year 2025 → Remove versions from 2024, 2023, 2022, 2021, 2020
                - Documents with identical RBI codes but different years → Remove older versions  
                - Amendment notifications → Remove the original document being amended
                - Revised guidelines → Remove previous version of the same guidelines

                **MANDATORY DOCUMENT ID EXTRACTION**:
                - Look for RBI reference numbers (e.g., RBI/FED/2024-25/17, DBR.No.Ret.BC.78/12.02.001/2024-25)
                - Extract document titles if no reference number found
                - Use notification codes or circular numbers as fallback
                - Only use "MANUAL_REVIEW_REQUIRED" if absolutely no identifier can be found

                **MANDATORY URL EXTRACTION**:
                - Extract new_document_url from PDF links in the notification (e.g., https://rbidocs.rbi.org.in/rdocs/notification/PDFs/...)
                - Extract notification_pdf_url from the notification source
                - If URLs are mentioned in the content, extract them
                - If no URLs found in content, mark requires_manual_review=true

                Only use one of: rbi_circular, rbi_master_circular, rbi_master_direction, rbi_other for any collection fields.
                Output must be structured JSON. For each affected document, include a 'collection' field with one of the allowed values.

                Title: {title}
                Content: {content}
                Category: {category}
                Document Codes: {notification_codes}

                For each affected document, specify:
                - Document type
                - Collection (must be one of the allowed values)
                - Specific document identifier or reference
                - Type of impact (update, supersede, reference, REMOVE_SUPERSEDED, etc.)
                - Action required (create, update, delete, link, REMOVE_OLDER_VERSION)
                - superseded_by: (if marking for removal, what document supersedes it)
                - supersession_reason: (why this document should be removed)

                **MANDATORY SUPERSESSION CHECK**: For every notification, explicitly check if it supersedes older versions and include removal actions for those older documents.
                **MANDATORY URL EXTRACTION**: Always attempt to extract new_document_url and notification_pdf_url from the content.

                Output a list of document actions per chunk, as structured JSON, including all relevant metadata and target collections.
                """
            )
            UPDATE_ACTION_DETERMINER_PROMPT = (
                """
                You are an expert in knowledge base management for RBI regulations. Analyze the notification and determine specific update actions.

                **RESPONSE CONSTRAINTS**:
                - Keep response CONCISE and focused
                - Maximum 3-5 actions per notification
                - Brief reasoning (1-2 sentences max)
                - No extensive explanations or examples

                **CRITICAL RULES**:
                - REMOVE_DOCUMENT actions: target_document MUST be valid document ID (e.g., "RBI/FED/2024-25/17")
                - If no clear document ID found, try to extract from document title or reference number
                - Only use "MANUAL_REVIEW_REQUIRED" as absolute last resort
                - Extract URLs from content exactly as they appear
                - Collections: rbi_circular, rbi_master_circular, rbi_master_direction, rbi_other only

                Title: {title}
                Category: {category}
                Affected Documents: {affected_documents}
                Content: {content}

                **OUTPUT**: Return UpdateActionResult with actions array. Each action needs:
                - action_type (required)
                - target_document (required for REMOVE_DOCUMENT)
                - details (brief, max 50 words)
                - priority (HIGH/MEDIUM/LOW)
                - new_document_url (if found in content)
                - rbi_page_url (if found in content)

                **IMPORTANT**: Keep response under 1000 tokens. Be concise and specific.
                """
            )
            # Import the notification processor models
            from prompts.notification_categorizer import (
                NotificationCategorizationResult,
                AffectedDocumentsResult,
                UpdateActionResult
            )
            
            # Create the real notification processor class
            class RealNotificationProcessor:
                def __init__(self, openai_manager: MockOpenAIManager):
                    self.openai_manager = openai_manager
                
                def _make_llm_call(self, prompt: str, response_format, system_message: str = "You are an expert RBI regulatory analyst."):
                    """Make a structured LLM call with error handling and key rotation"""
                    def _call_llm():
                        client = self.openai_manager.get_client()
                        logger.info(f"Making LLM call with model: gpt-4.1")
                        response = client.beta.chat.completions.parse(
                            model="gpt-4.1",
                            messages=[
                                {"role": "system", "content": system_message},
                                {"role": "user", "content": prompt}
                            ],
                            temperature=0.1,
                            max_tokens=4000,  # Limit response to prevent length issues
                            response_format=response_format
                        )
                        # Convert to dict
                        result = response.choices[0].message.parsed
                        return result.model_dump() if hasattr(result, 'model_dump') else result.__dict__
                    
                    try:
                        return self.openai_manager.with_key_rotation(_call_llm)
                    except Exception as e:
                        logger.error(f"LLM call failed: {e}")
                        raise
                
                def analyze_notification(self, title: str, rss_description: str, link: str) -> dict:
                    """Categorize RBI notification and determine its impact on knowledge base.
                    
                    Args:
                        title: The notification title
                        rss_description: The RSS feed description/content
                        link: URL of the notification
                        
                    Returns:
                        dict: Analysis result containing category, confidence, and reasoning
                    """
                    try:
                        logger.info(f"🔍 Starting notification analysis for: {title[:50]}...")
                        logger.info(f"📏 Notification length: {len(rss_description)} characters")

                        # Add reminder about enclosed directions in attached PDFs
                        enhanced_content = rss_description
                        if any(word in rss_description.lower() for word in ["attached", "enclosed", "herewith"]):
                            enhanced_content += "\n\n=== IMPORTANT NOTE ===\n"
                            enhanced_content += "If this notification mentions enclosed directions, guidelines, "
                            enhanced_content += "or regulations, please note that the enclosed direction/document "
                            enhanced_content += "is typically found in the attached PDF on the same notification page."

                        prompt = NOTIFICATION_CATEGORIZER_PROMPT.format(
                            title=title,
                            content=enhanced_content,
                            link=link
                        )

                        logger.info("📝 Prompt created, making LLM call...")
                        result = self._make_llm_call(prompt, NotificationCategorizationResult)
                        logger.info(f"✅ Notification categorized as: {result.get('category', 'Unknown')} "
                                  f"(confidence: {result.get('confidence', 'N/A')})")
                        
                        return result

                    except Exception as e:
                        logger.error(f"Error analyzing notification: {e}")
                        return self._get_fallback_analysis()
                
                def extract_affected_documents(self, title: str, content: str, category: str, notification_codes: dict = None, chunks: list = None) -> dict:
                    """Extract specific documents affected by the notification, including supersession detection"""
                    try:
                        logger.info(f"🔍 Extracting affected documents for: {title[:50]}...")
                        logger.info(f"📏 Notification length: {len(content)} characters")

                        # Include notification codes for better supersession detection
                        codes_info = json.dumps(notification_codes or {}, indent=2)
                        
                        all_document_actions = []
                        
                        # First analyze the main content
                        prompt = AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT.format(
                            title=title,
                            content=content,
                            category=category,
                            notification_codes=codes_info
                        )
                        
                        main_result = self._make_llm_call(
                            prompt,
                            AffectedDocumentsResult,
                            "You are an expert in RBI regulatory document analysis and knowledge base management with supersession detection."
                        )
                        
                        all_document_actions.extend(main_result.get('document_actions', []))
                        
                        # Process additional chunks if available
                        if chunks:
                            logger.info(f"🔍 Analyzing {len(chunks)} additional chunks for affected documents...")
                            chunk_results = []
                            successful_chunks = 0
                            
                            for idx, chunk in enumerate(chunks):
                                chunk_content = chunk.get('content', '') if isinstance(chunk, dict) else str(chunk)
                                if not chunk_content:
                                    logger.warning(f"⚠️ Skipping empty chunk {idx+1}")
                                    continue
                                
                                logger.info(f"📄 Processing chunk {idx+1}/{len(chunks)}")
                                
                                try:
                                    # Create chunk-specific prompt
                                    chunk_prompt = AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT.format(
                                        title=f"{title} (Chunk {idx+1}/{len(chunks)})",
                                        content=chunk_content,
                                        category=category,
                                        notification_codes=codes_info
                                    )
                                    
                                    # Process chunk
                                    chunk_result = self._make_llm_call(
                                        chunk_prompt,
                                        AffectedDocumentsResult,
                                        "You are an expert in RBI regulatory document analysis and knowledge base management with supersession detection."
                                    )
                                    
                                    # Enrich actions with chunk context
                                    chunk_actions = chunk_result.get('document_actions', [])
                                    for action in chunk_actions:
                                        action['source_chunk'] = {
                                            'index': idx,
                                            'total_chunks': len(chunks),
                                            'chunk_length': len(chunk_content),
                                            'content_preview': chunk_content[:100] + '...'
                                        }
                                    
                                    # Store result
                                    chunk_results.append({
                                        'chunk_index': idx,
                                        'actions': chunk_actions,
                                        'content_length': len(chunk_content),
                                        'content_preview': chunk_content[:200] + '...'
                                    })
                                    
                                    # Add to main collection
                                    all_document_actions.extend(chunk_actions)
                                    successful_chunks += 1
                                    logger.info(f"✅ Found {len(chunk_actions)} actions in chunk {idx+1}")
                                    
                                except Exception as e:
                                    logger.error(f"❌ Failed to process chunk {idx+1}: {str(e)}")
                            
                            # Summarize chunk processing
                            logger.info(f"📊 Chunk Processing Summary:")
                            logger.info(f"   - Successfully processed: {successful_chunks}/{len(chunks)} chunks")
                            logger.info(f"   - Chunks with actions: {len([c for c in chunk_results if c['actions']])}")
                            logger.info(f"   - Total actions from chunks: {sum(len(c['actions']) for c in chunk_results)}")
                            
                            # Store detailed results for debugging
                            with open("chunk_processing_results.json", "w", encoding='utf-8') as f:
                                json.dump({
                                    'summary': {
                                        'total_chunks': len(chunks),
                                        'successful_chunks': successful_chunks,
                                        'total_actions': sum(len(c['actions']) for c in chunk_results)
                                    },
                                    'chunks': chunk_results
                                }, f, indent=2, ensure_ascii=False)
                            
                            logger.info(f"📊 Chunk Processing Summary:")
                            logger.info(f"   - Total chunks processed: {len(chunks)}")
                            logger.info(f"   - Chunks with actions: {len([c for c in chunk_results if c['actions']])}")
                            logger.info(f"   - Total actions found in chunks: {sum(len(c['actions']) for c in chunk_results)}")
                            
                            # Store chunk results for debugging
                            with open("chunk_processing_results.json", "w") as f:
                                json.dump(chunk_results, f, indent=2)
                            
                            for idx, chunk in enumerate(chunks):
                                chunk_content = chunk.get('content', '')
                                if not chunk_content:
                                    continue
                                    
                                logger.info(f"📄 Processing chunk {idx+1}/{len(chunks)}")
                                chunk_prompt = AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT.format(
                                    title=title,
                                    content=chunk_content,
                                    category=category,
                                    notification_codes=codes_info
                                )
                                
                                try:
                                    chunk_result = self._make_llm_call(
                                        chunk_prompt,
                                        AffectedDocumentsResult,
                                        "You are an expert in RBI regulatory document analysis and knowledge base management with supersession detection."
                                    )
                                    chunk_actions = chunk_result.get('document_actions', [])
                                    logger.info(f"✅ Found {len(chunk_actions)} actions in chunk {idx+1}")
                                    all_document_actions.extend(chunk_actions)
                                except Exception as e:
                                    logger.warning(f"⚠️ Error processing chunk {idx+1}: {e}")
                                    continue

                        result = self._make_llm_call(
                            prompt,
                            AffectedDocumentsResult,
                            "You are an expert in RBI regulatory document analysis and knowledge base management with supersession detection."
                        )
                        
                        # Enhanced deduplication with smart merging
                        action_groups = {}
                        
                        for action in all_document_actions:
                            action_key = (
                                action.get('target_document', ''),
                                action.get('action_required', ''),
                                action.get('type_of_impact', '')
                            )
                            
                            if action_key in action_groups:
                                # Merge additional information from this action
                                existing = action_groups[action_key]
                                
                                # Merge URLs if found
                                if action.get('new_document_url') and not existing.get('new_document_url'):
                                    existing['new_document_url'] = action['new_document_url']
                                if action.get('rbi_page_url') and not existing.get('rbi_page_url'):
                                    existing['rbi_page_url'] = action['rbi_page_url']
                                
                                # Track which chunks contributed to this action
                                if 'source_chunks' not in existing:
                                    existing['source_chunks'] = []
                                if 'source_chunk' in action:
                                    existing['source_chunks'].append(action['source_chunk'])
                                
                                # Combine unique reasoning
                                if action.get('reasoning'):
                                    existing_reasons = set(existing.get('reasoning', '').split('. '))
                                    new_reasons = set(action['reasoning'].split('. '))
                                    all_reasons = existing_reasons.union(new_reasons)
                                    existing['reasoning'] = '. '.join(filter(None, all_reasons))
                            else:
                                # First time seeing this action
                                action_groups[action_key] = action.copy()
                                if 'source_chunk' in action:
                                    action_groups[action_key]['source_chunks'] = [action['source_chunk']]
                        
                        # Convert grouped actions to list
                        deduplicated_actions = list(action_groups.values())
                        
                        # Create final result with deduplicated actions
                        final_result = {
                            'document_actions': deduplicated_actions,
                            'document_keywords': list(set(action.get('document_type', '') for action in deduplicated_actions)),
                            'has_new_document_link': any(action.get('new_document_url') for action in deduplicated_actions),
                            'new_document_url': next((action.get('new_document_url') for action in deduplicated_actions if action.get('new_document_url')), ''),
                            'rbi_links': list(set(action.get('rbi_page_url', '') for action in deduplicated_actions if action.get('rbi_page_url'))),
                            'processing_notes': f"Processed {len(chunks) if chunks else 0} additional chunks, found {len(all_document_actions)} total actions, deduplicated to {len(deduplicated_actions)} actions"
                        }
                        
                        # Enhanced logging for supersession detection
                        removal_actions = len([action for action in deduplicated_actions 
                                             if action.get('action_required') in ['delete', 'REMOVE_OLDER_VERSION'] 
                                             or action.get('type_of_impact') == 'REMOVE_SUPERSEDED'])
                        
                        logger.info(f"✅ Found {len(deduplicated_actions)} unique document actions ({removal_actions} removals for supersession)")
                        logger.info(f"📊 Action reduction: {len(all_document_actions)} → {len(deduplicated_actions)} after deduplication")
                        
                        # write doc actions to a file for debugging
                        with open("debug_affected_documents.json", "w", encoding="utf-8") as f:
                            json.dump(final_result, f, indent=2, ensure_ascii=False)
                        return final_result

                    except Exception as e:
                        logger.error(f"Error extracting affected documents: {e}")
                        return self._get_fallback_affected_documents()
                
                def determine_update_actions(self, title: str, category: str, affected_documents: list, rss_description: str, notification_data: dict = None) -> dict:
                    """Determine specific actions needed for knowledge base updates"""
                    try:
                        logger.info(f"🎯 Determining update actions for: {title[:50]}...")
                        logger.info(f"📏 Notification length: {len(rss_description)} characters")

                        # Truncate content if too long to prevent token limit issues
                        max_content_length = 8000  # Conservative limit
                        truncated_content = rss_description[:max_content_length]
                        if len(rss_description) > max_content_length:
                            logger.warning(f"⚠️ Content truncated from {len(rss_description)} to {max_content_length} chars to prevent token limit")
                            truncated_content += "\n\n[CONTENT TRUNCATED - Analysis based on first 8000 characters]"

                        prompt = UPDATE_ACTION_DETERMINER_PROMPT.format(
                            title=title,
                            category=category,
                            affected_documents=json.dumps(affected_documents, indent=2),
                            content=truncated_content
                        )

                        result = self._make_llm_call(
                            prompt,
                            UpdateActionResult,
                            "You are an expert in knowledge base management for RBI regulations."
                        )
                        
                        # Post-process actions to ensure URLs and document IDs are populated
                        processed_actions = []
                        for action in result.get('actions', []):
                            processed_action = self._validate_and_enhance_action(action, notification_data, truncated_content)
                            # Add validation assertions
                            self._assert_action_completeness(processed_action)
                            processed_actions.append(processed_action)
                        
                        result['actions'] = processed_actions
                        logger.info(f"✅ Generated {len(result.get('actions', []))} update actions")
                        return result

                    except Exception as e:
                        error_msg = str(e)
                        if "length limit was reached" in error_msg or "completion_tokens" in error_msg:
                            logger.error(f"Token limit exceeded during update action determination: {e}")
                            logger.info("🔄 Falling back to simplified analysis...")
                            return self._get_simplified_update_actions(title, category, affected_documents, notification_data)
                        else:
                            logger.error(f"Error determining update actions: {e}")
                            return self._get_fallback_update_actions()

                def _validate_and_enhance_action(self, action: dict, notification_data: dict = None, chunk_content: str = None) -> dict:
                    """Validate and enhance action with missing URLs, document IDs, and content-based action IDs"""
                    try:
                        action_type = action.get('action_type', '')
                        
                        # Generate content-based action ID
                        action_id = self._generate_action_id(action, chunk_content, notification_data)
                        action['action_id'] = action_id
                        
                        # CRITICAL: Ensure target_document is not None for removal actions
                        if action_type == 'REMOVE_DOCUMENT' and not action.get('target_document'):
                            logger.warning(f"⚠️ REMOVE_DOCUMENT action missing target_document, attempting extraction")
                            logger.info(f"🔍 DEBUG: Action keys: {list(action.keys())}")
                            logger.info(f"🔍 DEBUG: filter_fields: {action.get('filter_fields', 'NOT_FOUND')}")
                            logger.info(f"🔍 DEBUG: document_id: {action.get('document_id', 'NOT_FOUND')}")
                            
                            # Try to extract from various fields, including filter_fields
                            document_id = (action.get('document_id') or 
                                         action.get('target_document_id') or
                                         action.get('filter_fields', {}).get('document_id') or
                                         self._extract_document_id_from_details(action.get('details', '')))
                            
                            if document_id:
                                action['target_document'] = document_id
                                logger.info(f"✅ Extracted target_document: {document_id}")
                            else:
                                # For REMOVE_DOCUMENT, if we have filter_fields but no target_document, 
                                # use the document_id from filter_fields as target_document
                                filter_doc_id = action.get('filter_fields', {}).get('document_id')
                                if filter_doc_id:
                                    action['target_document'] = filter_doc_id
                                    logger.info(f"✅ Using filter_fields document_id as target_document: {filter_doc_id}")
                                else:
                                    action['target_document'] = 'MANUAL_REVIEW_REQUIRED'
                                    logger.warning(f"⚠️ Could not extract target_document, set to MANUAL_REVIEW_REQUIRED")

                        # CRITICAL: For ADD_DOCUMENT actions, ensure we have document ID from notification codes
                        if action_type == 'ADD_DOCUMENT' and not action.get('target_document') and notification_data:
                            # Try to get document ID from notification codes in the notification data
                            notification_codes = notification_data.get('notification_codes', {})
                            if not notification_codes:
                                # Try to extract from notification title or other fields
                                title = notification_data.get('Title', '')
                                pdf_link = notification_data.get('PDF Link', '')
                                document_id = self._extract_document_id_from_title_or_link(title, pdf_link)
                                if document_id:
                                    action['target_document'] = document_id
                                    logger.info(f"✅ Extracted document ID from title/link: {document_id}")
                            else:
                                document_id = (notification_codes.get('short_code') or 
                                             notification_codes.get('long_code') or 
                                             notification_codes.get('full_code'))
                                if document_id:
                                    action['target_document'] = document_id
                                    logger.info(f"✅ Used notification code as target_document: {document_id}")
                        
                        # Extract URLs from notification_data if not present in action
                        if notification_data:
                            pdf_link = notification_data.get('PDF Link', '')
                            
                            # Set new_document_url if missing
                            if not action.get('new_document_url') and pdf_link:
                                action['new_document_url'] = pdf_link
                                logger.info(f"✅ Added new_document_url: {pdf_link}")
                            
                            # Set rbi_page_url if missing - construct from PDF link or use existing
                            if not action.get('rbi_page_url'):
                                # Try to construct RBI page URL from PDF link
                                if pdf_link and 'rbidocs.rbi.org.in' in pdf_link:
                                    # Extract ID from PDF link and construct page URL
                                    import re
                                    pdf_match = re.search(r'PDFs/(.+?)\.PDF', pdf_link)
                                    if pdf_match:
                                        pdf_id = pdf_match.group(1)
                                        rbi_page_url = f"https://rbi.org.in/Scripts/NotificationUser.aspx?Id={pdf_id}&Mode=0"
                                        action['rbi_page_url'] = rbi_page_url
                                        logger.info(f"✅ Constructed rbi_page_url: {rbi_page_url}")
                                    else:
                                        action['rbi_page_url'] = pdf_link  # Fallback to PDF link
                                        logger.info(f"✅ Used PDF link as rbi_page_url: {pdf_link}")
                                elif pdf_link:
                                    # For non-official URLs (like S3), use the PDF link as page URL
                                    action['rbi_page_url'] = pdf_link
                                    logger.info(f"✅ Used PDF link as rbi_page_url: {pdf_link}")
                                else:
                                    # If no PDF link available, set to None but don't fail
                                    action['rbi_page_url'] = None
                                    logger.warning(f"⚠️ No PDF link available for rbi_page_url construction")
                        
                        return action
                        
                    except Exception as e:
                        logger.error(f"Error validating action: {e}")
                        return action

                def _generate_action_id(self, action: dict, chunk_content: str = None, notification_data: dict = None) -> str:
                    """Generate a unique action ID based on content and metadata"""
                    try:
                        import hashlib
                        
                        # Create ID components
                        action_type = action.get('action_type', 'UNKNOWN')
                        target_document = action.get('target_document', '')
                        details = action.get('details', '')
                        
                        # Include chunk content hash if available
                        content_hash = ""
                        if chunk_content:
                            content_hash = hashlib.md5(chunk_content.encode('utf-8')).hexdigest()[:8]
                        
                        # Include notification codes if available
                        notification_hash = ""
                        if notification_data:
                            notification_codes = notification_data.get('notification_codes', {})
                            short_code = notification_codes.get('short_code', '')
                            if short_code:
                                notification_hash = hashlib.md5(short_code.encode('utf-8')).hexdigest()[:6]
                        
                        # Create meaningful action ID
                        id_parts = [action_type]
                        
                        if target_document and target_document != 'MANUAL_REVIEW_REQUIRED':
                            # Clean target document for ID
                            clean_target = re.sub(r'[^A-Za-z0-9_-]', '_', target_document)[:20]
                            id_parts.append(clean_target)
                        
                        if content_hash:
                            id_parts.append(f"c{content_hash}")
                        
                        if notification_hash:
                            id_parts.append(f"n{notification_hash}")
                        
                        # Add timestamp component for uniqueness
                        timestamp = datetime.now().strftime('%m%d_%H%M')
                        id_parts.append(timestamp)
                        
                        action_id = "_".join(id_parts)
                        logger.info(f"✅ Generated action ID: {action_id}")
                        return action_id
                        
                    except Exception as e:
                        logger.error(f"Error generating action ID: {e}")
                        # Fallback to simple timestamp-based ID
                        return f"action_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:17]}"

                def _extract_document_id_from_details(self, details: str) -> str:
                    """Extract document ID from action details using regex patterns"""
                    try:
                        if not details:
                            return ''
                        
                        # Enhanced patterns for document ID extraction
                        patterns = [
                            # RBI general references such as RBI/FED/2024‑25/17
                            r'RBI/[A-Za-z]+/\\d{4}-\\d{2}/\\d+',

                            # Codes with multiple department segments and numeric parts (e.g., DoR.MCS.REC.38/01.01.001/2024‑25 or DGBA.GBD/1616/15.02.005/2017‑18)
                            r'[A-Za-z]+(?:\\.[A-Za-z]+)+/?\\d+/(?:\\d+[\\.\\-])+\\d+/\\d{4}(?:-\\d{2})?',

                            # Codes that include “No.” after department segments (e.g., DGBA.GBD.No.1007/15.04.001/2017‑18, DBR.No.Leg.BC.98/09.08.019/2017‑18, CO.DPSS.POLC.No.S‑786/02‑14‑008/2021‑22)
                            r'[A-Za-z]+(?:\\.[A-Za-z]+)+(?:\\.[A-Za-z]+)?\\.\\s*No\\.?\\s*[\\w-]+/\\d+(?:[\\.\\-]\\d+)+/\\d{4}(?:-\\d{2})?',

                            # Codes with parentheses in department names (e.g., DNBR.PD.(ARC)CC. No.04/26.03.001/2017‑18, DCBR.BPD.(PCB/RCB).Cir.No.06/12.05.001/2017‑18)
                            r'[A-Za-z]+\\.[A-Za-z]+\\.\\([A-Za-z/]+\\)[A-Za-z]+\\.\\s*No\\.?\\s*[\\w-]+/\\d+(?:[\\.\\-]\\d+)+/\\d{4}(?:-\\d{2})?',

                            # A.P. (DIR Series) circulars
                            r'A\\.P\\.\\s*\\(DIR\\s*Series\\)\\s*Circular\\s*No\\.?\\s*\\d+',

                            # Master Direction codes with optional department, parenthesis and dash (e.g., Master Direction DCM(CC) No.G‑3/03.44.01/2017‑18)
                            r'Master\\s*Direction\\s*(?:[A-Za-z]+\\s*)?(?:\\([A-Za-z/]+\\)\\s*)?No\\.?\\s*[A-Za-z0-9]+(?:\\s*[-–]\\s*[A-Za-z0-9]+)?/\\d+(?:[\\.\\-]\\d+)+/\\d{4}(?:-\\d{2})?',
                        ]
                        
                        for pattern in patterns:
                            match = re.search(pattern, details, re.IGNORECASE)
                            if match:
                                document_id = match.group(0).strip()
                                logger.info(f"✅ Extracted document ID from details: {document_id}")
                                return document_id
                        
                        return ''
                        
                    except Exception as e:
                        logger.error(f"Error extracting document ID from details: {e}")
                        return ''

                def _extract_document_id_from_title_or_link(self, title: str, pdf_link: str) -> str:
                    """Extract document ID from notification title or PDF link"""
                    try:
                        # Try to extract from title first
                        if title:
                            patterns = [
                                r'RBI/\d{4}-\d{2}/\d+',
                                r'A\.P\.\s*\(DIR\s*Series\)\s*Circular\s*No\.\s*\d+',
                                r'Circular\s*No\.\s*\d+',
                            ]
                            
                            for pattern in patterns:
                                match = re.search(pattern, title, re.IGNORECASE)
                                if match:
                                    return match.group(0).strip()
                        
                        # Try to extract from PDF link
                        if pdf_link:
                            # Extract filename from PDF link and use as ID
                            pdf_match = re.search(r'PDFs/(.+?)\.PDF', pdf_link)
                            if pdf_match:
                                pdf_id = pdf_match.group(1)
                                # Clean up the PDF ID to make it more readable
                                clean_id = re.sub(r'[^A-Za-z0-9_-]', '_', pdf_id)[:50]
                                return clean_id
                        
                        return ''
                        
                    except Exception as e:
                        logger.error(f"Error extracting document ID from title/link: {e}")
                        return ''

                def _assert_action_completeness(self, action: dict) -> None:
                    """Assert that critical fields are present in actions with comprehensive validation"""
                    try:
                        action_type = action.get('action_type', '')
                        
                        # Critical field validations
                        required_fields = ['action_type']
                        
                        if action_type == 'REMOVE_DOCUMENT':
                            required_fields.extend(['target_document'])
                        elif action_type == 'ADD_DOCUMENT':
                            required_fields.extend(['target_document'])
                        elif action_type == 'UPDATE_DOCUMENT':
                            required_fields.extend(['target_document'])
                        
                        # Check for missing or invalid fields
                        missing_fields = []
                        invalid_fields = []
                        
                        for field in required_fields:
                            field_value = action.get(field)
                            if not field_value:
                                missing_fields.append(field)
                            elif field == 'target_document' and field_value == 'MANUAL_REVIEW_REQUIRED':
                                invalid_fields.append(f"{field} (requires manual review)")
                        
                        # Critical validation failures
                        if missing_fields:
                            logger.error(f"❌ CRITICAL VALIDATION FAILED: Action missing required fields: {missing_fields}")
                            logger.error(f"❌ Action: {json.dumps(action, default=str)}")
                        
                        if invalid_fields:
                            logger.warning(f"⚠️ VALIDATION WARNING: Action has fields requiring attention: {invalid_fields}")
                        
                        # URL validations (warnings only)
                        url_warnings = []
                        if not action.get('new_document_url'):
                            url_warnings.append('new_document_url')
                        
                        if not action.get('rbi_page_url'):
                            url_warnings.append('rbi_page_url')
                        
                        if url_warnings:
                            logger.warning(f"⚠️ Action missing URLs: {url_warnings} for action type: {action.get('action_type', 'UNKNOWN')}")
                        
                        # Success logging for complete actions
                        if not missing_fields and not invalid_fields and not url_warnings:
                            logger.info(f"✅ Action validation passed: {action_type}")
                        
                        # Additional specific validations based on action type
                        if action_type == 'ADD_DOCUMENT':
                            # Ensure we have meaningful document identifier
                            target_doc = action.get('target_document', '')
                            if len(target_doc) < 5:  # Very short document IDs are suspicious
                                logger.warning(f"⚠️ Suspiciously short target_document for ADD_DOCUMENT: '{target_doc}'")
                        
                        elif action_type == 'REMOVE_DOCUMENT':
                            # Ensure removal actions have clear reasoning
                            details = action.get('details', '')
                            if len(details) < 20:  # Very short details are suspicious
                                logger.warning(f"⚠️ REMOVE_DOCUMENT action has insufficient details: '{details}'")
                            
                    except Exception as e:
                        logger.error(f"❌ Error in action validation assertions: {e}")

                def _get_fallback_analysis(self) -> dict:
                    """Return fallback when analysis fails"""
                    return {
                        "category": "Informational",
                        "confidence": "low",
                        "reasoning": "LLM analysis failed",
                        "affects_regulations": False,
                        "keywords_found": [],
                        "requires_kb_update": False
                    }
                
                def _get_fallback_affected_documents(self) -> dict:
                    """Return fallback when affected documents extraction fails"""
                    return {
                        "document_actions": [],
                        "document_keywords": [],
                        "has_new_document_link": False,
                        "new_document_url": "",
                        "rbi_links": [],
                        "processing_notes": "LLM analysis failed",
                        "requires_manual_review": True
                    }
                
                def _get_fallback_update_actions(self) -> dict:
                    """Return fallback when update action determination fails"""
                    return {
                        "actions": [],
                        "processing_notes": "LLM analysis failed",
                        "requires_manual_review": True
                    }

                def _get_simplified_update_actions(self, title: str, category: str, affected_documents: list, notification_data: dict = None) -> dict:
                    """Simplified update actions when full analysis fails due to token limits"""
                    logger.info("🔧 Generating simplified update actions...")
                    
                    # Basic rule-based actions based on category and affected documents
                    actions = []
                    
                    # If there are affected documents, create basic actions
                    for doc in affected_documents:
                        action_type = doc.get('action_type', '')
                        if action_type == 'REMOVE_DOCUMENT':
                            actions.append({
                                'action_type': 'REMOVE_DOCUMENT',
                                'target_document': doc.get('document_id', 'MANUAL_REVIEW_REQUIRED'),
                                'details': f"Document removal based on {category.lower()} notification",
                                'priority': 'HIGH'
                            })
                        elif action_type == 'ADD_DOCUMENT':
                            actions.append({
                                'action_type': 'ADD_DOCUMENT', 
                                'target_document': doc.get('document_id', 'MANUAL_REVIEW_REQUIRED'),
                                'details': f"Document addition based on {category.lower()} notification",
                                'priority': 'MEDIUM',
                                'new_document_url': doc.get('new_document_url', ''),
                                'rbi_page_url': doc.get('rbi_page_url', '')
                            })
                    
                    # If no affected documents but this is a new notification, add basic ADD action
                    if not actions and category.lower() in ['new', 'issuance', 'launch']:
                        actions.append({
                            'action_type': 'ADD_DOCUMENT',
                            'target_document': 'MANUAL_REVIEW_REQUIRED',
                            'details': f"New document addition for {category.lower()} notification",
                            'priority': 'MEDIUM'
                        })
                    
                    return {
                        "actions": actions,
                        "processing_notes": "Simplified analysis due to token limit",
                        "requires_manual_review": True
                    }
                
                def _detect_document_type(self, title: str, analysis_result: dict) -> str:
                    """
                    Detect document type based on title and LLM analysis
                    Returns: 'master_circular', 'circular', 'master_direction', 'other'
                    """
                    title_lower = title.lower()
                    category = analysis_result.get('category', '').lower()
                    
                    logger.info(f"🔍 Detecting document type from title: {title}")
                    logger.info(f"📋 LLM Category: {category}")
                    
                    # Master Circular detection
                    if 'master circular' in title_lower:
                        logger.info("✅ Detected: Master Circular")
                        return 'master_circular'
                    
                    # Master Direction detection
                    if 'master direction' in title_lower:
                        logger.info("✅ Detected: Master Direction")
                        return 'master_direction'
                    
                    # Regular Circular detection
                    if any(keyword in title_lower for keyword in ['circular', 'notification']):
                        logger.info("✅ Detected: Circular")
                        return 'circular'
                    
                    # Fallback to other
                    logger.info("✅ Detected: Other")
                    return 'other'
                
                def _generate_flowchart_based_actions(self, document_type: str, title: str, content: str, 
                                                    analysis_result: dict, kb_decision: dict, notification: dict) -> list:
                    """
                    Generate KB actions based on the flowchart logic for each document type
                    """
                    logger.info(f"🎯 Generating flowchart-based actions for document type: {document_type}")
                    
                    if document_type == 'master_circular':
                        return self._process_master_circular_actions(title, content, analysis_result, kb_decision, notification)
                    elif document_type == 'circular':
                        return self._process_circular_actions(title, content, analysis_result, kb_decision, notification)
                    elif document_type == 'master_direction':
                        return self._process_master_direction_actions(title, content, analysis_result, kb_decision, notification)
                    else:
                        return self._process_other_document_actions(title, content, analysis_result, kb_decision, notification)
                
                def _process_master_circular_actions(self, title: str, content: str, analysis_result: dict, 
                                                   kb_decision: dict, notification: dict) -> list:
                    """Process Master Circular with consolidation detection"""
                    actions = []
                    logger.info("📘 Processing Master Circular actions...")
                    
                    # Always add the Master Circular to knowledge base
                    add_action = self._create_add_document_action(title, content, notification, 'rbi_master_circular', 
                                                                "New Master Circular publication")
                    actions.append(add_action)
                    logger.info("✅ Added: ADD_DOCUMENT action for Master Circular")
                    
                    # Check for consolidation using LLM analysis
                    consolidation_detected, superseded_docs = self._detect_consolidation_from_analysis(analysis_result)
                    
                    if consolidation_detected:
                        if superseded_docs:
                            # Explicit supersession list found
                            for doc in superseded_docs:
                                remove_action = self._create_remove_document_action(
                                    doc, 'rbi_master_circular', 
                                    f"Superseded by Master Circular: {title}"
                                )
                                actions.append(remove_action)
                                logger.info(f"✅ Added: REMOVE_DOCUMENT action for {doc}")
                        else:
                            # General consolidation mentioned, flag for review
                            flag_action = self._create_flag_for_review_action(
                                'rbi_master_circular', 
                                "Consolidation mentioned but no specific documents listed - manual review needed"
                            )
                            actions.append(flag_action)
                            logger.info("✅ Added: FLAG_FOR_REVIEW action for consolidation")
                    
                    return actions
                
                def _process_circular_actions(self, title: str, content: str, analysis_result: dict, 
                                            kb_decision: dict, notification: dict) -> list:
                    """Process regular Circular with linkage detection"""
                    actions = []
                    logger.info("📄 Processing Circular actions...")
                    
                    # Always add the circular to knowledge base
                    add_action = self._create_add_document_action(title, content, notification, 'rbi_circular', 
                                                                "New circular publication")
                    actions.append(add_action)
                    logger.info("✅ Added: ADD_DOCUMENT action for Circular")
                    
                    # Check for amendments/modifications using LLM analysis
                    amendment_detected, linked_docs = self._detect_amendments_from_analysis(analysis_result)
                    clarification_detected = self._detect_clarifications_from_analysis(analysis_result)
                    
                    if amendment_detected and linked_docs:
                        # Links to documents that are being amended/modified
                        for doc in linked_docs:
                            update_action = self._create_update_document_action(
                                doc, 'rbi_circular', 
                                f"Modified by circular: {title}"
                            )
                            actions.append(update_action)
                            logger.info(f"✅ Added: UPDATE_DOCUMENT action for {doc}")
                    elif clarification_detected:
                        # Just provides clarification, create reference linkage
                        link_action = self._create_link_document_action(
                            'rbi_circular', 
                            "Clarification document - create reference links"
                        )
                        actions.append(link_action)
                        logger.info("✅ Added: LINK_DOCUMENT action for clarification")
                    
                    return actions
                
                def _process_master_direction_actions(self, title: str, content: str, analysis_result: dict, 
                                                    kb_decision: dict, notification: dict) -> list:
                    """Process Master Direction with amendment detection"""
                    actions = []
                    logger.info("📜 Processing Master Direction actions...")
                    
                    # Check if this is an amendment to existing direction
                    is_amendment, target_direction = self._detect_direction_amendment_from_analysis(analysis_result, title)
                    
                    if is_amendment and target_direction:
                        update_action = self._create_update_document_action(
                            target_direction, 'rbi_master_direction', 
                            f"Amendment to {target_direction}"
                        )
                        actions.append(update_action)
                        logger.info(f"✅ Added: UPDATE_DOCUMENT action for Master Direction amendment")
                    else:
                        # New Master Direction
                        add_action = self._create_add_document_action(title, content, notification, 'rbi_master_direction', 
                                                                    "New Master Direction publication")
                        actions.append(add_action)
                        logger.info("✅ Added: ADD_DOCUMENT action for new Master Direction")
                    
                    # Extract requirements and timeline
                    requirements = self._extract_requirements_from_analysis(analysis_result)
                    if requirements:
                        link_action = self._create_link_document_action(
                            'rbi_master_direction', 
                            f"Requirements and timeline extracted: {len(requirements)} items"
                        )
                        actions.append(link_action)
                        logger.info("✅ Added: LINK_DOCUMENT action for requirements")
                    
                    return actions
                
                def _process_other_document_actions(self, title: str, content: str, analysis_result: dict, 
                                                  kb_decision: dict, notification: dict) -> list:
                    """Process other document types"""
                    actions = []
                    logger.info("📑 Processing Other document actions...")
                    
                    # Always add to knowledge base
                    add_action = self._create_add_document_action(title, content, notification, 'rbi_other', 
                                                                "Other notification type")
                    actions.append(add_action)
                    logger.info("✅ Added: ADD_DOCUMENT action for Other document")
                    
                    # Check for actionable directives
                    actionable_content = self._detect_actionable_content_from_analysis(analysis_result)
                    if actionable_content:
                        link_action = self._create_link_document_action(
                            'rbi_other', 
                            f"Actionable directives found: {actionable_content}"
                        )
                        actions.append(link_action)
                        logger.info("✅ Added: LINK_DOCUMENT action for actionable content")
                    
                    return actions
                
                # Helper methods for creating specific action types
                def _create_add_document_action(self, title: str, content: str, notification: dict, 
                                              collection: str, reasoning: str) -> dict:
                    """Create standardized ADD_DOCUMENT action"""
                    payload = {
                        'metadata': {
                            'title': title,
                            'document_type': 'notification',
                            'processing_timestamp': datetime.now().isoformat(),
                            'pdf_link': notification.get('PDF Link', ''),
                            'notification_link': notification.get('Link', ''),
                            'local_path': notification.get('Local Path', ''),
                            'collection': collection
                        }, 
                        'content': content, 
                        'notification_link': notification.get('Link', ''), 
                        'pdf_url': notification.get('PDF Link', '')
                    }
                    
                    return {
                        'action_type': 'ADD_DOCUMENT',
                        'collection_name': collection,
                        'target_document': title,
                        'payload': payload,
                        'reasoning': reasoning,
                        'confidence': 0.9
                    }
                
                def _create_remove_document_action(self, target_document: str, collection: str, reasoning: str) -> dict:
                    """Create standardized REMOVE_DOCUMENT action"""
                    return {
                        'action_type': 'REMOVE_DOCUMENT',
                        'collection_name': collection,
                        'target_document': target_document,
                        'filter_fields': {'metadata.title': target_document},
                        'reasoning': reasoning,
                        'confidence': 0.85
                    }
                
                def _create_update_document_action(self, target_document: str, collection: str, reasoning: str) -> dict:
                    """Create standardized UPDATE_DOCUMENT action"""
                    return {
                        'action_type': 'UPDATE_DOCUMENT',
                        'collection_name': collection,
                        'target_document': target_document,
                        'reasoning': reasoning,
                        'confidence': 0.8
                    }
                
                def _create_link_document_action(self, collection: str, reasoning: str) -> dict:
                    """Create standardized LINK_DOCUMENT action"""
                    return {
                        'action_type': 'LINK_DOCUMENT',
                        'collection_name': collection,
                        'reasoning': reasoning,
                        'confidence': 0.7
                    }
                
                def _create_flag_for_review_action(self, collection: str, reasoning: str) -> dict:
                    """Create standardized FLAG_FOR_REVIEW action"""
                    return {
                        'action_type': 'FLAG_FOR_REVIEW',
                        'collection_name': collection,
                        'reasoning': reasoning,
                        'confidence': 0.6
                    }
                
                # Helper methods for analyzing LLM results
                def _detect_consolidation_from_analysis(self, analysis_result: dict) -> tuple:
                    """Detect consolidation patterns from LLM analysis"""
                    category = analysis_result.get('category', '').lower()
                    reasoning = analysis_result.get('reasoning', '').lower()
                    
                    consolidation_indicators = ['consolidat', 'supersed', 'replac', 'brings together']
                    consolidation_detected = any(indicator in reasoning for indicator in consolidation_indicators)
                    
                    # Extract superseded documents from analysis if available
                    superseded_docs = []
                    # This would need to be enhanced based on actual LLM response structure
                    
                    return consolidation_detected, superseded_docs
                
                def _detect_amendments_from_analysis(self, analysis_result: dict) -> tuple:
                    """Detect amendment patterns from LLM analysis"""
                    category = analysis_result.get('category', '').lower()
                    reasoning = analysis_result.get('reasoning', '').lower()
                    
                    amendment_indicators = ['amend', 'modif', 'revis', 'updat']
                    amendment_detected = any(indicator in reasoning for indicator in amendment_indicators)
                    
                    # Extract linked documents from analysis
                    linked_docs = []
                    # This would need to be enhanced based on actual LLM response structure
                    
                    return amendment_detected, linked_docs
                
                def _detect_clarifications_from_analysis(self, analysis_result: dict) -> bool:
                    """Detect clarification patterns from LLM analysis"""
                    reasoning = analysis_result.get('reasoning', '').lower()
                    clarification_indicators = ['clarif', 'explain', 'clarity']
                    return any(indicator in reasoning for indicator in clarification_indicators)
                
                def _detect_direction_amendment_from_analysis(self, analysis_result: dict, title: str) -> tuple:
                    """Detect Master Direction amendment from analysis"""
                    title_lower = title.lower()
                    reasoning = analysis_result.get('reasoning', '').lower()
                    
                    is_amendment = 'amendment' in title_lower or 'amending' in title_lower
                    target_direction = None
                    
                    if is_amendment:
                        # Try to extract target direction from title
                        import re
                        direction_pattern = r'master direction[\s\w]*(\w+\.[\w\.]+)'
                        match = re.search(direction_pattern, title, re.IGNORECASE)
                        if match:
                            target_direction = match.group(1)
                    
                    return is_amendment, target_direction
                
                def _extract_requirements_from_analysis(self, analysis_result: dict) -> list:
                    """Extract requirements from LLM analysis"""
                    reasoning = analysis_result.get('reasoning', '').lower()
                    requirement_indicators = ['shall', 'must', 'required', 'mandatory', 'effective from']
                    
                    if any(indicator in reasoning for indicator in requirement_indicators):
                        return ['Requirements found in analysis']
                    return []
                
                def _detect_actionable_content_from_analysis(self, analysis_result: dict) -> str:
                    """Detect actionable content from LLM analysis"""
                    reasoning = analysis_result.get('reasoning', '').lower()
                    actionable_indicators = ['action required', 'implement', 'comply', 'submit']
                    
                    for indicator in actionable_indicators:
                        if indicator in reasoning:
                            return f"Actionable content detected: {indicator}"
                    return ""
            
            # Create the processor instance
            self.notification_processor = RealNotificationProcessor(openai_manager=self.openai_manager)
            logger.info("✅ Real notification processor initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup notification processor: {e}")
            raise
    
    def load_notifications(self) -> bool:
        """Load notifications from JSON file"""
        try:
            if not self.notifications_file.exists():
                logger.error(f"Notifications file not found: {self.notifications_file}")
                return False
            
            with open(self.notifications_file, 'r', encoding='utf-8') as f:
                self.notifications = json.load(f)
            
            # Log the file path and content being read
            logger.info(f"📂 Reading notifications from file: {self.notifications_file}")
            logger.info(f"📏 Notification content length: {len(json.dumps(self.notifications))} characters")
            
            logger.info(f"📁 Loaded {len(self.notifications)} notifications")
            return True
            
        except Exception as e:
            logger.error(f"Error loading notifications: {e}")
            return False

    def extract_pdf_from_local_file(self, local_path, max_chars=8000):
        """Extract chunked content from local PDF file using HTML AST and chunk_html_ast"""
        try:
            from utils.pdf_utils import parse_pdf_to_html_ast, chunk_html_ast
            from bs4 import BeautifulSoup
            logger.info(f"📄 Extracting content from local PDF (HTML AST): {local_path}")

            # Check for watermark and get the specific keyword found
            watermark_status = self.has_diagonal_withdrawn_watermark(local_path)

            if watermark_status in ["WITHDRAWN", "SUPERSEDED", "REPEALED"]:
                logger.info(f"   🚫 {watermark_status} watermark detected in PDF: {local_path}")
            elif watermark_status == "ACTIVE":
                logger.info(f"   ✅ No watermark found - document is ACTIVE: {local_path}")
            else:
                logger.warning(f"   ⚠️ Unknown watermark status: {watermark_status}")

            # Parse PDF to HTML AST
            html_content = parse_pdf_to_html_ast(local_path)
            # Write to a test_results folder for debugging

            logger.info(f"   📄 Parsed HTML AST from PDF: {local_path}")
            # Chunk the HTML AST
            chunks = chunk_html_ast(html_content, max_chars=max_chars)

            logger.info(f"   📦 Chunked HTML AST into {len(chunks)} chunks")
            # Extract plain text for notification codes
            soup = BeautifulSoup(str(html_content), 'html.parser')
            text_content = soup.get_text(separator=' ', strip=True)
            notification_codes = self.extract_notification_codes_from_text(text_content)
            logger.info(f"   📋 Extracted notification codes: {notification_codes}")

            # Add watermark status to notification codes
            notification_codes['watermark_status'] = watermark_status
            notification_codes['is_active'] = watermark_status == "ACTIVE"

            logger.info(f"   ✅ Extracted {len(chunks)} chunks from local PDF")
            logger.info(f"   🏷️ Watermark Status: {watermark_status}")
            if notification_codes.get('short_code') or notification_codes.get('long_code'):
                logger.info(f"   📋 Short code: {notification_codes.get('short_code', 'Not found')}")
                logger.info(f"   📋 Long code: {notification_codes.get('long_code', 'Not found')}")
                logger.info(f"   📅 Year from codes: {notification_codes.get('year', 'Not found')}")

            return chunks, notification_codes
        except Exception as e:
            logger.error(f"   ❌ Error extracting PDF content from local file (HTML AST): {e}")
            return [], {}

    def has_diagonal_withdrawn_watermark(self, pdf_path: str) -> str:
        """
        Check if a PDF has a diagonal watermark indicating its status.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            str: Status of the document - "WITHDRAWN", "SUPERSEDED", "REPEALED", or "ACTIVE"
        """
        try:
            # Use the imported function to check for watermark
            has_watermark = check_pdf_watermark(pdf_path)
            
            # Open the PDF to check the specific watermark text
            doc = fitz.open(pdf_path)
            watermark_text = ""
            
            for page in doc:
                text = page.get_text().lower()
                if "withdrawn" in text:
                    watermark_text = "WITHDRAWN"
                    break
                elif "superseded" in text:
                    watermark_text = "SUPERSEDED"
                    break
                elif "repealed" in text:
                    watermark_text = "REPEALED"
                    break
            
            doc.close()
            
            # If has_watermark is True but we couldn't determine the specific text,
            # default to WITHDRAWN
            if has_watermark and not watermark_text:
                return "WITHDRAWN"
            elif has_watermark:
                return watermark_text
            else:
                return "ACTIVE"
                
        except Exception as e:
            logger.warning(f"⚠️ Error checking watermark status: {e}")
            # Default to ACTIVE if there's an error
            return "ACTIVE"

    def download_and_extract_pdf_content(self, document_url: str, max_chars: int = None, return_chunks: bool = False):
        """
        Download a PDF from a URL and extract its content.
        
        Args:
            document_url: URL of the PDF document
            max_chars: Maximum characters per chunk (if None, no limit)
            return_chunks: If True, returns the chunks instead of just content
            
        Returns:
            If return_chunks is True, returns list of chunk dictionaries
            Otherwise, returns extracted text content
        """
        try:
            import requests
            import tempfile
            import os
            from pathlib import Path
            
            logger.info(f"📥 Downloading PDF from URL: {document_url}")
            
            # Create temp directory if it doesn't exist
            temp_dir = Path("./temp_pdfs")
            temp_dir.mkdir(exist_ok=True)
            
            # Generate a temporary filename
            pdf_filename = f"temp_{int(datetime.now().timestamp())}.pdf"
            local_path = temp_dir / pdf_filename
            
            # Download the file
            try:
                response = requests.get(document_url, stream=True, timeout=30)
                response.raise_for_status()  # Raise an exception for bad responses
                
                with open(local_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                logger.info(f"✅ PDF downloaded successfully to: {local_path}")
            except Exception as e:
                logger.error(f"❌ Failed to download PDF: {e}")
                raise
            
            # Extract content from the downloaded PDF
            from utils.pdf_utils import parse_pdf_to_html_ast, chunk_html_ast
            from bs4 import BeautifulSoup
            
            # Check for watermark
            watermark_status = self.has_diagonal_withdrawn_watermark(str(local_path))
            
            # Parse PDF to HTML AST
            html_content = parse_pdf_to_html_ast(str(local_path))
            
            # Chunk the HTML AST
            chunks = chunk_html_ast(html_content, max_chars=max_chars or 8000)
            
            # Extract plain text for notification codes
            soup = BeautifulSoup(str(html_content), 'html.parser')
            text_content = soup.get_text(separator=' ', strip=True)

            llm_response = self.openai_manager.get_completion(
                prompt=f"""

You are an RBI notification extractor bot.
 
Given the following text extracted from an RBI regulatory PDF, extract only the official title of the notification.

Return the result in the following JSON format: {{ "title": "<actual title>" }}
 
Content:

{text_content[:2000]}

""",
                model=None,  # Let smart selection choose
                temperature=0.1,
                response_format={"type": "json_object"},
                task_type='title_extraction',  # Simple task
                priority='speed'  # Prioritize speed for title extraction
            )

 
            logger.info(f"LLM response: {llm_response}")
            if isinstance(llm_response, str):
                llm_response = json.loads(llm_response)

            title = llm_response["title"]
            # Extract notification codes
            notification_codes = self.extract_notification_codes_from_text(text_content)
            
            logger.info(f"   ✅ Extracted {len(chunks)} chunks from PDF from URL {document_url}")
            logger.info(f" ##############  notification_codes: {notification_codes} ##############")

            # Generate meaningful S3 filename based on extracted codes
            pdf_filename = self._generate_s3_filename(notification_codes, title, pdf_filename)
            logger.info(f"📄 Generated S3 filename: {pdf_filename}")

            # Upload the PDF to S3 and get the link
            try:
                from utils.s3_utils import upload_file_to_s3
                upload_result = upload_file_to_s3(local_path, f"rbi_notifications/{pdf_filename}")
                if upload_result["success"]:
                    new_s3_url = upload_result["s3_url"]
                    logger.info(f"📤 PDF uploaded to S3: {new_s3_url}")
                else:
                    logger.error(f"❌ Failed to upload PDF to S3: {upload_result['error']}")
                    raise Exception(f"S3 upload failed: {upload_result['error']}")
            except Exception as e:
                logger.error(f"❌ Failed to upload PDF to S3: {e}")
                raise

            # Add metadata to chunks
            enhanced_chunks = []
            for i, chunk in enumerate(chunks):
                enhanced_chunk = {
                    **chunk,
                    'document_title': title,
                    'source_url': document_url,
                    'watermark_status': watermark_status,
                    'is_active': watermark_status == "ACTIVE",
                    'chunk_index': i,
                    'total_chunks': len(chunks),
                    'notification_codes': notification_codes,
                    'download_timestamp': datetime.now().isoformat()
                }
                enhanced_chunks.append(enhanced_chunk)
            
            # Clean up the temporary file
            try:
                os.remove(local_path)
                logger.debug(f"🧹 Temporary PDF file removed: {local_path}")
            except Exception as e:
                logger.warning(f"⚠️ Failed to remove temporary PDF file: {e}")
            
            logger.info(f"📄 Extracted {len(enhanced_chunks)} chunks from PDF")
            
            if return_chunks:
                return enhanced_chunks, notification_codes, title, new_s3_url
            else:
                # Return concatenated content if chunks not needed
                return text_content, notification_codes, title, new_s3_url
                
        except Exception as e:
            logger.error(f"❌ Error downloading and extracting PDF content: {e}")
            logger.error(traceback.format_exc())
            raise

    def extract_notification_codes_from_text(self, text_content: str) -> dict:
        """Extract notification codes from PDF text content with enhanced patterns"""

        try:
            # Initialize result structure
            result = {
                'short_code': '',
                'long_code': '',
                'full_code': '',
                'year': '',
                'all_codes': []
            }

            # Enhanced pattern for short codes (RBI reference numbers)
            short_code_patterns = [
                r'RBI/\d{4}-\d{2}/\d+',           # RBI/2025-26/64
                r'RBI/[A-Z]+/\d{4}-\d{2}/\d+',    # RBI/DNBR/2016-17/42
                r'FEMA\s*\d+\([A-Z]*\)/\d+/\d{4}-[A-Z]+',  # FEMA 23(R)/6/2025-RB
                r'FEMA\s*\d+\([A-Z]*\)/\d{4}-[A-Z]+',      # FEMA 23(R)/2015-RB
                r'RBI/\d{4}-\d{4}/\d+',          # Alternative year format
                r'Ref\.No\.\s*MPD\.\d+/\d+\.\d+\.\d+/\d{4}-\d{2}', # Ref.No. MPD.BC.123/07.01.279/2025-26
            ]

            # Enhanced patterns for long codes (department codes and circular series)
            long_code_patterns = [
                r'[A-Z]+\.[A-Z]+\.[A-Z]+\.\d+/\d+\.\d+\.\d+/\d{4}-\d{2}',  # DoR.MCS.REC.38/01.01.001/2025-26
                r'[A-Z]+\.[A-Z]+\.[A-Z]+\.\d+/\d+\.\d+\.\d+/\d{4}',        # DBOD.No.Leg.BC.21/09.07.007/2002
                r'\.[A-Z]+\.\d+/\d+\.\d+\.\d+/\d{4}-\d{2}',                # .PD.004/03.10.119/2016-17
                r'[A-Z]+\.[A-Z]+\.[A-Z]+\.\d+/\d+\.\d+\.\d+',              # DPSS.CO.PD.No.123/02.14.003
                r'A\.P\.\s*\(DIR\s*Series\)\s*Circular\s*No\.\s*\d+',      # A.P. (DIR Series) Circular No. 22
                r'A\.P\.\s*\(DIR\s*Series\)\s*Circular\s*No\.\s*\d+/\d{4}-\d{2}', # A.P. (DIR Series) Circular No. 22/2025-26
                r'Circular\s*No\.\s*[A-Z]+\.\d+',                          # Circular No. DPSS.123
                r'Master\s*Direction\s*No\.\s*[A-Z]+\.\d+',                # Master Direction No. RBI.456
                r'DBOD\.No\.[A-Z]+\.BC\.\d+/\d+\.\d+\.\d+/\d{4}-\d{2}', # DBOD.No.Dir.BC.90/13.03.00/2013-14
                r'DNBS\s*\(PD\)\s*CC\s*No\.\s*\d+/\d+\.\d+\.\d+/\d{4}-\d{2}', # DNBS (PD) CC No. 123/03.10.042/2025-26
            ]

            # Extract short codes
            for pattern in short_code_patterns:
                matches = re.finditer(pattern, text_content, re.IGNORECASE)
                for match in matches:
                    code = match.group(0).strip()
                    if not result['short_code']:
                        result['short_code'] = code
                    if code not in result['all_codes']:
                        result['all_codes'].append(code)

            # Extract long codes
            for pattern in long_code_patterns:
                matches = re.finditer(pattern, text_content, re.IGNORECASE)
                for match in matches:
                    code = match.group(0).strip()
                    if not result['long_code']:
                        result['long_code'] = code
                    if code not in result['all_codes']:
                        result['all_codes'].append(code)

            # Enhanced year extraction with multiple formats
            year_matches = re.findall(r'\d{4}-\d{2}', ' '.join(result['all_codes']))
            if year_matches:
                result['year'] = year_matches[0].split('-')[0]  # Take first year found
            else:
                # Try alternative year formats
                year_matches = re.findall(r'\d{4}-\d{4}', ' '.join(result['all_codes']))
                if year_matches:
                    result['year'] = year_matches[0].split('-')[1]  # Take second year for ranges like 2024-2025
                else:
                    year_matches = re.findall(r'\d{4}', ' '.join(result['all_codes']))
                    result['year'] = year_matches[0] if year_matches else ''

            # Create full code by combining short and long codes
            if result['short_code'] and result['long_code']:
                result['full_code'] = f"{result['short_code']} - {result['long_code']}"
            elif result['short_code']:
                result['full_code'] = result['short_code']
            elif result['long_code']:
                result['full_code'] = result['long_code']

            # Remove duplicates from all_codes
            result['all_codes'] = list(set(result['all_codes']))

            # Add fallback extraction if primary patterns fail
            if not result['short_code'] and not result['long_code']:
                logger.warning("⚠️ Primary code patterns failed, trying fallback extraction")
                fallback_result = self._fallback_code_extraction(text_content)
                if fallback_result['short_code'] or fallback_result['long_code']:
                    result.update(fallback_result)
                    logger.info(f"✅ Fallback extraction successful: {result}")

            # Log extraction results
            if result['short_code'] or result['long_code']:
                logger.info(f"✅ Code extraction successful - Short: {result['short_code']}, Long: {result['long_code']}")
            else:
                logger.warning(f"⚠️ No codes extracted from text")

            return result

        except Exception as e:
            logger.error(f"Error extracting notification codes: {e}")
            return {
                'short_code': '',
                'long_code': '',
                'full_code': '',
                'year': '',
                'all_codes': []
            }

    def _fallback_code_extraction(self, text_content: str) -> dict:
        """Fallback method for code extraction using simpler patterns"""
        try:
            result = {
                'short_code': '',
                'long_code': '',
                'full_code': '',
                'year': '',
                'all_codes': []
            }
            
            # Simple fallback patterns
            fallback_patterns = [
                r'RBI[/\s]+\d{4}[/-]\d{2,4}[/\s]*\d+',     # RBI 2025-26 64
                r'Circular[^0-9]*\d+',                      # Circular No 22
                r'Direction[^0-9]*\d+',                     # Direction No 123
                r'A\.P\.[^0-9]*\d+',                       # A.P. 22
            ]
            
            for pattern in fallback_patterns:
                matches = re.finditer(pattern, text_content, re.IGNORECASE)
                for match in matches:
                    code = match.group(0).strip()
                    if 'RBI' in code.upper() and not result['short_code']:
                        result['short_code'] = code
                    elif ('Circular' in code or 'Direction' in code or 'A.P.' in code) and not result['long_code']:
                        result['long_code'] = code
                    result['all_codes'].append(code)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in fallback code extraction: {e}")
            return {
                'short_code': '',
                'long_code': '',
                'full_code': '',
                'year': '',
                'all_codes': []
            }

    def _generate_s3_filename(self, notification_codes: dict, title: str, original_filename: str) -> str:
        """Generate meaningful S3 filename based on notification codes and title"""
        try:
            # Start with extracted codes
            short_code = notification_codes.get('short_code', '').strip()
            long_code = notification_codes.get('long_code', '').strip()
            year = notification_codes.get('year', '').strip()
            
            # Generate filename based on available data
            if short_code:
                # Use short code as primary identifier
                filename = short_code.replace('/', '_').replace('\\', '_')
                if long_code:
                    # Add sanitized long code
                    sanitized_long = re.sub(r'[^\w\s-]', '', long_code).replace(' ', '_')
                    filename += f"_{sanitized_long}"
            elif long_code:
                # Use long code if short code unavailable
                filename = re.sub(r'[^\w\s-]', '', long_code).replace(' ', '_')
            else:
                # Fallback to sanitized title + timestamp
                sanitized_title = re.sub(r'[^\w\s-]', '', title)[:50].replace(' ', '_')
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{sanitized_title}_{timestamp}"
                logger.warning(f"⚠️ No codes found, using title-based filename: {filename}")
            
            # Add year if available
            if year and year not in filename:
                filename += f"_{year}"
            
            # Ensure .pdf extension
            if not filename.lower().endswith('.pdf'):
                filename += '.pdf'
            
            # Sanitize the final filename
            filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
            filename = re.sub(r'_+', '_', filename)  # Remove multiple underscores
            
            logger.info(f"✅ Generated S3 filename: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"❌ Error generating S3 filename: {e}")
            # Return original filename as last resort
            return original_filename or f"fallback_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

    def classify_chunk_for_indexing(self, chunk_content: str, document_metadata: dict) -> dict:
        """
        Classify chunks and determine if they should be indexed to Qdrant.

        Args:
            chunk_content: The text content of the chunk
            document_metadata: Metadata about the document

        Returns:
            dict: {
                'classification': str,  # 'regulatory', 'informational', 'administrative', 'withdrawn'
                'should_index': bool,   # Whether to index this chunk
                'reasoning': str        # Explanation for the classification
            }
        """
        try:
            # Initialize result
            result = {
                'classification': 'informational',
                'should_index': True,
                'reasoning': 'Default classification'
            }

            # Convert content to lowercase for analysis
            content_lower = chunk_content.lower()

            # Check for withdrawn/repealed documents
            withdrawn_indicators = [
                'withdrawn', 'repealed', 'superseded', 'cancelled', 'revoked',
                'no longer applicable', 'stands withdrawn', 'hereby repealed',
                'will stand withdrawn', 'previous.*superseded'
            ]

            # Also check watermark status from document metadata
            watermark_status = document_metadata.get('watermark_status', 'UNKNOWN')
            if watermark_status in ['WITHDRAWN', 'SUPERSEDED', 'REPEALED']:
                result = {
                    'classification': 'withdrawn',
                    'should_index': False,
                    'reasoning': f'Document has {watermark_status} watermark',
                    'target_collection': 'rbi_other',
                    'watermark_status': watermark_status
                }
                return result

            # Define indicator lists
            regulatory_indicators = [
                'regulation', 'directive', 'guideline', 'instruction', 'circular',
                'master direction', 'notification', 'amendment', 'compliance',
                'shall', 'must', 'required', 'mandatory', 'prohibited', 'penalty',
                'effective from', 'applicable to', 'banks shall', 'nbfcs shall'
            ]

            informational_indicators = [
                'information', 'clarification', 'announcement', 'press release',
                'speech', 'statement', 'report', 'publication', 'data',
                'statistics', 'for information', 'please note', 'fyi'
            ]

            administrative_indicators = [
                'tender', 'recruitment', 'appointment', 'office', 'administrative',
                'meeting', 'conference', 'seminar', 'training', 'holiday',
                'office hours', 'contact', 'address'
            ]

            if any(indicator in content_lower for indicator in withdrawn_indicators):
                result = {
                    'classification': 'withdrawn',
                    'should_index': False,
                    'reasoning': 'Document has been withdrawn or repealed',
                    'target_collection': 'rbi_other'
                }
                return result

            # Calculate indicator counts
            regulatory_count = sum(1 for indicator in regulatory_indicators if indicator in content_lower)
            informational_count = sum(1 for indicator in informational_indicators if indicator in content_lower)
            administrative_count = sum(1 for indicator in administrative_indicators if indicator in content_lower)

            # Get document type from metadata
            doc_type = document_metadata.get('document_type', '').lower()

            # High regulatory content
            if regulatory_count >= 3 or doc_type in ['master_direction', 'master_circular', 'notification']:
                result = {
                    'classification': 'regulatory',
                    'should_index': True,
                    'reasoning': f'High regulatory content ({regulatory_count} indicators) or regulatory document type',
                    'target_collection': 'rbi_circular' if doc_type == 'notification' else 'rbi_master_direction'
                }
            elif administrative_count >= 2 or doc_type in ['tender']:
                result = {
                    'classification': 'administrative',
                    'should_index': False,
                    'reasoning': f'Administrative content ({administrative_count} indicators)',
                    'target_collection': 'rbi_other'
                }
            elif informational_count >= 2 and regulatory_count >= 1:
                result = {
                    'classification': 'regulatory',
                    'should_index': True,
                    'reasoning': 'Mixed regulatory and informational content',
                    'target_collection': 'rbi_circular'
                }
            elif informational_count >= 2:
                result = {
                    'classification': 'informational',
                    'should_index': True,
                    'reasoning': f'Primarily informational content ({informational_count} indicators)',
                    'target_collection': 'rbi_other'
                }
            else:
                result = {
                    'classification': 'informational',
                    'should_index': True,
                    'reasoning': 'Default classification with mixed content',
                    'target_collection': 'rbi_other'
                }

            # Determine appropriate collection for this chunk
            target_collection = self.determine_collection_for_document(
                document_metadata.get('document_type', ''),
                result['classification'],
                chunk_content
            )
            result['target_collection'] = target_collection

            return result

        except Exception as e:
            logger.error(f"Error classifying chunk: {e}")
            return {
                'classification': 'informational',
                'should_index': True,
                'reasoning': f'Classification error: {str(e)}'
            }

    def llm_classify_chunk_for_indexing(self, chunk_content: str, document_metadata: dict, context: dict = None) -> DocumentClassificationModel:
        """
        Use LLM to classify chunks and determine if they should be indexed to Qdrant.
        This provides more accurate classification than rule-based methods with smart model selection.

        Args:
            chunk_content: The text content of the chunk
            document_metadata: Metadata about the document
            context: Additional context including notification data

        Returns:
            DocumentClassificationModel: Pydantic model containing:
                - classification: DocumentClassification enum
                - should_index: bool
                - reasoning: str
                - confidence: ConfidenceLevel enum
                - target_collection: CollectionName enum
        """
        logger.info(f"🔍 [DEBUG] Starting LLM classification of chunk")
        # logger.info(f"🔍 [DEBUG] Document metadata: {json.dumps(document_metadata, default=str)}")
        logger.info(f"🔍 [DEBUG] Context: {json.dumps(context, default=str) if context else 'None'}")
        logger.info(f"🔍 [DEBUG] Chunk content length: {len(chunk_content)} chars")
        # Removed content preview to reduce log noise
        
        try:
            # Get the schema from our Pydantic model
            try:
                indexing_schema = DocumentClassificationModel.get_json_schema()
                logger.info(f"🔍 [DEBUG] Successfully got schema from DocumentClassificationModel")
            except Exception as e:
                logger.error(f"❌ [DEBUG] Error getting schema from DocumentClassificationModel: {e}")
                logger.error(f"❌ [DEBUG] Error traceback: {traceback.format_exc()}")
                raise
            
            # Create LLM prompt for chunk classification
            classification_prompt = f"""
You are an expert RBI regulatory analyst. Analyze the following document chunk and determine:

1. Classification (regulatory, informational, administrative, withdrawn)
2. Whether it should be indexed to knowledge base
3. Confidence level (high, medium, low)
4. Appropriate collection (rbi_circular, rbi_master_circular, rbi_master_direction, rbi_other)
5. Reasoning for your decision

Document Context:
- Document Type: {document_metadata.get('document_type', 'unknown')}
- Source: {document_metadata.get('source_url', 'unknown')}

{f"Notification Context: {context.get('notification_title', '')}" if context else ""}

CHUNK CONTENT:
{chunk_content}

Focus on:
- Regulatory requirements, compliance obligations, or guidelines
- Informational content that aids compliance understanding
- Administrative content (typically not indexed)
- Withdrawn/superseded content (never indexed)
"""

            # Get LLM classification with structured output and smart model selection
            try:
                # Add schema to the prompt
                schema_json = json.dumps(indexing_schema, indent=2)
                schema_prompt = f"{classification_prompt}\n\nYou must respond with a JSON object that matches this schema:\n{schema_json}"
                
                logger.info(f"🔍 [DEBUG] Sending LLM request for classification")
                
                # Smart model selection based on task complexity
                llm_response = self.openai_manager.get_completion(
                    schema_prompt,
                    model=None,  # Let smart selection choose
                    temperature=0.1,
                    response_format={"type": "json_object"},
                    task_type='document_classification',  # Medium complexity task
                    priority='balanced'
                )
                
                logger.info(f"🔍 [DEBUG] Received LLM response for classification")
                logger.info(f"🔍 [DEBUG] Response preview: {llm_response[:200]}...")
                
                # Parse JSON response into our Pydantic model
                try:
                    # Parse with Pydantic model
                    logger.info(f"🔍 [DEBUG] Parsing LLM response with DocumentClassificationModel")
                    result = DocumentClassificationModel.parse_raw(llm_response)
                    
                    # Log the classification results
                    logger.info(f"   🤖 LLM Classification: {result.classification.value} "
                              f"(index: {result.should_index}, confidence: {result.confidence.value})")
                    logger.info(f"🔍 [DEBUG] Complete classification result: {json.dumps(result.model_dump(), default=str)}")
                    
                    return result
                except ValidationError as e:
                    logger.error(f"❌ [DEBUG] Validation error in LLM response: {e}")
                    logger.error(f"❌ [DEBUG] Invalid response: {llm_response}")
                    logger.error(f"❌ [DEBUG] Error details: {str(e)}")
                    
                    # Create a default model instance
                    logger.info(f"🔍 [DEBUG] Creating fallback DocumentClassificationModel due to validation error")
                    return DocumentClassificationModel(
                        classification=DocumentClassification.INFORMATIONAL,
                        should_index=True,
                        confidence=ConfidenceLevel.MEDIUM,
                        target_collection=CollectionName.RBI_OTHER,
                        reasoning="LLM classification with validation fallback"
                    )
                
            except Exception as e:
                logger.warning(f"   ⚠️  LLM classification failed, falling back to rule-based: {e}")
                logger.error(f"❌ [DEBUG] Error in LLM request: {str(e)}")
                logger.error(f"❌ [DEBUG] Error traceback: {traceback.format_exc()}")
                
                # Fall back to rule-based classification
                try:
                    logger.info(f"🔍 [DEBUG] Using rule-based classification fallback")
                    rule_based_result = self.classify_chunk_for_indexing(chunk_content, document_metadata)
                    
                    logger.info(f"🔍 [DEBUG] Rule-based result: {json.dumps(rule_based_result, default=str)}")
                    
                    # Create a model from the rule-based classification
                    result = DocumentClassificationModel(
                        classification=DocumentClassification(rule_based_result.get('classification', 'informational')),
                        should_index=rule_based_result.get('should_index', True),
                        confidence=ConfidenceLevel(rule_based_result.get('confidence', 'medium')),
                        target_collection=CollectionName(rule_based_result.get('target_collection', 'rbi_other')),
                        reasoning=rule_based_result.get('reasoning', 'Rule-based fallback classification')
                    )
                    
                    logger.info(f"🔍 [DEBUG] Created DocumentClassificationModel from rule-based result")
                    return result
                except Exception as fallback_error:
                    logger.error(f"❌ [DEBUG] Even rule-based fallback failed: {str(fallback_error)}")
                    logger.error(f"❌ [DEBUG] Fallback error traceback: {traceback.format_exc()}")
                    
                    # Last resort fallback
                    return DocumentClassificationModel(
                        classification=DocumentClassification.INFORMATIONAL,
                        should_index=False,
                        confidence=ConfidenceLevel.LOW,
                        target_collection=CollectionName.RBI_OTHER,
                        reasoning="Emergency fallback due to multiple classification failures"
                    )
                
        except Exception as e:
            logger.error(f"❌ Error in LLM chunk classification: {e}")
            logger.error(f"❌ [DEBUG] Critical error in classification method: {str(e)}")
            logger.error(f"❌ [DEBUG] Error traceback: {traceback.format_exc()}")
            
            # Fall back to rule-based classification
            try:
                logger.info(f"🔍 [DEBUG] Attempting last-resort rule-based classification")
                return self.classify_chunk_for_indexing(chunk_content, document_metadata)
            except Exception as fallback_error:
                logger.error(f"❌ [DEBUG] Even last-resort fallback failed: {str(fallback_error)}")
                
                # Ultimate emergency fallback
                return DocumentClassificationModel(
                    classification=DocumentClassification.INFORMATIONAL,
                    should_index=False,
                    confidence=ConfidenceLevel.LOW,
                    target_collection=CollectionName.RBI_OTHER,
                    reasoning="Critical error in chunk classification pipeline"
                )

    def llm_get_list_actions_from_chunk(self, chunk_content: str, metadata: dict) -> List[DocumentAction]:
        """
        Use LLM to extract a list of actions from a document chunk.
        
        Args:
            chunk_content: The text content of the chunk
            metadata: Additional metadata for the chunk
            
        Returns:
            List[DocumentAction]: List of actions extracted from the chunk
        """
        import json
        import logging
        import traceback

        try:
            # Convert metadata to a string representation for the prompt
            if isinstance(metadata, dict):
                metadata_str = json.dumps(metadata, default=str, indent=2)
            else:
                metadata_str = str(metadata)

            # Create a system prompt for document action extraction
            system_prompt = """
You are an expert RBI regulatory document analyzer. Your task is to analyze notification chunks and extract document actions.
Identify any affected documents mentioned in the content and determine what action is required for each document.

For each document, you must extract:
1. document_id: Primary document identifier (reference number or title) - REQUIRED
2. action_type: One of [UPDATE_DOCUMENT, REMOVE_DOCUMENT, ADD_DOCUMENT, ADD_TEMPORARY_NOTE, NO_ACTION] - REQUIRED
3. confidence: Confidence level (high, medium, low) - REQUIRED
4. Optional fields if available: document_title, document_url, reference_number, department, original_date, update_location, sunset_withdraw_date, reasoning
"""

            user_prompt = f"""
Document Metadata:
{metadata_str}

Document Chunk:
{chunk_content}

Extract all affected documents and their required actions from this chunk.
Format your response as a JSON object with an 'actions' array containing document action objects.
Example:
{{
  "actions": [
    {{
      "document_id": "RBI/2024-25/123",
      "action_type": "UPDATE_DOCUMENT",
      "confidence": "high",
      "document_title": "Master Direction on Digital Lending",
      "update_location": "paragraph 5.2"
    }},
    {{
      "document_id": "Master Circular on Import of Goods",
      "action_type": "REMOVE_DOCUMENT", 
      "confidence": "medium",
      "sunset_withdraw_date": "2025-08-01"
    }}
  ]
}}

If no actions are found, return {{ "actions": [] }}
"""
            
            logging.info("Sending structured request to extract document actions")
            
            # Get completion with structured Pydantic output
            response = self.openai_manager.get_structured_output(
                prompt=f"{system_prompt}\n\n{user_prompt}",
                model='gpt-4',
                temperature=0.1,
                response_format=DocumentActionsList
            )
            
            logging.info(f"Received response type: {type(response)}")
            
            try:
                # Since we used response_model, the response should already be a Pydantic model
                if isinstance(response, DocumentActionsList):
                    logging.info(f"✅ Successfully received structured Pydantic output")
                    return list(response.actions)
                
                # Fallback: If we got a dict or string instead
                response_dict = response
                if isinstance(response, str):
                    # Parse JSON string response to dict
                    try:
                        response_dict = json.loads(response)
                        logging.info("Parsed JSON string response to dict")
                    except json.JSONDecodeError as e:
                        logging.error(f"JSON decode error: {e}")
                        logging.error(f"Raw response: {response[:500]}...")
                        return []
                
                # Try to convert dict to Pydantic model
                try:
                    actions_model = DocumentActionsList(**response_dict)
                    if not actions_model.actions:
                        logging.info("No document actions found in content")
                        return []
                    
                    # Log each action that was successfully created
                    for action in actions_model.actions:
                        logging.info(f"Created DocumentAction: {action.document_id}, {action.action_type}")
                        # write doc actions to file:
                        with open("debug_extracted_actions.json", "w", encoding="utf-8") as f:
                            json.dump(response_dict, f, indent=2, ensure_ascii=False, default=str)
                        
                    return list(actions_model.actions)
                
                except Exception as validation_error:
                    logging.error(f"Pydantic validation error: {str(validation_error)}")
                    
                    # Print more detailed validation errors to help debugging
                    if hasattr(validation_error, 'errors'):
                        for error in validation_error.errors():
                            logging.error(f"Validation error: {error}")
                    
                    logging.error(f"Response data: {json.dumps(response_dict, default=str)[:500]}...")
                    
                    # Try to recover by creating actions individually
                    actions_data = response_dict.get('actions', [])
                    if not actions_data:
                        logging.warning("No actions data found in response")
                        return []
                    
                    logging.info(f"Attempting to create {len(actions_data)} individual actions")
                    document_actions = []
                    for i, action_data in enumerate(actions_data):
                        try:
                            # Log the action data for debugging
                            logging.info(f"Processing action {i+1}: {json.dumps(action_data, default=str)[:200]}...")
                            
                            # Ensure required fields are present
                            if not action_data.get('document_id'):
                                action_data['document_id'] = f"unknown_doc_{i+1}"
                                logging.warning(f"Added missing document_id for action {i+1}")
                                
                            if not action_data.get('action_type'):
                                action_data['action_type'] = "NO_ACTION"
                                logging.warning(f"Added missing action_type for action {i+1}")
                                
                            if not action_data.get('confidence'):
                                action_data['confidence'] = "low"
                                logging.warning(f"Added missing confidence for action {i+1}")
                            
                            # Create DocumentAction one by one
                            action = DocumentAction(**action_data)
                            document_actions.append(action)
                            logging.info(f"✅ Created individual DocumentAction: {action.document_id}, {action.action_type}")
                        except Exception as e:
                            logging.error(f"❌ Error creating individual action {i+1}: {str(e)}")
                    
                    logging.info(f"Successfully created {len(document_actions)} individual actions")
                    return document_actions
            
            except Exception as parsing_error:
                logging.error(f"❌ Error parsing LLM response: {str(parsing_error)}")
                logging.error(f"❌ Stack trace: {traceback.format_exc()}")
                # Print raw response for debugging
                if isinstance(response, str):
                    logging.error(f"❌ Raw response (first 500 chars): {response[:500]}")
                elif isinstance(response, dict):
                    logging.error(f"❌ Raw response keys: {list(response.keys())}")
                return []
                        
        except Exception as e:
            logging.error(f"❌ Error in llm_get_list_actions_from_chunk: {str(e)}")
            logging.error(f"❌ Stack trace: {traceback.format_exc()}")
            logging.info("⚠️ Returning empty list of actions due to error")
            return []
    def determine_collection_for_document(self, document_type: str, classification: str, content: str = '') -> str:
        """
        Determine the appropriate collection for a document based on its type and classification.
        
        Args:
            document_type: Type of document (e.g., 'master_direction', 'notification')
            classification: Classification of the document content ('regulatory', 'informational', etc.)
            content: The document content for additional context (optional)
            
        Returns:
            str: The target collection name (rbi_circular, rbi_master_circular, rbi_master_direction, rbi_other)
        """
        # Normalize document type and classification
        document_type = document_type.lower() if document_type else ''
        classification = classification.lower() if classification else ''
        
        # Handle known document types directly
        if document_type == 'master_direction':
            return 'rbi_master_direction'
        elif document_type == 'master_circular':
            return 'rbi_master_circular'
        elif document_type == 'notification' or document_type == 'circular':
            return 'rbi_circular'
        
        # Handle based on classification
        if classification == 'regulatory':
            if 'circular' in document_type or 'notification' in document_type:
                return 'rbi_circular'
            elif 'master' in document_type:
                if 'direction' in document_type:
                    return 'rbi_master_direction'
                elif 'circular' in document_type:
                    return 'rbi_master_circular'
                else:
                    return 'rbi_master_direction'  # Default for master documents
            else:
                return 'rbi_circular'  # Default for regulatory documents
        elif classification == 'withdrawn':
            # Withdrawn documents still maintain their collection type
            if 'master direction' in document_type or 'master_direction' in document_type:
                return 'rbi_master_direction'
            elif 'master circular' in document_type or 'master_circular' in document_type:
                return 'rbi_master_circular'
            elif 'circular' in document_type or 'notification' in document_type:
                return 'rbi_circular'
            else:
                return 'rbi_other'
        else:
            # For informational or administrative content
            return 'rbi_other'

    def llm_classify_chunk_for_kb_action(self, chunk_content: str, metadata: dict) -> KBActionModel:
        """
        Use LLM to classify chunks and determine the appropriate action for the knowledge base
        with smart model selection.
        
        Args:
            chunk_content: The text content of the chunk
            metadata: Additional metadata for the chunk

        Returns:
            KBActionModel: Pydantic model with classification and action determination
        """
        try:
            logger.debug(f"Metadata: {metadata}")
            logger.debug(f"Chunk content: {chunk_content}")

            # Determine if this is a complex KB action that needs LLM analysis
            complex_indicators = [
                'supersed', 'withdraw', 'repeal', 'amend', 'modify', 'replace',
                'consolidat', 'master circular', 'master direction', 'notification',
                'in supersession of', 'hereby withdrawn', 'hereby repealed'
            ]
            
            is_complex = any(indicator in chunk_content.lower() for indicator in complex_indicators)
            
            if is_complex and len(chunk_content) > 200:
                # Use LLM for complex KB action determination
                try:
                    kb_schema = KBActionModel.get_json_schema()
                    
                    kb_prompt = f"""
You are an expert RBI regulatory knowledge base manager. Analyze this document chunk and determine the appropriate knowledge base action.

METADATA:
- Document Type: {metadata.get('document_type', 'unknown')}
- Watermark Status: {metadata.get('watermark_status', 'unknown')}
- Year: {metadata.get('year', 'unknown')}

CHUNK CONTENT:
{chunk_content}

Determine:
1. Action type (ADD_DOCUMENT, REMOVE_DOCUMENT, UPDATE_DOCUMENT, LINK_DOCUMENT, NO_ACTION)
2. Target collection (rbi_circular, rbi_master_circular, rbi_master_direction, rbi_other)
3. Document classification (regulatory, informational, administrative, withdrawn)
4. Whether to store/remove the document
5. Confidence level and reasoning

Focus on regulatory compliance requirements and document relationships.
"""
                    
                    # Add schema to prompt
                    schema_json = json.dumps(kb_schema, indent=2)
                    schema_prompt = f"{kb_prompt}\n\nYou must respond with a JSON object that matches this schema:\n{schema_json}"
                    
                    logger.info("🧠 Using LLM for complex KB action determination")
                    
                    llm_response = self.openai_manager.get_completion(
                        schema_prompt,
                        model=None,  # Let smart selection choose
                        temperature=0.1,
                        response_format={"type": "json_object"},
                        task_type='supersession_detection',  # Complex task for GPT-4.1
                        priority='quality'  # Prioritize quality for KB decisions
                    )
                    
                    # Parse LLM response
                    kb_decision = KBActionModel.parse_raw(llm_response)
                    logger.info(f"🤖 LLM KB Decision: {kb_decision.action_type.value} -> {kb_decision.target_collection.value}")
                    return kb_decision
                    
                except Exception as e:
                    logger.warning(f"LLM KB action determination failed: {e}, falling back to rule-based")
            
            # Rule-based determination for simple cases or LLM fallback
            if metadata.get('watermark_status') in ['WITHDRAWN', 'SUPERSEDED', 'REPEALED']:
                action = ActionType.REMOVE_DOCUMENT
                classification = DocumentClassification.WITHDRAWN
                should_store = False
                should_remove = True
                confidence = ConfidenceLevel.HIGH
                reasoning = f"Document has {metadata.get('watermark_status')} status"
            elif 'update' in chunk_content.lower() or 'amend' in chunk_content.lower():
                action = ActionType.UPDATE_DOCUMENT
                classification = DocumentClassification.REGULATORY
                should_store = True
                should_remove = False
                confidence = ConfidenceLevel.MEDIUM
                reasoning = "Content indicates document update"
            # Enhanced detection for linked documents
            elif any(keyword in chunk_content.lower() for keyword in [
                'reference', 'link', 'refer', 'please see', 'please read',
                'in conjunction with', 'along with', 'related to',
                'pursuant to', 'with respect to', 'in connection with'
            ]):
                action = ActionType.LINK_DOCUMENT
                classification = DocumentClassification.INFORMATIONAL
                should_store = True
                should_remove = False
                confidence = ConfidenceLevel.MEDIUM
                reasoning = "Content indicates document linking/referencing"
            else:
                action = ActionType.ADD_DOCUMENT
                classification = DocumentClassification.REGULATORY
                should_store = True
                should_remove = False
                confidence = ConfidenceLevel.MEDIUM
                reasoning = "Default to add document when no specific action detected"
            
            # Determine collection
            doc_type = metadata.get('document_type', '').lower()
            collection = self.determine_collection_for_document(
                doc_type,
                classification.value.lower(),
                chunk_content
            )
            
            # Map string to enum
            collection_enum = None
            if collection == 'rbi_circular':
                collection_enum = CollectionName.RBI_CIRCULAR
            elif collection == 'rbi_master_circular':
                collection_enum = CollectionName.RBI_MASTER_CIRCULAR
            elif collection == 'rbi_master_direction':
                collection_enum = CollectionName.RBI_MASTER_DIRECTION
            else:
                collection_enum = CollectionName.RBI_OTHER

            # Create and return the model
            kb_decision = KBActionModel(
                action_type=action,
                target_collection=collection_enum,
                classification=classification,
                should_store=should_store,
                should_remove=should_remove,
                reasoning=reasoning,
                confidence=confidence
            )
            logger.info(f"✅ Determined action: {action.value}, classification: {classification.value}")
            return kb_decision

        except Exception as e:
            logger.error(f"Error in action determination: {e}")
            # Return a fallback model instance
            return KBActionModel(
                action_type=ActionType.NO_ACTION,
                target_collection=CollectionName.RBI_OTHER,
                classification=DocumentClassification.INFORMATIONAL,
                should_store=False,
                should_remove=False,
                reasoning=f"Fallback reasoning due to error: {str(e)}",
                confidence=ConfidenceLevel.LOW
            )
    
    def process_found_document_for_chunking(self, document_info: dict, notification_data: dict, chunk_metadata: dict = None) -> dict:
        """
        Process documents found by LLM by downloading, chunking, and preparing them for knowledge base addition.
        
        Args:
            document_info: Information about the document from LLM analysis
            notification_data: Original notification data
            chunk_metadata: Metadata from the chunk that identified this document
            
        Returns:
            dict: Processing results including chunks marked for addition
        """
        try:
            # Detailed logging of input parameters
            logger.info(f"🔖 [DEBUG] Processing document: {document_info.get('document_title', 'Unknown')}")
            # Removed verbose JSON logging to reduce log noise
            
            logger.info(f"📄 Processing found document for chunking: {document_info.get('document_title', 'Unknown')}")
            
            # Extract document details
            document_url = document_info.get('document_url', '')
            document_title = document_info.get('document_title', '')
            document_type = document_info.get('document_type', '')
            action_type = document_info.get('action_type', '')
            collection_name = document_info.get('collection_name', 'rbi_other')
            
            logger.info(f"🔍 [DEBUG] Extracted details:")
            logger.info(f"🔍 [DEBUG] - Document URL: {document_url}")
            logger.info(f"🔍 [DEBUG] - Document Title: {document_title}")
            logger.info(f"🔍 [DEBUG] - Document Type: {document_type}")
            logger.info(f"🔍 [DEBUG] - Action Type: {action_type}")
            logger.info(f"🔍 [DEBUG] - Collection Name: {collection_name}")

            if not document_url:
                # take rbi_page_url
                document_url = notification_data.get('rbi_page_url', '')
                logger.info(f"🔍 [DEBUG] No document_url provided, using rbi_page_url: {document_url}")

            if not document_url:
                # take PDF Link
                document_url = notification_data.get('PDF Link', '')
                logger.info(f"🔍 [DEBUG] No document_url or rbi_page_url provided, using PDF Link: {document_url}")

            if not document_url:
                logger.warning("⚠️  No document URL provided, skipping chunking")
                logger.info(f"🔍 [DEBUG] Document info missing URL - Title: {document_info.get('document_title', 'Unknown')}")
                # Removed verbose JSON logging to reduce log noise
                return {'status': 'skipped', 'reason': 'No URL provided'}
        
            # Download and extract content from the found document
            logger.info(f"⬇️  Downloading document: {document_url}")
            
            try:
                found_doc_chunks, chunk_notification_code, new_document_title, new_s3_url = self.download_and_extract_pdf_content(
                    document_url, 
                    max_chars=None,  # No limit for full processing
                    return_chunks=True
                )
                logger.info(f"🔍 [DEBUG] Document download successful: {document_url}")
            except Exception as e:
                logger.error(f"❌ [DEBUG] Document download failed: {document_url}, Error: {str(e)}")
                logger.error(f"❌ [DEBUG] Error type: {type(e).__name__}")
                logger.error(f"❌ [DEBUG] Error traceback: {traceback.format_exc()}")
                return {'status': 'failed', 'reason': f'Failed to download: {str(e)}'}
            
            if not found_doc_chunks:
                logger.error(f"❌ Failed to extract content from document: {document_url}")
                logger.error(f"🔍 [DEBUG] Document download result: {found_doc_chunks}")
                return {'status': 'failed', 'reason': 'Failed to extract content'}
            
            logger.info(f"✅ Extracted {len(found_doc_chunks)} chunks from found document")
            logger.info(f"🔍 [DEBUG] First chunk sample: {json.dumps(found_doc_chunks[0] if found_doc_chunks else {}, indent=2, default=str)[:500]}...")
            
            # Process each chunk with LLM to determine if it should be indexed
            processed_chunks = []
            chunks_to_add = []
            
            logger.info(f"🔍 [DEBUG] Starting to analyze {len(found_doc_chunks)} document chunks for indexing")
            
            for i, chunk in enumerate(found_doc_chunks):
                if not isinstance(chunk, dict):
                    logger.warning(f"🔍 [DEBUG] Chunk {i+1} is not a dictionary: {type(chunk)}")
                    continue
                    
                chunk_content = chunk.get('content', '')
                if not chunk_content.strip():
                    logger.warning(f"🔍 [DEBUG] Chunk {i+1} has empty content")
                    continue
                
                # Log chunk details for debugging
                logger.info(f"🔍 [DEBUG] Chunk {i+1} details:")
                logger.info(f"🔍 [DEBUG] - Content length: {len(chunk_content)} chars")
                logger.info(f"🔍 [DEBUG] - Page number: {chunk.get('page_number', 'N/A')}")
                logger.info(f"🔍 [DEBUG] - Section title: {chunk.get('section_title', 'N/A')}")
                # Removed content preview to reduce log noise
                
                logger.info(f"   🔍 Analyzing chunk {i+1}/{len(found_doc_chunks)} for indexing decision")
                
                # Use LLM classification for better accuracy
                try:
                    classification = self.llm_classify_chunk_for_indexing(
                        chunk_content, 
                        {
                            'document_type': document_type,
                            'source_url': document_url
                        },
                        {
                            'notification_title': notification_data.get('Title', ''),
                            'notification_date': notification_data.get('Date', '')
                        }
                    )
                    
                    logger.info(f"🔍 [DEBUG] Classification result for chunk {i+1}:")
                    logger.info(f"🔍 [DEBUG] - Should index: {classification.should_index}")
                    logger.info(f"🔍 [DEBUG] - Classification: {classification.classification}")
                    logger.info(f"🔍 [DEBUG] - Target collection: {classification.target_collection}")
                    logger.info(f"🔍 [DEBUG] - Reasoning: {classification.reasoning}")
                except Exception as e:
                    logger.error(f"❌ [DEBUG] Error classifying chunk {i+1}: {str(e)}")
                    logger.error(f"❌ [DEBUG] Error traceback: {traceback.format_exc()}")
                    # Skip this chunk if classification fails
                    continue
                
                # Enhance chunk with metadata using the Pydantic model
                try:
                    enhanced_chunk = {
                        **chunk,
                        'chunk_index': i,  # Add explicit chunk index
                        'source_document_url': document_url,
                        'source_document_title': document_title,
                        'source_document_type': document_type,
                        'target_collection': classification.target_collection.value,
                        'classification': classification.classification.value,
                        'should_index': classification.should_index,
                        'reasoning': classification.reasoning,
                        'confidence': classification.confidence.value,
                        'linked_notification_title': notification_data.get('Title', ''),
                        'linked_notification_date': notification_data.get('Date', ''),
                        'linked_notification_watermark': notification_data.get('Watermark', ''),
                        'discovery_chunk_metadata': chunk_metadata,
                        'processing_timestamp': datetime.now().isoformat()
                    }
                    
                    logger.info(f"🔍 [DEBUG] Created enhanced chunk metadata for chunk {i+1}")
                    processed_chunks.append(enhanced_chunk)
                    
                    # If LLM determines this chunk should be indexed, prepare it for addition
                    if classification.should_index and classification.classification != DocumentClassification.WITHDRAWN:
                        logger.info(f"   ✅ Chunk {i+1} marked for addition: {classification.reasoning}")
                        
                        document_id = chunk_notification_code.get('full_code','')
                        short_id = chunk_notification_code.get('short_code','')

                        logger.info(f"################## 🔍 [DEBUG] Document ID: {document_id} #################")
                        logger.info(f"################## 🔍 [DEBUG] Short ID: {short_id} ########################")
                        
                        logger.info(f"🔍 [DEBUG] Creating KB action for chunk {i+1}:")
                        logger.info(f"🔍 [DEBUG] - Document ID: {document_id}")
                        logger.info(f"🔍 [DEBUG] - Short ID: {short_id}")
                        logger.info(f"🔍 [DEBUG] - Target collection: {classification.target_collection.value}")
                        logger.info(f"New S3 URL: {new_s3_url if new_s3_url else 'Not provided'}")
                        # Create KB action for this chunk with enhanced uniqueness metadata
                        processing_timestamp = datetime.now().isoformat()
                        kb_action = {
                            'action_type': 'ADD_DOCUMENT',
                            'collection_name': classification.target_collection.value,
                            'target_document': document_id,  # Use document_id instead of content for better ID generation
                            'timestamp': processing_timestamp,  # Add timestamp for unique UUID generation
                            'payload': {
                                'document_id': document_id,
                                'short_id': short_id,
                                'title': new_document_title,
                                'content': chunk_content,
                                'page_content': chunk_content,  # Ensure both content fields are available
                                'document_type': document_type,
                                'source_url': document_url,
                                'source_notification': notification_data.get('Title', ''),
                                'classification': classification.classification.value,
                                'confidence': classification.confidence.value,
                                'chunk_index': i,
                                'total_chunks': len(found_doc_chunks),
                                # 'positions': chunk.get('positions', []),
                                'section_title': chunk.get('section_title', ''),
                                'page_number': chunk.get('page_number', ''),
                                'processing_timestamp': processing_timestamp,
                                'metadata': {
                                    'discovered_by_llm': True,
                                    'discovery_source': notification_data.get('Title', ''),
                                    'discovery_chunk': chunk_metadata.get('chunk_index') if chunk_metadata else None,
                                    'processing_method': 'automatic_document_discovery',
                                    'added_timestamp': processing_timestamp,
                                    'chunk_index': i,  # Include chunk index in metadata for UUID generation
                                    'unique_id': f"{document_id}_{i}_{processing_timestamp}"
                                },
                                "s3_url": new_s3_url if new_s3_url else None
                            }
                        }
                        
                        logger.info(f"🔍 [DEBUG] KB action successfully created for chunk {i+1}")
                        
                        # Add the chunk to the list for KB updates
                        chunks_to_add.append({
                            'chunk_data': enhanced_chunk,
                            'kb_action': kb_action
                        })
                except Exception as e:
                    logger.error(f"❌ [DEBUG] Error creating enhanced chunk {i+1}: {str(e)}")
                    logger.error(f"❌ [DEBUG] Error traceback: {traceback.format_exc()}")
                    continue
                else:
                    logger.info(f"   ✅ Chunk {i+1} added to processing queue")
            
            # Execute KB actions for chunks marked for addition
            kb_results = []
            added_uuids = []
            
            logger.info(f"🔍 [DEBUG] Total chunks marked for addition: {len(chunks_to_add)} out of {len(found_doc_chunks)} total chunks")
            
            if chunks_to_add:
                logger.info(f"🗂️  Executing KB additions for {len(chunks_to_add)} chunks from found document")
                
                # Process each chunk individually to ensure unique UUIDs
                for chunk_idx, chunk_item in enumerate(chunks_to_add):
                    try:
                        kb_action = chunk_item['kb_action']
                        chunk_data = chunk_item['chunk_data']
                        original_chunk_index = chunk_data.get('chunk_index', chunk_idx)
                        
                        logger.info(f"🔍 [DEBUG] Processing chunk {chunk_idx+1}/{len(chunks_to_add)} (original index: {original_chunk_index})")
                        
                        # Execute single chunk with specific metadata
                        kb_execution_result = self.knowledge_base_executor.execute_updates(
                            [kb_action], 
                            notification_data,
                            chunk_metadata={
                                'source': 'found_document', 
                                'document_url': document_url,
                                'chunk_index': original_chunk_index,  # Use the actual chunk index
                                'processing_timestamp': datetime.now().isoformat(),
                                'batch_index': chunk_idx,  # Add batch processing index
                                'unique_suffix': f"chunk_{original_chunk_index}_{chunk_idx}"
                            }
                        )
                        kb_results.extend(kb_execution_result)
                        
                        logger.info(f"🔍 [DEBUG] Chunk {chunk_idx+1} KB execution completed with {len(kb_execution_result)} results")
                        
                        # Process results for this specific chunk
                        for result_idx, result in enumerate(kb_execution_result):
                            logger.info(f"🔍 [DEBUG] Chunk {chunk_idx+1} result {result_idx+1}: {result.get('action', 'UNKNOWN')} - {'SUCCESS' if result.get('success') else 'FAILED'}")
                            
                            if result.get('success'):
                                # Try to get UUID from result - check multiple possible locations
                                uuid_val = result.get('uuid')
                                
                                # If not found directly, try to extract from details string
                                if not uuid_val and result.get('details'):
                                    uuid_match = re.search(r'ID:\s*([a-f0-9-]{36})', result.get('details', ''))
                                    if uuid_match:
                                        uuid_val = uuid_match.group(1)
                                
                                if uuid_val:
                                    added_uuids.append(uuid_val)
                                    logger.info(f"🆔 Successfully added found document chunk {chunk_idx+1} with UUID: {uuid_val}")
                                else:
                                    logger.warning(f"⚠️ [DEBUG] Could not extract UUID from successful result for chunk {chunk_idx+1}: {result.get('details', 'No details')}")
                            else:
                                logger.error(f"❌ [DEBUG] KB action failed for chunk {chunk_idx+1}: {result.get('details', 'No error details')}")
                        
                    except Exception as e:
                        logger.error(f"❌ [DEBUG] Error executing KB action for chunk {chunk_idx+1}: {str(e)}")
                        logger.error(f"❌ [DEBUG] Error traceback: {traceback.format_exc()}")
                        kb_results.append({
                            'success': False,
                            'error': f"Exception occurred during KB execution for chunk {chunk_idx+1}: {str(e)}",
                            'details': traceback.format_exc(),
                            'chunk_index': chunk_idx
                        })
                
                logger.info(f"✅ KB additions completed for found document chunks. Total UUIDs added: {len(added_uuids)}")
                logger.info(f"🔍 [DEBUG] UUIDs added: {added_uuids}")
                
                # Add successful chunks to metadata_vectors collection for enhanced searchability
                if added_uuids and chunks_to_add:
                    logger.info(f"📊 Adding {len(chunks_to_add)} chunks to metadata_vectors collection for enhanced search")
                    try:
                        notification_metadata = {
                            'title': document_title,
                            'document_type': document_type,
                            'source_url': document_url,
                            'notification_link': notification_data.get('Link', ''),
                            'pdf_link': notification_data.get('PDF Link', ''),
                            'processing_timestamp': datetime.now().isoformat()
                        }
                        
                        metadata_vectors_uuids = self._add_chunks_to_metadata_vectors(
                            chunks_to_add, document_title, notification_metadata
                        )
                        
                        logger.info(f"📊 Successfully added {len(metadata_vectors_uuids)} chunks to metadata_vectors")
                        added_uuids.extend(metadata_vectors_uuids)
                        
                    except Exception as e:
                        logger.error(f"❌ Error adding chunks to metadata_vectors: {e}")
            
            # Compile complete result with debugging information
            result = {
                'status': 'success',
                'document_url': document_url,
                'document_title': document_title,
                'document_type': document_type,
                'collection_name': collection_name,
                'total_chunks_extracted': len(found_doc_chunks),
                'total_chunks_processed': len(processed_chunks),
                'chunks_marked_for_addition': len(chunks_to_add),
                'processed_chunks': processed_chunks,
                'chunks_added_to_kb': chunks_to_add,
                'kb_execution_results': kb_results,
                'added_document_uuids': added_uuids,
                'processing_timestamp': datetime.now().isoformat(),
                'debug_info': {
                    'input_document_info': document_info,
                    'input_chunk_metadata': chunk_metadata,
                    'notification_title': notification_data.get('Title', ''),
                    'kb_execution_success_count': len([r for r in kb_results if r.get('success')]),
                    'kb_execution_error_count': len([r for r in kb_results if not r.get('success')]),
                }
            }
            
            # Final debug log with complete result summary
            logger.info(f"🔍 [DEBUG] Document processing completed successfully")
            logger.info(f"🔍 [DEBUG] Summary stats:")
            logger.info(f"🔍 [DEBUG] - Total chunks extracted: {len(found_doc_chunks)}")
            logger.info(f"🔍 [DEBUG] - Chunks processed: {len(processed_chunks)}")
            logger.info(f"🔍 [DEBUG] - Chunks marked for addition: {len(chunks_to_add)}")
            logger.info(f"🔍 [DEBUG] - UUIDs added: {len(added_uuids)}")
            logger.info(f"🔍 [DEBUG] - UUIDs: {added_uuids}")
            logger.info(f"🔍 [DEBUG] - KB execution success rate: {len([r for r in kb_results if r.get('success')])} / {len(kb_results)}")
            
            return result
            
        except Exception as e:
            # Enhanced error logging
            logger.error(f"❌ Error processing found document for chunking: {e}")
            logger.error(f"❌ [DEBUG] Error type: {type(e).__name__}")
            logger.error(f"❌ [DEBUG] Error traceback: {traceback.format_exc()}")
            logger.error(f"❌ [DEBUG] Document info: {json.dumps(document_info, default=str)}")
            
            # Return detailed error information
            return {
                'status': 'error',
                'error': str(e),
                'error_type': type(e).__name__,
                'document_url': document_info.get('document_url', ''),
                'document_title': document_info.get('document_title', ''),
                'processing_timestamp': datetime.now().isoformat(),
                'debug_traceback': traceback.format_exc()
            }

    def evaluate_notification_for_kb_storage(self, categorization_result: dict, title: str, content: str, link: str) -> NotificationEvaluationModel:
        """
        Evaluate whether this notification should be stored in or removed from the knowledge base.
        Returns a Pydantic model with the evaluation results.
        """
        try:
            category = categorization_result.get('category', '').lower()
            confidence = categorization_result.get('confidence', '').lower()
            requires_kb_update = categorization_result.get('requires_kb_update', False)
            
            # Get the schema from our Pydantic model
            kb_evaluation_schema = NotificationEvaluationModel.get_json_schema()
            
            # Create evaluation prompt for LLM
            evaluation_prompt = f"""
You are an expert RBI knowledge base manager. Analyze this notification and determine if it should be stored in the knowledge base.

NOTIFICATION DETAILS:
Title: {title}
Category: {category}
Confidence: {confidence}
Requires KB Update: {requires_kb_update}
Link: {link}

CONTENT:
{content}

Based on this notification, determine:
1. Should this notification be processed? (true/false)
2. Should this notification be stored in the knowledge base? (true/false)
3. If stored, what collection should it go to? (rbi_circular, rbi_master_circular, rbi_master_direction, rbi_other)
4. Should any existing documents be removed/superseded? (true/false)
5. What type of processing is needed? (store_new, update_existing, remove_existing, no_action)
6. Confidence level in your decision (high, medium, low)
7. Reasoning for your decision

Consider:
- Regulatory notifications should generally be stored
- Withdrawn/superseded documents should trigger removal of old versions
- Administrative notifications may not need storage
- Informational content may need storage if it aids compliance
"""
            
            # Add schema to the prompt
            schema_json = json.dumps(kb_evaluation_schema, indent=2)
            schema_prompt = f"{evaluation_prompt}\n\nYou must respond with a JSON object that matches this schema:\n{schema_json}"
            
            # Get LLM evaluation with structured output
            llm_response = self.openai_manager.get_completion(
                schema_prompt,
                model=None,  # Let smart selection choose
                temperature=0.1,
                response_format={"type": "json_object"},
                task_type='regulatory_interpretation',  # Complex task for GPT-4.1
                priority='quality'  # Prioritize quality for KB decisions
            )
            
            try:
                # Parse with our Pydantic model for validation
                kb_decision = NotificationEvaluationModel.parse_raw(llm_response)
            except ValidationError as e:
                logger.error(f"Validation error in LLM response: {e}")
                # Use fallback as a Pydantic model
                fallback = self._get_fallback_kb_decision(category, requires_kb_update)
                kb_decision = NotificationEvaluationModel(
                    should_process=fallback.get('should_process', False),
                    should_store=fallback.get('should_store', False),
                    target_collection=CollectionName.RBI_OTHER,
                    should_remove_existing=fallback.get('should_remove_existing', False),
                    processing_type=ProcessingType.NO_ACTION,
                    reasoning=fallback.get('reasoning', 'Fallback decision due to validation error'),
                    confidence=ConfidenceLevel.LOW
                )
            
            logger.info(f"📋 KB Decision: {kb_decision.processing_type.value} "
                      f"(store: {kb_decision.should_store}, remove: {kb_decision.should_remove_existing})")
            
            return kb_decision
            
        except Exception as e:
            logger.error(f"Error evaluating notification for KB storage: {e}")
            fallback = self._get_fallback_kb_decision(category, requires_kb_update)
            return NotificationEvaluationModel(
                should_process=fallback.get('should_process', False),
                should_store=fallback.get('should_store', False),
                target_collection=CollectionName.RBI_OTHER,
                should_remove_existing=fallback.get('should_remove_existing', False),
                processing_type=ProcessingType.NO_ACTION,
                reasoning=fallback.get('reasoning', f"Fallback decision due to error: {str(e)}"),
                confidence=ConfidenceLevel.LOW
            )
    
    def process_notification_pdf_for_kb(self, title: str, link: str, categorization_result: dict, content: str, local_pdf_path: str = None) -> dict:
        """
        Process the PDF associated with this notification for knowledge base storage
        """
        try:
            logger.info(f"📄 Processing notification PDF for KB storage: {title[:50]}...")
            # Always use local PDF path if available
            pdf_path = None
            s3_url = None
            
            # Validate local_pdf_path
            if local_pdf_path:
                try:
                    # Convert to string and then Path to ensure valid path
                    pdf_path = str(local_pdf_path)
                    pdf_path_obj = Path(pdf_path)
                    
                    # Verify path exists
                    if not pdf_path_obj.exists():
                        logger.warning(f"⚠️ Local PDF path does not exist: {pdf_path}")
                        pdf_path = None
                except Exception as e:
                    logger.error(f"❌ Error validating local PDF path: {e}")
                    pdf_path = None
            
            # Upload PDF to S3 if available
            if pdf_path:
                try:
                    from utils.s3_utils import upload_file_to_s3
                    
                    # Create S3 key based on notification title and category
                    clean_title = title.replace('/', '_').replace('\\', '_').replace(' ', '_')[:50]
                    collection_name = categorization_result.get('kb_decision', {}).get('target_collection', 'rbi_notification')
                    s3_key = f"notifications/{collection_name}/{clean_title}.pdf"
                    
                    # Upload to S3 - ensure pdf_path is converted to string
                    logger.info(f"Attempting to upload file from: {pdf_path} (type: {type(pdf_path)})")
                    
                    # Ensure pdf_path is a valid string path
                    if pdf_path is None:
                        raise ValueError("PDF path is None")
                    
                                    # Convert to absolute path and ensure it's a string
                    pdf_path_str = str(pdf_path)
                    
                    # Debug the path type
                    logger.info(f"PDF path type before conversion: {type(pdf_path)}")
                    
                    # Convert to absolute path if it's relative
                    pdf_file = Path(pdf_path_str).absolute()
                    pdf_path_str = str(pdf_file)
                    logger.info(f"Using absolute PDF path for upload: {pdf_path_str}")
                    logger.info(f"PDF path type after conversion: {type(pdf_path_str)}")
                    
                    # Ensure the file exists before attempting to upload
                    if not pdf_file.exists():
                        raise FileNotFoundError(f"PDF file does not exist: {pdf_path_str}")
                        
                    # Upload file with proper path handling and extra error handling
                    try:
                        # Read a small bit of the file to verify it's readable
                        with open(pdf_path_str, 'rb') as test_file:
                            # Just read a few bytes to test
                            test_bytes = test_file.read(10)
                            logger.info(f"File is readable, first few bytes: {test_bytes}")
                            
                        # Ensure all metadata values are strings
                        metadata = {
                            "document_id": str(clean_title),
                            "collection": str(collection_name),
                            "document_type": "notification"
                        }
                        
                        # Debug metadata values
                        logger.info(f"S3 upload metadata: {metadata}")
                        print(pdf_path_str)
                        upload_result = upload_file_to_s3(
                            local_file_path=pdf_path_str,
                            s3_key=s3_key,
                            metadata=metadata
                        )
                    except IOError as io_err:
                        logger.error(f"IO error when trying to read the file: {io_err}")
                        raise
                    except TypeError as type_err:
                        if "expected string or bytes-like object" in str(type_err):
                            logger.error(f"TypeError during S3 upload: {type_err}. This is likely due to an issue with the file path.")
                            # Try a different approach - use bytes mode
                            try:
                                with open(pdf_path_str, 'rb') as file:
                                    from io import BytesIO
                                    file_content = BytesIO(file.read())
                                    
                                from utils.s3_utils import get_s3_client
                                from utils.config import config
                                
                                s3_client = get_s3_client()
                                logger.info(f"Attempting alternative upload method using boto3 put_object")
                                
                                # Get the bucket name from configuration
                                bucket_name = "localdocdump"
                                
                                response = s3_client.put_object(
                                    Body=file_content,
                                    Bucket=bucket_name,
                                    Key=s3_key,
                                    Metadata=metadata
                                )
                                print(f"Response from S3 put_object: {response}")
                                # s3_url = f"s3://{bucket_name}/{s3_key}"
                                region_name = 'ap-south-1'  # Adjust as needed
                                url = f"https://{bucket_name}.s3.{region_name}amazonaws.com/{s3_key}"
                                upload_result = {
                                    "success": True,
                                    # "s3_url": s3_url,
                                    "s3_url": url,
                                    "bucket": bucket_name,
                                    "key": s3_key
                                }
                                logger.info(f"Alternative upload succeeded: {s3_url}")
                            except Exception as alt_err:
                                logger.error(f"Alternative upload method also failed: {alt_err}")
                                raise
                        else:
                            logger.error(f"Unexpected TypeError during S3 upload preparation: {type_err}")
                            raise
                    except Exception as upload_err:
                        logger.error(f"Unexpected error during S3 upload preparation: {upload_err}")
                        raise
                    
                    if upload_result["success"]:
                        # Store S3 URL for use in chunks
                        s3_url = upload_result["s3_url"]
                        logger.info(f"✅ Uploaded notification PDF to S3: {s3_url}")
                    else:
                        logger.warning(f"⚠️ Failed to upload notification PDF to S3: {upload_result.get('error')}")
                except Exception as e:
                    logger.error(f"❌ Error uploading notification PDF to S3: {e}")
            
            if pdf_path:
                logger.info(f"📥 Using local PDF for chunking: {pdf_path}")
                try:
                    pdf_chunks, _ = self.extract_pdf_from_local_file(pdf_path)
                except Exception as e:
                    logger.error(f"❌ Failed to extract local PDF content: {e}")
                    return {
                        'status': 'pdf_failed',
                        'reason': 'PDF extraction failed',
                        'notification_content_only': True,
                        'pdf_path': pdf_path,
                        's3_url': s3_url
                    }
            else:
                logger.warning(f"⚠️  No valid local PDF path found for notification: {title}")
                return {
                    'status': 'no_pdf',
                    'reason': 'No valid PDF path found',
                    'notification_content_only': True
                }
            logger.info(f"✅ Extracted {len(pdf_chunks)} chunks from local PDF")
            # Process each chunk with LLM to determine if it should be indexed
            processed_chunks = []
            chunks_for_storage = []
            chunks_for_removal = []
            for i, chunk in enumerate(pdf_chunks):
                if not isinstance(chunk, dict) or not chunk.get('content', '').strip():
                    continue
                logger.info(f"   🔍 Analyzing chunk {i+1}/{len(pdf_chunks)} for storage decision")
                # Use LLM to classify chunk and determine storage action
                chunk_decision = self.llm_classify_chunk_for_kb_action(
                    chunk.get('content', ''),
                    {
                        'notification_title': title,
                        'notification_category': categorization_result.get('category'),
                        'pdf_url': pdf_path,
                        'chunk_index': i,
                        'total_chunks': len(pdf_chunks)
                    }
                )
                # Enhance chunk with metadata and decision using Pydantic model
                enhanced_chunk = {
                    **chunk,
                    'chunk_index': i,
                    'source_notification_title': title,
                    'source_notification_link': link,
                    'source_pdf_url': pdf_path,
                    'kb_action': chunk_decision.action_type.value,
                    'target_collection': chunk_decision.target_collection.value,
                    'classification': chunk_decision.classification.value,
                    'should_store': chunk_decision.should_store,
                }
                
                # Add S3 URL to chunk metadata if available
                if s3_url:
                    enhanced_chunk['s3_url'] = s3_url
                
                processed_chunks.append(enhanced_chunk)
                
                # If LLM determines this chunk should be indexed, prepare it for addition
                if chunk_decision.should_store and chunk_decision.classification.value != 'withdrawn':
                    logger.info(f"   ✅ Chunk {i+1} marked for storage: {chunk_decision.reasoning}")
                    
                    # Create unique document ID with timestamp for uniqueness
                    chunk_timestamp = datetime.now().isoformat()
                    chunk_document_id = f"{title}_chunk_{i}_{int(datetime.now().timestamp())}"
                    
                    # Create KB action for this chunk with correct variables in this context
                    kb_action = {
                        'action_type': 'ADD_DOCUMENT',
                        'collection_name': chunk_decision.target_collection.value,
                        'target_document': chunk_document_id,
                        'timestamp': chunk_timestamp,  # Add timestamp for unique UUID generation
                        'payload': {
                            'document_id': chunk_document_id,
                            'title': f"{title} - Chunk {i+1}",
                            'content': chunk.get('content', ''),
                            'page_content': chunk.get('content', ''),  # Ensure both content fields are available
                            'document_type': 'notification_chunk',
                            'source_notification_title': title,
                            'source_notification_link': link,
                            'classification': chunk_decision.classification.value,
                            'confidence': chunk_decision.confidence.value,
                            'chunk_index': i,
                            'total_chunks': len(pdf_chunks),
                            'positions': chunk.get('positions', []),
                            'section_title': chunk.get('section_title', ''),
                            'page_number': chunk.get('page_number', ''),
                            'processing_timestamp': chunk_timestamp,
                            'metadata': {
                                'source_notification_title': title,
                                'source_pdf': pdf_path,
                                'processing_method': 'notification_pdf_processing',
                                'added_timestamp': chunk_timestamp,
                                'chunk_index': i,  # Include chunk index in metadata for UUID generation
                                'unique_id': f"{chunk_document_id}_{i}_{chunk_timestamp}"
                            }
                        }
                    }
                    
                    # Add S3 URL to both top-level payload and metadata if available
                    if s3_url:
                        kb_action['payload']['s3_url'] = s3_url
                        kb_action['payload']['metadata']['s3_url'] = s3_url
                    
                    chunks_for_storage.append({
                        'chunk_data': enhanced_chunk,
                        'kb_action': kb_action
                    })
                else:
                    logger.info(f"   ⏭️  Chunk {i+1} skipped: {chunk_decision.reasoning}")
            
            # Execute KB actions for chunks marked for storage
            kb_results = []
            if chunks_for_storage:
                logger.info(f"🗂️  Executing KB storage for {len(chunks_for_storage)} chunks from PDF")
                
                kb_actions = [item['kb_action'] for item in chunks_for_storage]
                # Create a notification metadata object for the KB executor
                notification_metadata = {
                    'Title': title,
                    'Link': link,
                    'PDF Link': pdf_path
                }
                
                kb_execution_result = self.knowledge_base_executor.execute_updates(
                    kb_actions, 
                    notification_metadata,
                    chunk_metadata={'source': 'notification_pdf', 'document_url': pdf_path, 's3_url': s3_url, 'total_chunks': len(chunks_for_storage)}
                )
                kb_results.extend(kb_execution_result)
                
                # Extract and log UUIDs from successful operations
                added_uuids = []
                for result in kb_execution_result:
                    if result.get('success'):
                        # Try to get UUID from result - check multiple possible locations
                        uuid_val = result.get('uuid')
                        
                        # If not found directly, try to extract from details string
                        if not uuid_val and result.get('details'):
                            uuid_match = re.search(r'ID:\s*([a-f0-9-]{36})', result.get('details', ''))
                            if uuid_match:
                                uuid_val = uuid_match.group(1)
                        
                        if uuid_val:
                            added_uuids.append(uuid_val)
                            logger.info(f"🆔 Successfully added PDF chunk with UUID: {uuid_val}")
                        else:
                            logger.warning(f"⚠️ Could not extract UUID from successful result: {result.get('details', 'No details')}")
                    else:
                        logger.warning(f"⚠️ KB action failed: {result.get('details', 'No error details')}")
                
                logger.info(f"✅ KB storage completed for PDF chunks. Total UUIDs added: {len(added_uuids)}")
            
            return {
                'status': 'success',
                'pdf_url': pdf_path,
                'total_chunks_extracted': len(pdf_chunks),
                'chunks_processed': len(processed_chunks),
                'chunks_marked_for_storage': len(chunks_for_storage),
                'processed_chunks': processed_chunks,
                'chunks_added_to_kb': chunks_for_storage,
                'kb_execution_results': kb_results,
                'added_document_uuids': added_uuids,
                'processing_timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"❌ Error processing notification PDF for KB storage: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'processing_timestamp': datetime.now().isoformat()
            }


    def _get_fallback_kb_decision(self, category: str, requires_kb_update: bool) -> dict:
        """
        Fallback KB decision when LLM evaluation fails.
        For backward compatibility, this still returns a dictionary
        which can be used to create a Pydantic model.
        """
        normalized = (category or '').replace('_', '').replace('-', '').lower()
        regulatory_categories = [
            'regulatory', 'notification', 'amendment', 'newregulatoryissuance', 'regulatoryupdate', 'regulatoryissuance'
        ]
        if normalized in regulatory_categories or requires_kb_update:
            return {
                "should_process": True,
                "should_store": True,
                "target_collection": "rbi_circular",
                "should_remove_existing": False,
                "processing_type": "store_new",
                "reasoning": f"Fallback: {category} notifications are typically stored",
                "confidence": "low"
            }
        else:
            return {
                "should_process": False,
                "should_store": False,
                "target_collection": "rbi_other",
                "should_remove_existing": False,
                "processing_type": "no_action",
                "reasoning": f"Fallback: {category} notifications typically don't require storage",
                "confidence": "low"
            }

    def process_notification_pdf_for_kb(self, title: str, link: str, categorization_result: dict, content: str, local_pdf_path: str = None) -> dict:
        """
        Process the PDF associated with this notification for knowledge base storage
        """
        try:
            logger.info(f"📄 Processing notification PDF for KB storage: {title[:50]}...")
            # Always use local PDF path if available
            pdf_path = local_pdf_path or ''
            if pdf_path and Path(pdf_path).exists():
                logger.info(f"📥 Using local PDF for chunking: {pdf_path}")
                try:
                    pdf_chunks, _ = self.extract_pdf_from_local_file(pdf_path)
                except Exception as e:
                    logger.error(f"❌ Failed to extract local PDF content: {e}")
                    return {
                        'status': 'pdf_failed',
                        'reason': 'PDF extraction failed',
                        'notification_content_only': True,
                        'pdf_path': pdf_path
                    }
            else:
                logger.warning(f"⚠️  No local PDF path found or file does not exist for notification: {title}")
                return {
                    'status': 'no_pdf',
                    'reason': 'No local PDF found',
                    'notification_content_only': True
                }
            logger.info(f"✅ Extracted {len(pdf_chunks)} chunks from local PDF")
            # Process each chunk with LLM to determine if it should be indexed
            processed_chunks = []
            chunks_for_storage = []
            chunks_for_removal = []
            for i, chunk in enumerate(pdf_chunks):
                if not isinstance(chunk, dict) or not chunk.get('content', '').strip():
                    continue
                logger.info(f"   🔍 Analyzing chunk {i+1}/{len(pdf_chunks)} for storage decision")
                # Use LLM to classify chunk and determine storage action
                chunk_decision = self.llm_classify_chunk_for_kb_action(
                    chunk.get('content', ''),
                    {
                        'notification_title': title,
                        'notification_category': categorization_result.get('category'),
                        'pdf_url': pdf_path,
                        'chunk_index': i,
                        'total_chunks': len(pdf_chunks)
                    }
                )
                # Enhance chunk with metadata and decision using Pydantic model
                enhanced_chunk = {
                    **chunk,
                    'chunk_index': i,
                    'source_notification_title': title,
                    'source_notification_link': link,
                    'source_pdf_url': pdf_path,
                    'kb_action': chunk_decision.action_type.value,
                    'target_collection': chunk_decision.target_collection.value,
                    'classification': chunk_decision.classification.value,
                    'should_store': chunk_decision.should_store,
                    'should_remove': chunk_decision.should_remove,
                    'reasoning': chunk_decision.reasoning,
                    'confidence': chunk_decision.confidence.value,
                    'processing_timestamp': datetime.now().isoformat()
                }
                processed_chunks.append(enhanced_chunk)
                # Categorize chunks based on LLM decision
                if chunk_decision.should_store:
                    # Create KB action for storage chunks
                    chunk_timestamp = datetime.now().isoformat()
                    chunk_document_id = f"{title}_chunk_{i}_{int(datetime.now().timestamp())}"
                    
                    kb_action = {
                        'action_type': 'ADD_DOCUMENT',
                        'collection_name': chunk_decision.target_collection.value,
                        'target_document': chunk_document_id,
                        'timestamp': chunk_timestamp,  # Add timestamp for unique UUID generation
                        'payload': {
                            'document_id': chunk_document_id,
                            'title': f"{title} - Chunk {i+1}",
                            'content': chunk.get('content', ''),
                            'page_content': chunk.get('content', ''),  # Ensure both content fields are available
                            'document_type': 'notification_chunk',
                            'source_notification_title': title,
                            'source_notification_link': link,
                            'classification': chunk_decision.classification.value,
                            'confidence': chunk_decision.confidence.value,
                            'chunk_index': i,
                            'total_chunks': len(pdf_chunks),
                            'positions': chunk.get('positions', []),
                            'section_title': chunk.get('section_title', ''),
                            'page_number': chunk.get('page_number', ''),
                            'processing_timestamp': chunk_timestamp,
                            'metadata': {
                                'source_notification_title': title,
                                'source_pdf': pdf_path,
                                'processing_method': 'notification_pdf_processing_v2',
                                'added_timestamp': chunk_timestamp,
                                'chunk_index': i,  # Include chunk index in metadata for UUID generation
                                'unique_id': f"{chunk_document_id}_{i}_{chunk_timestamp}"
                            }
                        }
                    }
                    
                    # Store both the enhanced chunk and its KB action
                    chunks_for_storage.append({
                        'chunk_data': enhanced_chunk,
                        'kb_action': kb_action
                    })
                    logger.info(f"   ✅ Chunk {i+1} marked for storage: {chunk_decision.reasoning}")
                elif chunk_decision.should_remove:
                    chunks_for_removal.append(enhanced_chunk)
                    logger.info(f"   🗑️  Chunk {i+1} triggers removal: {chunk_decision.reasoning}")
                else:
                    logger.info(f"   ⏭️  Chunk {i+1} no action needed: {chunk_decision.reasoning}")
            
            # Execute KB actions for chunks marked for storage
            kb_results = []
            added_chunk_uuids = []
            if chunks_for_storage:
                logger.info(f"🗂️  Executing KB storage for {len(chunks_for_storage)} chunks from PDF")
                
                kb_actions = [item['kb_action'] for item in chunks_for_storage]
                # Create a notification metadata object for the KB executor
                notification_metadata = {
                    'Title': title,
                    'Link': link,
                    'PDF Link': pdf_path
                }
                
                kb_execution_result = self.knowledge_base_executor.execute_updates(
                    kb_actions, 
                    notification_metadata,
                    chunk_metadata={'source': 'notification_pdf_v2', 'document_url': pdf_path, 'total_chunks': len(chunks_for_storage)}
                )
                kb_results.extend(kb_execution_result)
                
                # Extract and log UUIDs from successful operations
                for result in kb_execution_result:
                    if result.get('success') and result.get('uuid'):
                        added_chunk_uuids.append(result.get('uuid'))
                    elif result.get('success') and 'ID:' in result.get('details', ''):
                        uuid_match = re.search(r'ID:\s*([a-f0-9-]{36})', result.get('details', ''))
                        if uuid_match:
                            uuid_val = uuid_match.group(1)
                            added_chunk_uuids.append(uuid_val)
                
                # ✅ CRITICAL: Also add chunks to metadata_vectors collection for enhanced searchability
                logger.info(f"📊 Adding {len(chunks_for_storage)} chunks to metadata_vectors collection for enhanced search")
                metadata_vectors_uuids = self._add_chunks_to_metadata_vectors(chunks_for_storage, title, notification_metadata)
                added_chunk_uuids.extend(metadata_vectors_uuids)
                
                logger.info(f"✅ KB storage completed for PDF chunks. Total UUIDs added: {len(added_chunk_uuids)}")
                logger.info(f"   📊 Metadata vectors UUIDs: {len(metadata_vectors_uuids)}")
            
            return {
                'status': 'success',
                'pdf_url': pdf_path,
                'total_chunks_extracted': len(pdf_chunks),
                'chunks_processed': len(processed_chunks),
                'chunks_for_storage': len(chunks_for_storage),
                'chunks_for_removal': len(chunks_for_removal),
                'processed_chunks': processed_chunks,
                'storage_chunks': chunks_for_storage,
                'removal_chunks': chunks_for_removal,
                'kb_execution_results': kb_results,
                'added_chunk_uuids': added_chunk_uuids,
                'processing_timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"❌ Error processing notification PDF for KB: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'processing_timestamp': datetime.now().isoformat()
            }

    def search_document_by_metadata(self, field: str, query_text: str, top_k: int = 5) -> List[Dict]:
        """
        Search for documents using vector similarity on document metadata
        
        Args:
            field: The metadata field to search on (e.g., document_id, short_summary, document_number)
            query_text: The text to search for
            top_k: Number of results to return
            
        Returns:
            List of search hits with payload and score
        """
        if not self.metadata_search_model:
            logger.warning("⚠️ Metadata vector search not available - cannot search document metadata")
            return []
            
        try:
            # Generate query embedding using SentenceTransformer model
            query_embedding = self.metadata_search_model.encode(query_text).tolist()
            
            # Search using named vector field
            logger.info(f"[QDRANT] SEARCH: Collection={self.metadata_collection}, Field={field}, Query={query_text[:50]}")
            hits = self.qdrant_client.search(
                collection_name=self.metadata_collection,
                query_vector=(f"{field}_vec", query_embedding),
                limit=top_k,
                with_payload=True,
                with_vectors=False
            )
            
            # Convert Qdrant hits to dictionary format
            results = []
            for hit in hits:
                results.append({
                    'id': hit.id,
                    'payload': hit.payload,
                    'score': hit.score
                })
            
            logger.info(f"[QDRANT] SEARCH RESULTS: Found {len(results)} matches")
            return results
        except Exception as e:
            logger.error(f"❌ Error searching document metadata: {e}")
            return []
            
    def find_similar_documents(self, document_id: str, top_k: int = 5) -> List[Dict]:
        """
        Find documents similar to a given document ID
        
        Args:
            document_id: The document ID to find similar documents for
            top_k: Number of results to return
            
        Returns:
            List of similar documents
        """
        return self.search_document_by_metadata("document_id", document_id, top_k=top_k)
    
    def find_superseded_documents(self, document_id: str, document_number: str) -> List[Dict]:
        """
        Find documents that might be superseded by this document
        
        Args:
            document_id: The document ID to check
            document_number: The document number to check
            
        Returns:
            List of potentially superseded documents
        """
        # First search by document number
        number_hits = self.search_document_by_metadata("document_number", document_number, top_k=3)
        
        # Then search by document ID
        id_hits = self.search_document_by_metadata("document_id", document_id, top_k=3)
        
        # Combine results, removing duplicates by ID
        combined_hits = []
        seen_ids = set()
        
        for hit in number_hits + id_hits:
            hit_id = hit.id
            if hit_id not in seen_ids:
                seen_ids.add(hit_id)
                combined_hits.append(hit)
        
        return combined_hits
        
    def process_notification(self, notification: dict) -> dict:
        """
        Process a single notification through the complete pipeline including KB storage and ensure chunks are also added/linked to affected documents in DB.
        """
        # Always prefer local PDF path for content extraction
        title = notification.get('Title', '')
        link = notification.get('Link', '')
        local_pdf_path = notification.get('Local Path') or notification.get('local_pdf_path')
        # If local PDF path is missing or file does not exist, try to download from PDF Link
        if (not local_pdf_path or not Path(local_pdf_path).exists()) and notification.get('PDF Link'):
            pdf_url = notification.get('PDF Link')
            try:
                logger.info(f"Downloading PDF from {pdf_url}")
                import requests, tempfile
                response = requests.get(pdf_url)
                if response.status_code == 200:
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp:
                        tmp.write(response.content)
                        local_pdf_path = tmp.name
                    logger.info(f"Downloaded PDF to {local_pdf_path}")
                else:
                    logger.warning(f"Failed to download PDF from {pdf_url}, status code: {response.status_code}")
            except Exception as e:
                logger.error(f"Error downloading PDF from {pdf_url}: {e}")
        content = ''
        used_pdf = False
        if local_pdf_path and Path(local_pdf_path).exists():
            logger.info(f"🔄 Extracting content from local PDF: {local_pdf_path}")
            try:
                chunks, _ = self.extract_pdf_from_local_file(local_pdf_path)
                content = '\n\n'.join(chunk.get('content', '') for chunk in chunks if chunk.get('content'))
                used_pdf = True
            except Exception as e:
                logger.error(f"Failed to extract content from local PDF: {e}")
                content = ''
        # Ensure 'local_path' is used if notification length is zero
        if not content.strip() and local_pdf_path:
            logger.info(f"🔄 Using 'local_path' to extract content as notification length is zero")
            try:
                chunks, _ = self.extract_pdf_from_local_file(local_pdf_path)
                content = '\n\n'.join(chunk.get('content', '') for chunk in chunks if chunk.get('content'))
                used_pdf = True
            except Exception as e:
                logger.error(f"Failed to extract content from 'local_path': {e}")
                content = ''
        # Fallback to Description if no valid PDF
        if not content.strip():
            content = notification.get('Description', '')
        logger.info(f"🚀 Processing notification: {title[:50]}... (used_pdf={used_pdf})")
        try:
            # 1. Analyze notification and get KB decision
            logger.info("--- Stage 1: Notification Analysis ---")
            analysis_result = self.notification_processor.analyze_notification(title, content, link)
            kb_decision_model = analysis_result.get('kb_decision') or self.evaluate_notification_for_kb_storage(
                analysis_result, title, content, link
            )
            logger.info(f"Analysis: {analysis_result}")
            logger.info(f"KB Decision: {kb_decision_model}")
            
            # Convert Pydantic model to dict for legacy methods that expect dict
            kb_decision = {
                'should_process': kb_decision_model.should_process,
                'should_store': kb_decision_model.should_store,
                'should_remove_existing': kb_decision_model.should_remove_existing,
                'target_collection': kb_decision_model.target_collection.value,
                'processing_type': kb_decision_model.processing_type.value,
                'reasoning': kb_decision_model.reasoning,
                'confidence': kb_decision_model.confidence.value
            }

            # 2. Document Type Detection and Flowchart-Based KB Actions
            logger.info("--- Stage 2: Document Type Detection & Flowchart Processing ---")
            
            # Detect document type from title and analysis
            document_type = self._detect_document_type(title, analysis_result)
            logger.info(f"� Document Type Detected: {document_type}")
            
            # Generate KB actions based on flowchart logic
            notif_actions = self._generate_flowchart_based_actions(
                document_type, title, content, analysis_result, kb_decision, notification
            )
            
            # Log all generated actions
            logger.info(f"🎯 Generated {len(notif_actions)} KB actions based on flowchart:")
            for i, action in enumerate(notif_actions, 1):
                action_type = action.get('action_type', 'UNKNOWN')
                collection = action.get('collection_name', 'UNKNOWN')
                target = action.get('target_document', action.get('filter_fields', {}).get('metadata.title', 'N/A'))
                reasoning = action.get('reasoning', 'No reasoning provided')
                logger.info(f"  📝 Action {i}: {action_type} in {collection}")
                logger.info(f"      Target: {target}")
                logger.info(f"      Reasoning: {reasoning}")
            notification_kb_results = []
            added_uuids = []  # Track UUIDs for better monitoring
            if notif_actions:
                logger.info(f"✅ Executing {len(notif_actions)} notification KB actions")
                for idx, action in enumerate(notif_actions, 1):
                    logger.info(f"  Action {idx}: {action.get('action_type')} for collection {action.get('collection_name')}")
                
                notification_kb_results = self.knowledge_base_executor.execute_updates(
                    notif_actions, notification
                )
                
                # Log results with clear action types, reasoning, and extract UUIDs
                logger.info(f"✅ Notification KB Results summary:")
                for idx, result in enumerate(notification_kb_results, 1):
                    action_type = result.get('action', 'UNKNOWN')
                    success = result.get('success', False)
                    status = "SUCCESS" if success else "FAILED"
                    
                    # Get corresponding action for reasoning and confidence
                    corresponding_action = notif_actions[idx-1] if idx <= len(notif_actions) else {}
                    reasoning = corresponding_action.get('reasoning', 'N/A')
                    confidence = corresponding_action.get('confidence', 'N/A')
                    collection = corresponding_action.get('collection_name', 'N/A')
                    
                    # Extract UUID from result if available
                    uuid_value = None
                    if success and action_type == "ADD_DOCUMENT":
                        # First try to get from extra data
                        uuid_value = result.get('uuid')
                        
                        # If not found, try to extract from details string
                        if not uuid_value and result.get('details'):
                            uuid_match = re.search(r'ID:\s*([a-f0-9-]{36})', result.get('details', ''))
                            if uuid_match:
                                uuid_value = uuid_match.group(1)
                        
                        if uuid_value:
                            added_uuids.append(uuid_value)
                            logger.info(f"  📋 Result {idx}: {action_type} - {status}")
                            logger.info(f"      📦 Collection: {collection}")
                            logger.info(f"      🆔 UUID: {uuid_value}")
                            logger.info(f"      🤔 Reasoning: {reasoning}")
                            logger.info(f"      📊 Confidence: {confidence}")
                        else:
                            logger.info(f"  📋 Result {idx}: {action_type} - {status}")
                            logger.info(f"      📦 Collection: {collection}")
                            logger.info(f"      🤔 Reasoning: {reasoning}")
                            logger.info(f"      📊 Confidence: {confidence}")
                    else:
                        logger.info(f"  📋 Result {idx}: {action_type} - {status}")
                        logger.info(f"      📦 Collection: {collection}")
                        logger.info(f"      🤔 Reasoning: {reasoning}")
                        logger.info(f"      📊 Confidence: {confidence}")
                        if not success:
                            error_details = result.get('error', result.get('details', 'No error details'))
                            logger.error(f"      ❌ Error: {error_details}")
                
                logger.info(f"📊 Added {len(added_uuids)} documents with UUIDs: {added_uuids}")
            else:
                logger.info("⏭️ No notification KB actions to execute")

            # 3. Process notification PDF for KB storage/removal (always use local PDF if available)
            logger.info("--- Stage 3: PDF Processing for KB ---")
            pdf_processing = None
            pdf_chunks = []
            if kb_decision['should_process']:
                pdf_processing = self.process_notification_pdf_for_kb(
                    title, link, analysis_result, content, local_pdf_path=local_pdf_path
                )
                pdf_chunks = pdf_processing.get('processed_chunks', []) if pdf_processing else []
            logger.info(f"PDF Processing: {pdf_processing}")

            # 4. Extract affected documents
            logger.info("--- Stage 4: Extract Affected Documents ---")
            affected_documents = {}
            try:
                # Get all chunks from the notification
                notification_chunks = pdf_processing.get('chunks', []) if pdf_processing else []
                
                affected_documents = self.notification_processor.extract_affected_documents(
                    title, content, analysis_result.get('category', ''),
                    notification_codes={},  # Add any notification codes if available
                    chunks=notification_chunks
                )
            except Exception as e:
                logger.error(f"Error extracting affected documents: {e}")
            logger.info(f"Affected Documents: {affected_documents}")

            # 5. Determine update actions
            logger.info("--- Stage 5: Determine Update Actions ---")
            update_actions = []
            try:
                update_result = self.notification_processor.determine_update_actions(
                    title,
                    analysis_result.get('category', ''),
                    affected_documents.get('document_actions', []),
                    content,
                    notification  # Pass notification data for URL extraction
                )
                update_actions = update_result.get('actions', [])
                # write update actions to file 
                with open("debug_update_actions.json", "w", encoding="utf-8") as f:
                    json.dump(update_actions, f, indent=2, ensure_ascii=False, default=str)
            except Exception as e:
                logger.error(f"Error determining update actions: {e}")
            logger.info(f"Update Actions: {update_actions}")

            # 6. Process found documents for chunking and KB updates
            logger.info("--- Stage 6: Process Found Documents for Chunking ---")
            found_document_results = []
            for action in update_actions:
                try:
                    res = self.process_found_document_for_chunking(
                        action, notification, chunk_metadata=None
                    )
                    found_document_results.append(res)
                except Exception as e:
                    logger.error(f"Error processing found document for chunking: {e}")
                    continue
            logger.info(f"Found Document Results: {found_document_results}")

            # 7. Process unique new document URLs that haven't been processed yet
            logger.info("--- Stage 7: Process New Document URLs ---")
            new_document_urls = set()
            # From affected_documents
            if affected_documents:
                if isinstance(affected_documents.get('new_document_url'), str) and affected_documents['new_document_url']:
                    new_document_urls.add(affected_documents['new_document_url'])
                # Also check for pdf_links list
                for url in affected_documents.get('pdf_links', []):
                    if url:
                        new_document_urls.add(url)
            # From update_actions
            for action in update_actions:
                url = action.get('new_document_url')
                if url:
                    new_document_urls.add(url)
            
            # Remove any URLs already processed as found documents
            already_processed = set()
            for res in found_document_results:
                if isinstance(res, dict) and res.get('document_url'):
                    already_processed.add(res['document_url'])
            
            # Process only truly new document URLs
            new_document_results = []
            for url in new_document_urls:
                if url and url not in already_processed:
                    try:
                        doc_info = {
                            'document_url': url, 
                            'document_title': f"New Document: {url.split('/')[-1]}", 
                            'document_type': 'regulatory_document', 
                            'collection_name': 'rbi_other'
                        }
                        res = self.process_found_document_for_chunking(doc_info, notification)
                        new_document_results.append(res)
                    except Exception as e:
                        logger.error(f"Error processing new_document_url {url}: {e}")
                        new_document_results.append({'status': 'error', 'document_url': url, 'error': str(e)})
            logger.info(f"New Document Results: {new_document_results}")

            # 8. Link notification chunks to affected documents (no duplication with stages 6-7)
            logger.info("--- Stage 8: Link Notification Chunks to Affected Documents ---")
            enhanced_processing_results = []
            enhanced_linking_uuids = []
            
            # Extract notification codes for this notification (fallback for regular process_notification)
            notification_codes = notification.get('notification_codes', {})
            if not notification_codes:
                # Extract codes from title as fallback
                notification_codes = self.extract_notification_codes_from_text(title) if hasattr(self, 'extract_notification_codes_from_text') else {}
            
            try:
                # Only link chunks to affected documents identified in stage 4 (no document processing duplication)
                if pdf_chunks and affected_documents.get('document_actions'):
                    logger.info(f"📎 Linking {len(pdf_chunks)} notification chunks to {len(affected_documents['document_actions'])} affected documents")
                    
                    for doc_action in affected_documents['document_actions']:
                        doc_id = doc_action.get('document_identifier') or doc_action.get('reference') or doc_action.get('document_id')
                        collection = doc_action.get('collection', 'rbi_circular')
                        
                        if not doc_id:
                            continue
                            
                        # Create document info for linking only
                        doc_info = {
                            'document_url': doc_action.get('document_url', ''),
                            'document_title': f"Affected Document: {doc_id}",
                            'document_type': 'regulatory_document',
                            'collection_name': collection,
                            'document_id': doc_id
                        }
                        
                        # Link notification chunks to this existing document
                        link_results = self._link_notification_chunks_to_document(
                            pdf_chunks, doc_info, notification, notification_codes
                        )
                        enhanced_processing_results.extend(link_results)
                        
                        # Extract UUIDs from linking results
                        for result in link_results:
                            if result.get('success') and result.get('uuid'):
                                enhanced_linking_uuids.append(result.get('uuid'))
                                
                    logger.info(f"✅ Completed linking chunks to {len(affected_documents['document_actions'])} affected documents")
                else:
                    logger.info(f"ℹ️ No chunks or affected documents to link")
                
                logger.info(f"📊 Linking operations completed: {len(enhanced_processing_results)}")
                logger.info(f"📊 Linking UUIDs generated: {len(enhanced_linking_uuids)}")
                
            except Exception as e:
                logger.error(f"❌ Error in notification chunk linking: {e}")

            # Compile final result
            result = {
                'notification': notification,
                'analysis': analysis_result,
                'kb_decision': kb_decision,
                'notification_kb_results': notification_kb_results,
                'pdf_processing': pdf_processing,
                'affected_documents': affected_documents,
                'update_actions': update_actions,
                'found_document_results': found_document_results,
                'new_document_results': new_document_results,
                'enhanced_processing_results': enhanced_processing_results,
                'enhanced_linking_uuids': enhanced_linking_uuids,
                'processing_timestamp': datetime.now().isoformat(),
                'status': 'success'
            }
            logger.info(f"✅ Notification processing completed: {title[:50]}")
            return result
        except Exception as e:
            logger.error(f"❌ Error processing notification: {e}")
            return {
                'notification': notification,
                'error': str(e),
                'processing_timestamp': datetime.now().isoformat(),
                'status': 'error'
            }
    
    def process_notification_enhanced(self, notification: dict) -> dict:
        """
        Enhanced notification processing with comprehensive UUID tracking and metadata handling.
        """
        # Initialize tracking variables
        title = notification.get('Title', '')
        link = notification.get('Link', '')
        local_pdf_path = notification.get('Local Path') or notification.get('local_pdf_path')
        
        # Extract comprehensive notification codes for better metadata
        notification_codes = self.extract_notification_codes_from_text(title)
        
        # UUID tracking variables
        all_added_uuids = []
        all_updated_uuids = []
        all_removed_uuids = []
        operation_details = []
        
        logger.info(f"🚀 ENHANCED Processing notification: {title[:50]}...")
        logger.info(f"📊 Initial notification codes from title: {notification_codes}")
        
        # Handle PDF content extraction
        content, local_pdf_path = self._extract_notification_content(notification, local_pdf_path)
        
        if local_pdf_path and Path(local_pdf_path).exists():
            try:
                logger.info(f"📄 Extracting notification codes from PDF: {local_pdf_path}")
                _, pdf_notification_codes = self.extract_pdf_from_local_file(local_pdf_path)
                
                # Merge PDF codes with title codes (PDF codes take priority if not empty)
                for key, value in pdf_notification_codes.items():
                    if value and value != '':  # Only update if PDF code is not empty
                        notification_codes[key] = value
                        
                logger.info(f"📋 Enhanced codes after PDF extraction: {notification_codes}")
                logger.info(f"   📋 Short code: {notification_codes.get('short_code', 'Not found')}")
                logger.info(f"   📋 Long code: {notification_codes.get('long_code', 'Not found')}")
                logger.info(f"   📋 Full code: {notification_codes.get('full_code', 'Not found')}")
                logger.info(f"   📅 Year: {notification_codes.get('year', 'Not found')}")
                logger.info(f"   🏷️ Watermark status: {notification_codes.get('watermark_status', 'UNKNOWN')}")
                
            except Exception as e:
                logger.error(f"❌ Failed to extract codes from PDF: {e}")
                logger.info(f"📋 Using codes from title only: {notification_codes}")
        else:
            logger.info(f"⚠️ No local PDF available, using codes from title only: {notification_codes}")
        
        # Update notification with extracted codes
        notification['notification_codes'] = notification_codes
        
        try:
            # Stage 1: Enhanced Analysis with better metadata
            logger.info("--- ENHANCED Stage 1: Notification Analysis ---")
            analysis_result = self.notification_processor.analyze_notification(title, content, link)
            kb_decision_model = analysis_result.get('kb_decision') or self.evaluate_notification_for_kb_storage(
                analysis_result, title, content, link
            )
            
            # Convert Pydantic model to dict for legacy methods that expect dict
            kb_decision = {
                'should_process': kb_decision_model.should_process,
                'should_store': kb_decision_model.should_store,
                'should_remove_existing': kb_decision_model.should_remove_existing,
                'target_collection': kb_decision_model.target_collection.value,
                'processing_type': kb_decision_model.processing_type.value,
                'reasoning': kb_decision_model.reasoning,
                'confidence': kb_decision_model.confidence.value
            }
            
            # Stage 2: Enhanced notification-level KB actions with metadata
            logger.info("--- ENHANCED Stage 2: Notification-level KB Actions ---")
            logger.info(f"notification_codes being passed to KB storage: {notification_codes}")
            notification_uuids, notif_results = self._process_notification_kb_actions(
                notification, kb_decision, content, link, notification_codes
            )
            all_added_uuids.extend(notification_uuids)
            operation_details.extend(notif_results)
            
            # Stage 3: STREAMLINED PDF processing with action execution
            logger.info("--- ENHANCED Stage 3: STREAMLINED PDF Processing and Action Execution ---")
            if local_pdf_path and Path(local_pdf_path).exists():
                try:
                    # Extract chunks from PDF
                    chunks, chunk_notification_codes = self.extract_pdf_from_local_file(local_pdf_path)
                    
                    # Update notification codes with PDF data
                    for key, value in chunk_notification_codes.items():
                        if value and value != '':
                            notification_codes[key] = value
                    
                    # Process chunks in streamlined manner
                    processed_chunks, chunk_uuids, executed_chunk_actions = self._process_chunks_streamlined(
                        chunks, notification, notification_codes
                    )
                    
                    all_added_uuids.extend(chunk_uuids)
                    
                    # Track operation details
                    operation_details.append({
                        'operation': 'add',
                        'uuid_count': len(chunk_uuids),
                        'success': len(chunk_uuids) > 0,
                        'operation_type': 'pdf_chunks_streamlined',
                        'executed_actions': len(executed_chunk_actions),
                        'timestamp': datetime.utcnow().isoformat()
                    })
                    
                    
                    logger.info(f"✅ STREAMLINED PDF processing: {len(processed_chunks)} chunks, {len(executed_chunk_actions)} actions executed")
                    pdf_processing = {
                        'success': True,
                        'chunks': processed_chunks,
                        'chunk_uuids': chunk_uuids,
                        'executed_actions': executed_chunk_actions,
                        'local_pdf_path': local_pdf_path,
                        'notification_codes': notification_codes,
                        'processing_timestamp': datetime.now().isoformat()
                        }
                except Exception as e:
                    logger.error(f"❌ Error in streamlined PDF processing: {e}")
                    pdf_processing = {'success': False, 'error': str(e)}
            else:
                logger.info("⚠️ No local PDF available for streamlined processing")
                pdf_processing = {'success': False, 'error': 'No local PDF available'}
            
            # # Stage 4: Extract affected documents with enhanced metadata
            # logger.info("--- ENHANCED Stage 4: Extract Affected Documents ---")
            # affected_documents = self._extract_affected_documents_enhanced(
            #     title, content, analysis_result.get('category', ''), notification_codes
            # )
            
            # # Stage 4.5: Execute affected document actions (ADD/REMOVE)
            # logger.info("--- ENHANCED Stage 4.5: Execute Affected Document Actions ---")
            # affected_doc_results, affected_doc_uuids = self._execute_affected_document_actions_enhanced(
            #     affected_documents, notification, notification_codes
            # )
            # all_added_uuids.extend(affected_doc_uuids.get('added', []))
            # all_removed_uuids.extend(affected_doc_uuids.get('removed', []))
            # # Add operation details for affected document actions
            # operation_details.extend(affected_doc_results.get('operation_details', []))
            
            # # Stage 5: Determine update actions with better tracking
            # logger.info("--- ENHANCED Stage 5: Determine Update Actions ---")
            # update_actions = self._determine_update_actions_enhanced(
            #     title, analysis_result.get('category', ''), affected_documents, content, notification
            # )
            
            return {
                'notification': notification,
                'analysis': analysis_result,
                'kb_decision': kb_decision,
                'notification_codes': notification_codes,
                'pdf_processing': pdf_processing,
                'added_uuids': all_added_uuids,
                'operation_details': operation_details,
                'processing_timestamp': datetime.now().isoformat(),
                'status': 'success'
            }

        except Exception as e:
            logger.error(f"❌ Error processing notification: {e}")
            return {
                'notification': notification,
                'error': str(e),
                'processing_timestamp': datetime.now().isoformat(),
                'status': 'error'
            }
    
    def _extract_notification_content(self, notification: dict, local_pdf_path: str) -> str:
        """Extract content from notification with fallbacks"""
        content = ''
        
        # Try local PDF first
        if local_pdf_path and Path(local_pdf_path).exists():
            try:
                chunks, _ = self.extract_pdf_from_local_file(local_pdf_path)
                content = '\n\n'.join(chunk.get('content', '') for chunk in chunks if chunk.get('content'))
                logger.info(f"✅ Extracted {len(content)} chars from local PDF")
            except Exception as e:
                logger.error(f"Failed to extract from local PDF: {e}")
        
        # Try downloading if local failed
        if not content.strip() and notification.get('PDF Link'):
            try:
                logger.info(f"📥 Downloading PDF from {notification['PDF Link']}")
                response = requests.get(notification['PDF Link'])
                if response.status_code == 200:
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp:
                        tmp.write(response.content)
                        local_pdf_path = tmp.name
                    chunks, _ = self.extract_pdf_from_local_file(local_pdf_path)
                    content = '\n\n'.join(chunk.get('content', '') for chunk in chunks if chunk.get('content'))
                    logger.info(f"✅ Downloaded and extracted {len(content)} chars")
            except Exception as e:
                logger.error(f"Failed to download/extract PDF: {e}")
        
        # Fallback to description
        if not content.strip():
            content = notification.get('Description', '')
            logger.info(f"📝 Using description content: {len(content)} chars")

        return content, local_pdf_path

    def _process_notification_kb_actions(self, notification: dict, kb_decision: dict, content: str, link: str, notification_codes: dict) -> tuple:
        """Process notification-level KB actions with FLOWCHART-BASED logic and enhanced metadata tracking"""
        # Get title for document type detection
        title = notification.get('Title', '')
        
        # Stage 1: Document Type Detection using flowchart logic
        logger.info("🔍 Detecting document type for flowchart-based processing...")
        document_type = self._detect_document_type_enhanced(title, kb_decision)
        logger.info(f"📋 Document Type Detected: {document_type}")
        
        # Stage 2: Generate flowchart-based KB actions
        notif_actions = self._generate_flowchart_actions_enhanced(
            document_type, title, content, kb_decision, notification, notification_codes
        )
        
        # Log all generated actions with detailed reasoning
        logger.info(f"🎯 Generated {len(notif_actions)} KB actions based on flowchart:")
        for i, action in enumerate(notif_actions, 1):
            action_type = action.get('action_type', 'UNKNOWN')
            collection = action.get('collection_name', 'UNKNOWN')
            target = action.get('target_document', action.get('filter_fields', {}).get('metadata.title', 'N/A'))
            reasoning = action.get('reasoning', 'No reasoning provided')
            confidence = action.get('confidence', 'N/A')
            logger.info(f"  📝 Action {i}: {action_type} in {collection}")
            logger.info(f"      🎯 Target: {target}")
            logger.info(f"      🤔 Reasoning: {reasoning}")
            logger.info(f"      📊 Confidence: {confidence}")
        
        # Stage 3: Upload PDF to S3 if available
        notification_uuids = []
        s3_url = None
        
        local_pdf_path = notification.get('Local Path') or notification.get('local_pdf_path')
        if local_pdf_path and Path(local_pdf_path).exists():
            try:
                from utils.s3_utils import upload_file_to_s3
                
                # Create S3 key based on notification data
                doc_id = notification_codes.get('full_code', notification.get('Title', ''))
                clean_id = doc_id.replace('/', '_').replace('\\', '_').replace(' ', '_')
                collection_name = kb_decision['target_collection']
                s3_key = f"notifications/{collection_name}/{clean_id}.pdf"
                
                # Upload to S3
                upload_result = upload_file_to_s3(
                    local_file_path=local_pdf_path,
                    s3_key=s3_key,
                    metadata={
                        "document_id": doc_id,
                        "collection": collection_name,
                        "document_type": "notification"
                    }
                )
                
                if upload_result["success"]:
                    # Store S3 URL for use in payload
                    s3_url = upload_result["s3_url"]
                    logger.info(f"✅ Uploaded notification PDF to S3: {s3_url}")
                else:
                    logger.warning(f"⚠️ Failed to upload notification PDF to S3: {upload_result.get('error')}")
            except Exception as e:
                logger.error(f"❌ Error uploading notification PDF to S3: {e}")
        
        # Stage 4: Execute KB actions with enhanced logging
        notif_results = []
        if notif_actions:
            logger.info(f"✅ Executing {len(notif_actions)} flowchart-based KB actions")
            for idx, action in enumerate(notif_actions, 1):
                logger.info(f"  📝 Action {idx}: {action.get('action_type')} for collection {action.get('collection_name')}")
            
            notif_results = self.knowledge_base_executor.execute_updates(notif_actions, notification)
            
            # Enhanced result logging with reasoning and confidence
            logger.info(f"✅ Flowchart-based KB Results summary:")
            for idx, result in enumerate(notif_results, 1):
                action_type = result.get('action', 'UNKNOWN')
                success = result.get('success', False)
                status = "SUCCESS" if success else "FAILED"
                
                # Get corresponding action for reasoning and confidence
                corresponding_action = notif_actions[idx-1] if idx <= len(notif_actions) else {}
                reasoning = corresponding_action.get('reasoning', 'N/A')
                confidence = corresponding_action.get('confidence', 'N/A')
                collection = corresponding_action.get('collection_name', 'N/A')
                
                # Extract UUID from result if available
                uuid_value = None
                if success and action_type == "ADD_DOCUMENT":
                    uuid_value = result.get('uuid')
                    if not uuid_value and result.get('details'):
                        import re
                        uuid_match = re.search(r'ID:\s*([a-f0-9-]{36})', result.get('details', ''))
                        if uuid_match:
                            uuid_value = uuid_match.group(1)
                    
                    if uuid_value:
                        notification_uuids.append(uuid_value)
                        logger.info(f"  📋 Result {idx}: {action_type} - {status}")
                        logger.info(f"      📦 Collection: {collection}")
                        logger.info(f"      🆔 UUID: {uuid_value}")
                        logger.info(f"      🤔 Reasoning: {reasoning}")
                        logger.info(f"      📊 Confidence: {confidence}")
                    else:
                        logger.info(f"  📋 Result {idx}: {action_type} - {status}")
                        logger.info(f"      📦 Collection: {collection}")
                        logger.info(f"      🤔 Reasoning: {reasoning}")
                        logger.info(f"      📊 Confidence: {confidence}")
                else:
                    logger.info(f"  📋 Result {idx}: {action_type} - {status}")
                    logger.info(f"      📦 Collection: {collection}")
                    logger.info(f"      🤔 Reasoning: {reasoning}")
                    logger.info(f"      📊 Confidence: {confidence}")
                    if not success:
                        error_details = result.get('error', result.get('details', 'No error details'))
                        logger.error(f"      ❌ Error: {error_details}")
            
            logger.info(f"📊 Added {len(notification_uuids)} documents with UUIDs: {notification_uuids}")
            
            # Add notification to metadata vectors
            if notification_uuids:
                try:
                    logger.info(f"📊 Adding notification to metadata_vectors collection for enhanced search")
                    metadata_uuids = self._add_notification_to_metadata_vectors(notification, content, notification_codes)
                    logger.info(f"   📊 Notification metadata vectors UUIDs: {len(metadata_uuids)}")
                except Exception as e:
                    logger.error(f"❌ Failed to add notification to metadata_vectors: {e}")
        
        return notification_uuids, notif_results
    
    def _detect_document_type_enhanced(self, title: str, kb_decision: dict) -> str:
        """
        Detect document type based on title and KB decision for flowchart processing
        Returns: 'master_circular', 'circular', 'master_direction', 'other'
        """
        title_lower = title.lower()
        target_collection = kb_decision.get('target_collection', '')
        
        logger.info(f"🔍 Detecting document type from title: {title[:100]}...")
        logger.info(f"📋 Target collection from KB decision: {target_collection}")
        
        # Master Circular detection
        if 'master circular' in title_lower:
            logger.info("✅ Detected: Master Circular")
            return 'master_circular'
        
        # Master Direction detection
        if 'master direction' in title_lower:
            logger.info("✅ Detected: Master Direction")
            return 'master_direction'
        
        # Regular Circular detection
        if any(keyword in title_lower for keyword in ['circular', 'notification']):
            # If target collection is master_circular, prioritize that
            if 'master_circular' in target_collection:
                logger.info("✅ Detected: Master Circular (based on collection)")
                return 'master_circular'
            else:
                logger.info("✅ Detected: Circular")
                return 'circular'
        
        # Fallback to other
        logger.info("✅ Detected: Other")
        return 'other'
    
    def _generate_flowchart_actions_enhanced(self, document_type: str, title: str, content: str, 
                                           kb_decision: dict, notification: dict, notification_codes: dict) -> list:
        """
        Generate KB actions based on the flowchart logic for each document type
        """
        logger.info(f"🎯 Generating flowchart-based actions for document type: {document_type}")
        
        if document_type == 'master_circular':
            return self._process_master_circular_actions_enhanced(title, content, kb_decision, notification, notification_codes)
        elif document_type == 'circular':
            return self._process_circular_actions_enhanced(title, content, kb_decision, notification, notification_codes)
        elif document_type == 'master_direction':
            return self._process_master_direction_actions_enhanced(title, content, kb_decision, notification, notification_codes)
        else:
            return self._process_other_document_actions_enhanced(title, content, kb_decision, notification, notification_codes)
    
    def _process_master_circular_actions_enhanced(self, title: str, content: str, kb_decision: dict, 
                                                notification: dict, notification_codes: dict) -> list:
        """Process Master Circular with consolidation detection"""
        actions = []
        logger.info("📘 Processing Master Circular actions...")
        
        # Always add the Master Circular to knowledge base
        add_action = self._create_add_document_action_enhanced(
            title, content, notification, notification_codes, 'rbi_master_circular', 
            "New Master Circular publication - follows flowchart logic"
        )
        actions.append(add_action)
        logger.info("✅ Added: ADD_DOCUMENT action for Master Circular")
        
        # TODO: Add consolidation detection logic here
        # This would analyze content for supersession patterns
        
        return actions
    
    def _process_circular_actions_enhanced(self, title: str, content: str, kb_decision: dict, 
                                         notification: dict, notification_codes: dict) -> list:
        """Process regular Circular with linkage detection"""
        actions = []
        logger.info("📄 Processing Circular actions...")
        
        # Always add the circular to knowledge base
        add_action = self._create_add_document_action_enhanced(
            title, content, notification, notification_codes, 'rbi_circular', 
            "New circular publication - follows flowchart logic"
        )
        actions.append(add_action)
        logger.info("✅ Added: ADD_DOCUMENT action for Circular")
        
        # TODO: Add amendment/modification detection logic here
        
        return actions
    
    def _process_master_direction_actions_enhanced(self, title: str, content: str, kb_decision: dict, 
                                                 notification: dict, notification_codes: dict) -> list:
        """Process Master Direction with amendment detection"""
        actions = []
        logger.info("📜 Processing Master Direction actions...")
        
        # Always add the Master Direction to knowledge base
        add_action = self._create_add_document_action_enhanced(
            title, content, notification, notification_codes, 'rbi_master_direction', 
            "New Master Direction publication - follows flowchart logic"
        )
        actions.append(add_action)
        logger.info("✅ Added: ADD_DOCUMENT action for Master Direction")
        
        # TODO: Add direction amendment detection logic here
        
        return actions
    
    def _process_other_document_actions_enhanced(self, title: str, content: str, kb_decision: dict, 
                                               notification: dict, notification_codes: dict) -> list:
        """Process other document types"""
        actions = []
        logger.info("📑 Processing Other document actions...")
        
        # Always add to knowledge base
        add_action = self._create_add_document_action_enhanced(
            title, content, notification, notification_codes, 'rbi_other', 
            "Other notification type - follows flowchart logic"
        )
        actions.append(add_action)
        logger.info("✅ Added: ADD_DOCUMENT action for Other document")
        
        return actions
    
    def _create_add_document_action_enhanced(self, title: str, content: str, notification: dict, 
                                           notification_codes: dict, collection: str, reasoning: str) -> dict:
        """Create standardized ADD_DOCUMENT action with enhanced metadata"""
        
        # Upload the PDF to S3 if available
        s3_url = None
        local_pdf_path = notification.get('Local Path') or notification.get('local_pdf_path')
        if local_pdf_path and Path(local_pdf_path).exists():
            try:
                from utils.s3_utils import upload_file_to_s3
                
                # Create S3 key based on notification data
                doc_id = notification_codes.get('full_code', notification.get('Title', ''))
                clean_id = doc_id.replace('/', '_').replace('\\', '_').replace(' ', '_')
                s3_key = f"notifications/{collection}/{clean_id}.pdf"
                
                # Upload to S3
                upload_result = upload_file_to_s3(
                    local_file_path=local_pdf_path,
                    s3_key=s3_key,
                    metadata={
                        "document_id": doc_id,
                        "collection": collection,
                        "document_type": "notification"
                    }
                )
                
                if upload_result["success"]:
                    s3_url = upload_result["s3_url"]
                    logger.info(f"✅ Uploaded notification PDF to S3: {s3_url}")
                else:
                    logger.warning(f"⚠️ Failed to upload notification PDF to S3: {upload_result.get('error')}")
            except Exception as e:
                logger.error(f"❌ Error uploading notification PDF to S3: {e}")
        
        payload = {
            'metadata': {
                'title': title,
                'document_id': notification_codes.get('full_code', title),
                'short_id': notification_codes.get('short_code', ''),
                'long_code': notification_codes.get('long_code', ''),
                'year': notification_codes.get('year', notification.get('Year', '')),
                'section': notification.get('Section', ''),
                'watermark': notification_codes.get('watermark', ''),
                'watermark_status': notification_codes.get('watermark_status', 'UNKNOWN'),
                'is_active': notification_codes.get('is_active', True),
                'pdf_link': notification.get('PDF Link', ''),
                'local_path': notification.get('Local Path', ''),
                'document_type': 'notification',
                'processing_timestamp': datetime.now().isoformat(),
                'notification_codes': notification_codes,
                'all_extracted_codes': notification_codes.get('all_codes', []),
                'collection': collection
            }, 
            'content': content, 
            'page_content': content,
            'notification_link': notification.get('Link', ''), 
            'pdf_url': notification.get('PDF Link', '')
        }
        
        # Add S3 URL if available
        if s3_url:
            payload['metadata']['s3_url'] = s3_url
            payload['s3_url'] = s3_url
            payload['metadata']['document_url'] = s3_url
        
        document_id = notification_codes.get('full_code', title)
        
        return {
            'action_type': 'ADD_DOCUMENT',
            'collection_name': collection,
            'target_document': document_id,
            'document_id': document_id,
            'payload': payload,
            'reasoning': reasoning,
            'confidence': 0.9
        }
    
    def _process_notification_pdf_enhanced(self, title: str, link: str, analysis_result: dict, content: str, local_pdf_path: str, notification_codes: dict) -> tuple:
        """Enhanced PDF processing with UUID tracking"""
        pdf_uuids = []
        operation_details = []
        
        if not analysis_result.get('requires_kb_update', False):
            return None, pdf_uuids, operation_details
        
        try:
            # Process the notification PDF, which now handles S3 URL generation and inclusion in Qdrant points
            pdf_processing = self.process_notification_pdf_for_kb(
                title, link, analysis_result, content, local_pdf_path=local_pdf_path
            )
            
            if pdf_processing and pdf_processing.get('status') == 'success':
                # Extract UUIDs from added chunks - check both possible field names
                pdf_uuids = pdf_processing.get('added_chunk_uuids', []) or pdf_processing.get('added_document_uuids', [])
                
                for uuid_val in pdf_uuids:
                    logger.info(f"🆔 PDF CHUNK UUID: {uuid_val}")
                
                # Track operation details
                operation_details.append({
                    'operation': 'add',
                    'uuid_count': len(pdf_uuids),
                    'success': True,
                    'operation_type': 'pdf_processing',
                    'timestamp': datetime.utcnow().isoformat()
                })
            else:
                operation_details.append({
                    'operation': 'add',
                    'uuid_count': 0,
                    'success': False,
                    'operation_type': 'pdf_processing',
                    'timestamp': datetime.utcnow().isoformat()
                })
            
            return pdf_processing, pdf_uuids, operation_details
            
        except Exception as e:
            logger.error(f"Error in enhanced PDF processing: {e}")
            operation_details.append({
                'operation': 'add',
                'uuid_count': 0,
                'success': False,
                'operation_type': 'pdf_processing',
                'timestamp': datetime.utcnow().isoformat()
            })
            return None, pdf_uuids, operation_details
    
    def get_notification_chunks(self, title: str, content: str) -> list:
        """Get chunks from notification content using HTML AST chunking"""
        try:
            from utils.pdf_utils import parse_pdf_to_html_ast, chunk_html_ast
            from bs4 import BeautifulSoup
            
            chunks = []
            
            # If content is already HTML (has HTML tags), use it directly
            if '<' in content and '>' in content:
                chunks = chunk_html_ast(content, max_chars=8000)
            else:
                # Try to parse as plain text if not HTML
                soup = BeautifulSoup(f"<div>{content}</div>", 'html.parser')
                chunks = chunk_html_ast(str(soup), max_chars=8000)
            
            logger.info(f"✅ Generated {len(chunks)} chunks from notification content")
            return chunks
        except Exception as e:
            logger.warning(f"⚠️ Could not generate chunks from content: {e}")
            return []

    def _extract_affected_documents_enhanced(self, title: str, content: str, category: str, notification_codes: dict) -> dict:
        """Enhanced affected documents extraction with supersession detection"""
        try:
            # Pass notification_codes for supersession detection
            # Get chunks from both the notification content and any PDF content
            notification_chunks = self.get_notification_chunks(title, content)
            
            # If we have PDF processing results, add those chunks too
            pdf_chunks = []
            if hasattr(self, 'pdf_processing_results') and self.pdf_processing_results:
                pdf_chunks = self.pdf_processing_results.get('chunks', [])
            
            # Combine all chunks
            all_chunks = notification_chunks + pdf_chunks
            logger.info(f"🔄 Total chunks for processing: {len(all_chunks)} (Notification: {len(notification_chunks)}, PDF: {len(pdf_chunks)})")
            
            affected_documents = self.notification_processor.extract_affected_documents(
                title, content, category, notification_codes,
                chunks=all_chunks
            )
            
            # Enhance with notification codes
            if affected_documents:
                affected_documents['source_notification_codes'] = notification_codes
                affected_documents['source_notification_metadata'] = {
                    'title': title,
                    'category': category,
                    'codes': notification_codes
                }
            
            return affected_documents
        except Exception as e:
            logger.error(f"Error extracting enhanced affected documents: {e}")
            return {}
    


    def _process_chunks_streamlined(self, chunks: list, notification_data: dict, notification_codes: dict) -> tuple:
        """
        Streamlined processing of PDF chunks with embeddings and action execution tracking.
        
        Returns:
            tuple: (processed_chunks, chunk_uuids, executed_actions)
        """
        try:
            logger.info(f"🔧 Starting streamlined processing of {len(chunks)} chunks")
            
            processed_chunks = []
            skipped_chunks = []
            chunk_uuids = []
            executed_actions = []
            
            for i, chunk in enumerate(chunks):
                logger.info(f"🔍 [DEBUG] Processing chunk {i+1}/{len(chunks)}")
                logger.info(f"🔍 [DEBUG] Chunk content length: {len(chunk.get('content', ''))} chars")
                
                # Enhance chunk with notification metadata
                enhanced_chunk = {
                    **chunk,
                    'notification_metadata': notification_data,
                    'notification_codes': notification_codes,
                    'chunk_index': i,
                    'total_chunks': len(chunks),
                    'processing_timestamp': datetime.now().isoformat()
                }
                
                # Classify chunk for KB indexing
                classification_result = self.llm_classify_chunk_for_indexing(
                    chunk.get('content', ''), 
                    enhanced_chunk,
                    {'notification_title': notification_data.get('Title', ''), 'notification_date': notification_data.get('Date', '')}
                )
                
                # Only process chunks that should be indexed
                if classification_result.should_index:
                    logger.info(f"✅ Chunk {i+1} classified as {classification_result.classification.value} - INDEXING")
                    
                    # # Generate chunk-specific actions based on content
                    chunk_actions = self.llm_get_list_actions_from_chunk(
                        enhanced_chunk.get('content', ''), 
                        {
                            'notification_title': notification_data.get('Title', ''), 
                            'notification_date': notification_data.get('Date', '')
                        }
                    )
                    
                    # # Execute actions for this chunk
                    if chunk_actions:
                        logger.info(f"🎯 Executing {len(chunk_actions)} actions for chunk {i+1}")
                        action_results = self._execute_chunk_actions(chunk_actions, enhanced_chunk)
                        executed_actions.extend(action_results)
                        # Log which chunk produced an action
                        enhanced_chunk['produced_actions'] = True
                    
                    # Create KB action for chunk indexing
                    kb_action = self._create_chunk_kb_action(enhanced_chunk, classification_result, notification_codes)
                    
                    if kb_action:
                        logger.info(f"🔍 [DEBUG] Creating KB action for chunk {i+1}:")
                        logger.info(f"🔍 [DEBUG] - Document ID: {kb_action.get('document_id')}")
                        logger.info(f"🔍 [DEBUG] - Short ID: {kb_action.get('short_id')}")
                        logger.info(f"🔍 [DEBUG] - Target collection: {kb_action.get('collection_name')}")
                        
                        # Execute KB action and get UUID
                        try:
                            kb_results = self.knowledge_base_executor.execute_updates([kb_action], notification_data, enhanced_chunk)
                            if kb_results and kb_results[0].get('success'):
                                chunk_uuid = kb_results[0].get('uuid')
                                if chunk_uuid:
                                    chunk_uuids.append(chunk_uuid)
                                    logger.info(f"🆔 CHUNK UUID: {chunk_uuid}")
                                logger.info(f"🔍 [DEBUG] KB action successfully created for chunk {i+1}")
                            else:
                                logger.warning(f"⚠️ KB action failed for chunk {i+1}")
                        except Exception as e:
                            logger.error(f"❌ Error executing KB action for chunk {i+1}: {e}")
                    
                    processed_chunks.append(enhanced_chunk)
                    logger.info(f"   ✅ Chunk {i+1} added to processing queue")
                    
                else:
                    logger.info(f"⏭️ Chunk {i+1} classified as {classification_result.classification.value} - SKIPPING")
                    skipped_chunks.append(enhanced_chunk)

            
            logger.info(f"� Chunk Processing Summary:")
            logger.info(f"   - Total chunks: {len(chunks)}")
            logger.info(f"   - Indexed chunks: {len(processed_chunks)}")
            logger.info(f"   - Skipped chunks: {len(skipped_chunks)}")
            logger.info(f"   - Chunks producing actions: {sum(1 for c in processed_chunks if c.get('produced_actions'))}")

            # For auditing, we can store skipped chunks if needed
            # self.save_skipped_chunks_for_audit(skipped_chunks)
            
            return processed_chunks, chunk_uuids, executed_actions
            
        except Exception as e:
            logger.error(f"❌ Error in streamlined chunk processing: {e}")
            return [], [], []




    def _execute_chunk_actions(self, actions: list, chunk: dict) -> list:
        """
        Execute actions extracted from chunk content.
        
        Args:
            actions: List of actions (can be DocumentAction Pydantic models or dicts)
            chunk: The chunk data that generated these actions
        
        Returns:
            list: Results of action execution
        """
        try:
            logger.info(f"🎯 Executing {len(actions)} chunk-specific actions")
            results = []
            
            # Log the type of actions we're working with
            if actions and hasattr(actions[0], 'model_dump'):
                logger.info(f"📝 Processing Pydantic DocumentAction objects")
                # Log a sample action for debugging
                try:
                    logger.info(f"📝 Sample action: {actions[0].model_dump()}")
                except:
                    logger.info(f"📝 Sample action: {actions[0]}")
            else:
                logger.info(f"📝 Processing dictionary action objects")
            
            for i, action in enumerate(actions):
                try:
                    logger.info(f"▶️ Processing action {i+1}/{len(actions)}")
                    
                    # Handle both Pydantic DocumentAction objects and dicts for ID generation
                    if hasattr(action, 'action_type'):
                        # For Pydantic models, add action_id as attribute if not already present
                        if not hasattr(action, 'action_id') or not action.action_id:
                            # We can't directly modify the Pydantic model, so we'll add this to a separate dict
                            # that we'll pass along to _execute_single_action
                            action_id = self._generate_action_id_from_chunk(action, chunk)
                            logger.info(f"📌 Generated action ID for Pydantic model: {action_id}")
                    else:
                        # For dict objects, add action_id as a key
                        action['action_id'] = self._generate_action_id_from_chunk(action, chunk)
                    
                    # Validate action before execution
                    if self._validate_action_for_execution(action):
                        # Execute the action
                        result = self._execute_single_action(action, chunk)
                        results.append(result)
                        
                        # Get action type and target for logging
                        action_type = action.action_type if hasattr(action, 'action_type') else action.get('action_type')
                        target = action.document_id if hasattr(action, 'document_id') else action.get('target_document')
                        
                        if result.get('success'):
                            logger.info(f"✅ Action executed successfully: {action_type} - {target}")
                        else:
                            logger.warning(f"⚠️ Action execution failed: {result.get('error')}")
                    else:
                        logger.warning(f"⚠️ Action validation failed, skipping: {action}")
                        results.append({
                            'action': action,
                            'success': False,
                            'error': 'Action validation failed'
                        })
                        
                except Exception as e:
                    logger.error(f"❌ Error executing action: {e}")
                    logger.error(f"❌ Error traceback: {traceback.format_exc()}")
                    results.append({
                        'action': action,
                        'success': False,
                        'error': str(e)
                    })
            
            success_count = sum(1 for r in results if r.get('success'))
            logger.info(f"📊 Chunk action execution: {success_count}/{len(results)} successful")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Error in chunk action execution: {e}")
            return []

    def _generate_action_id_from_chunk(self, action, chunk: dict) -> str:
        """
        Generate action ID based on chunk content and metadata.
        
        Works with both Pydantic DocumentAction objects and regular dicts.
        """
        try:
            import hashlib
            import uuid
            
            # Components for ID generation - handle both dict and Pydantic object
            if hasattr(action, 'action_type'):
                # It's a Pydantic model
                action_type = action.action_type
                target_doc = getattr(action, 'document_id', 'unknown')
            else:
                # Regular dict
                action_type = action.get('action_type', 'ACTION')
                target_doc = action.get('target_document', 'unknown')
            chunk_index = chunk.get('chunk_index', 0)
            
            # Create content hash from chunk
            content = chunk.get('content', '')
            content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()[:8]
            
            # Clean target document for ID
            clean_target = re.sub(r'[^A-Za-z0-9_-]', '_', str(target_doc))[:15]
            
            action_id = f"{action_type}_{clean_target}_chunk{chunk_index}_{content_hash}"
            return action_id
            
        except Exception as e:
            logger.error(f"❌ Error generating action ID from chunk: {e}")
            return f"action_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:17]}"

    def _validate_action_for_execution(self, action: dict) -> bool:
        """Validate that action is ready for execution"""
        try:
            # Handle both Pydantic DocumentAction objects and regular dicts
            if hasattr(action, 'action_type') and isinstance(action.action_type, str):
                # It's a Pydantic model
                action_type = action.action_type
                # For Pydantic model, use document_id as target
                target_document = action.document_id
                
                logger.info(f"🔍 Validating Pydantic action: {action_type} for {target_document}")
            else:
                # Regular dict
                action_type = action.get('action_type')
                target_document = action.get('target_document')
                
                logger.info(f"🔍 Validating dict action: {action_type} for {target_document}")
            
            # Basic validation
            if not action_type:
                logger.warning(f"⚠️ Missing action_type in action")
                return False
                
            if action_type in ['REMOVE_DOCUMENT', 'UPDATE_DOCUMENT'] and not target_document:
                logger.warning(f"⚠️ Missing target_document for {action_type} action")
                return False
                
            if target_document == 'MANUAL_REVIEW_REQUIRED':
                logger.warning(f"⚠️ Action requires manual review: {action}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"❌ Error validating action: {e}")
            return False

    def _execute_single_action(self, action: dict, chunk: dict) -> dict:
        """Execute a single action"""
        try:
            # Handle both Pydantic DocumentAction objects and regular dicts
            if hasattr(action, 'action_type') and isinstance(action.action_type, str):
                # It's a Pydantic model
                action_type = action.action_type
                target_document = action.document_id  # For Pydantic model, use document_id as target
                logger.info(f"🎯 Executing {action_type} action for Pydantic model with document_id: {target_document}")
                
                # Convert Pydantic model to KB action format
                kb_action = {
                    'action': action_type,
                    'action_type': action_type,
                    'action_id': getattr(action, 'action_id', None) or str(uuid.uuid4()),
                    'target_document': target_document,
                    'details': getattr(action, 'reasoning', None),
                    'priority': getattr(action, 'priority', 'MEDIUM'),
                    'source_chunk': chunk.get('chunk_index'),
                    'timestamp': datetime.now().isoformat()
                }
            else:
                # Regular dict
                action_type = action.get('action_type')
                logger.info(f"🎯 Executing {action_type} action for {action.get('target_document')}")
                
                # Convert action to KB action format
                kb_action = {
                    'action': action_type,
                    'action_type': action_type,
                    'action_id': action.get('action_id'),
                    'target_document': action.get('target_document'),
                    'details': action.get('details'),
                    'priority': action.get('priority', 'MEDIUM'),
                    'source_chunk': chunk.get('chunk_index'),
                    'timestamp': datetime.now().isoformat()
                }
            
            # Execute through knowledge base executor with tracking
            action_id = track_kb_action(kb_action)
            logger.info(f"🎯 Started tracking KB action: {action_id}")

            try:
                result = self.knowledge_base_executor.execute_updates([kb_action], chunk.get('notification_metadata', {}), chunk)

                # Complete tracking
                if result and len(result) > 0:
                    success = result[0].get('success', False)
                    docs_affected = result[0].get('documents_affected', 0)
                    error_msg = result[0].get('error') if not success else None
                    complete_kb_action(action_id, success, docs_affected, error_msg)
                else:
                    complete_kb_action(action_id, False, 0, "No result from executor")
            except Exception as e:
                complete_kb_action(action_id, False, 0, str(e))
                raise
            
            if result and len(result) > 0:
                return result[0]
            else:
                return {'success': False, 'error': 'No result from executor'}
                
        except Exception as e:
            logger.error(f"❌ Error executing single action: {e}")
            return {'success': False, 'error': str(e)}

    def _create_chunk_kb_action(self, chunk: dict, classification: object, notification_codes: dict) -> dict:
        """Create knowledge base action for chunk indexing with enhanced affected documents metadata"""
        try:
            # Extract document ID for the chunk
            document_id = (notification_codes.get('short_code') or 
                          notification_codes.get('long_code') or 
                          notification_codes.get('full_code') or
                          f"CHUNK_{chunk.get('chunk_index', 0)}")
            
            # Determine collection based on classification
            collection_map = {
                'regulatory': 'rbi_circular',
                'master_direction': 'rbi_master_direction', 
                'master_circular': 'rbi_master_circular',
                'informational': 'rbi_other',
                'administrative': 'rbi_other'
            }
            
            classification_name = classification.classification.value if hasattr(classification, 'classification') else 'informational'
            collection_name = collection_map.get(classification_name, 'rbi_circular')
            
            # Enhanced metadata with affected documents
            enhanced_metadata = {
                'chunk_index': chunk.get('chunk_index'),
                'total_chunks': chunk.get('total_chunks'),
                'classification': classification_name,
                'confidence': classification.confidence.value if hasattr(classification, 'confidence') else 'medium',
                'notification_codes': notification_codes,
                'processing_timestamp': datetime.now().isoformat()
            }
            
            # Add affected documents metadata if available
            if chunk.get('affected_documents'):
                affected_docs = chunk['affected_documents']
                enhanced_metadata['affected_documents'] = affected_docs
                enhanced_metadata['affected_document_count'] = chunk.get('affected_document_count', len(affected_docs))
                
                # Extract summary information for quick access
                doc_codes = [doc.get('document_code', '') for doc in affected_docs if doc.get('document_code')]
                doc_titles = [doc.get('document_title', '') for doc in affected_docs if doc.get('document_title')]
                relationships = [doc.get('relationship', '') for doc in affected_docs if doc.get('relationship')]
                
                enhanced_metadata['affected_document_codes'] = doc_codes
                enhanced_metadata['affected_document_titles'] = doc_titles  
                enhanced_metadata['document_relationships'] = list(set(relationships))
                
                logger.info(f"📄 Chunk {chunk.get('chunk_index', 0)} enhanced with {len(affected_docs)} affected documents")
            
            kb_action = {
                'action': 'ADD_DOCUMENT',
                'action_type': 'ADD_DOCUMENT',
                'document_id': document_id,
                'short_id': document_id,
                'collection_name': collection_name,
                'content': chunk.get('content', ''),
                'metadata': enhanced_metadata,
                'dense_vector_required': True,
                'sparse_vector_required': True,
                'target_collection': collection_name
            }
            
            return kb_action
            
        except Exception as e:
            logger.error(f"❌ Error creating chunk KB action: {e}")
            return None

    def _determine_update_actions_enhanced(self, title: str, category: str, affected_documents: dict, content: str, notification: dict = None) -> list:
        """Enhanced update actions determination"""
        try:
            update_result = self.notification_processor.determine_update_actions(
                title, category, affected_documents.get('document_actions', []), content, notification
            )
            logger.info(f"Determined update actions for {title}: {update_result}")
            return update_result.get('actions', [])
        except Exception as e:
            logger.error(f"Error determining enhanced update actions: {e}")
            return []
    
    def _execute_affected_document_actions_enhanced(self, affected_documents: dict, notification: dict, notification_codes: dict) -> tuple:
        """Execute ADD_DOCUMENT and REMOVE_DOCUMENT actions from affected documents analysis"""
        results = {
            'operation_details': [],
            'add_results': [],
            'remove_results': []
        }
        
        uuids = {
            'added': [],
            'removed': []
        }
        
        if not affected_documents or not affected_documents.get('document_actions'):
            logger.info("No affected document actions to execute")
            return results, uuids
        
        document_actions = affected_documents.get('document_actions', [])
        logger.info(f"📋 Executing {len(document_actions)} affected document actions")
        
        # Separate actions by type and execute in order: REMOVE first, then ADD
        remove_actions = [action for action in document_actions if action.get('action_type') == 'REMOVE_DOCUMENT']
        add_actions = [action for action in document_actions if action.get('action_type') == 'ADD_DOCUMENT']
        
        # Execute REMOVE actions first
        for action in remove_actions:
            try:
                logger.info(f"🗑️ Executing REMOVE_DOCUMENT for: {action.get('document_id')}")

                # Build flexible filter_fields for all possible codes
                filter_fields = {}
                doc_id = action.get('document_id') or notification_codes.get('full_code')
                long_code = notification_codes.get('long_code')
                short_code = notification_codes.get('short_code')
                full_title = notification.get('Title') or notification.get('title')

                if doc_id:
                    filter_fields['metadata.document_id'] = doc_id
                if long_code:
                    filter_fields['metadata.long_code'] = long_code
                if short_code:
                    filter_fields['metadata.short_code'] = short_code
                if full_title:
                    filter_fields['metadata.title'] = full_title

                # Also add the original document_id for legacy compatibility
                if doc_id:
                    filter_fields['document_id'] = doc_id
                if action.get('reference_number'):
                    filter_fields['reference_number'] = action.get('reference_number')

                # Create KB action for removal with vector search fallback
                kb_action = {
                    'action': 'REMOVE_DOCUMENT',
                    'action_type': 'REMOVE_DOCUMENT',
                    'collection': 'rbi_circular',
                    'filter_fields': filter_fields,
                    'reasoning': action.get('reasoning', 'Document superseded'),
                    'source_notification_codes': notification_codes,
                    'use_vector_search': True  # Enable vector search fallback
                }
                
                # Set document_id directly from filter fields if available
                if filter_fields.get('document_id'):
                    kb_action['document_id'] = filter_fields['document_id']

                # Execute the removal
                remove_result = self.knowledge_base_executor.execute_updates([kb_action], notification)
                results['remove_results'].extend(remove_result)

                # Track operation details
                if remove_result and len(remove_result) > 0:
                    success = remove_result[0].get('success', False)
                    removed_count = remove_result[0].get('removed_count', 0) if success else 0

                    results['operation_details'].append({
                        'operation': 'remove',
                        'uuid_count': removed_count,
                        'success': success,
                        'operation_type': 'affected_document_removal',
                        'document_id': action.get('document_id'),
                        'timestamp': datetime.utcnow().isoformat()
                    })

                    if success and removed_count > 0:
                        logger.info(f"✅ Successfully removed {removed_count} chunks for document: {action.get('document_id')}")
                    else:
                        logger.warning(f"⚠️ No chunks found to remove for codes: doc_id={doc_id}, long_code={long_code}, short_code={short_code}, title={full_title}")

            except Exception as e:
                logger.error(f"❌ Error executing REMOVE_DOCUMENT for {action.get('document_id')}: {e}")
                results['operation_details'].append({
                    'operation': 'remove',
                    'uuid_count': 0,
                    'success': False,
                    'operation_type': 'affected_document_removal',
                    'document_id': action.get('document_id'),
                    'error': str(e),
                    'timestamp': datetime.utcnow().isoformat()
                })
        
        # Execute ADD actions
        for action in add_actions:
            try:
                logger.info(f"📝 Executing ADD_DOCUMENT for: {action.get('document_id')}")
                
                # For ADD_DOCUMENT, we need to either:
                # 1. Process the document if we have a URL
                # 2. Create a placeholder/metadata entry if we don't
                
                document_url = action.get('document_url')
                if document_url:
                    # Process the document from URL
                    logger.info(f"🔗 Processing document from URL: {document_url}")
                    
                    # Prepare document info for processing
                    document_info = {
                        'document_url': document_url,
                        'document_title': action.get('document_title'),
                        'document_id': action.get('document_id'),
                        'reference_number': action.get('reference_number'),
                        'action_type': 'ADD_DOCUMENT'
                    }
                    
                    doc_result = self.process_found_document_for_chunking(
                        document_info=document_info,
                        notification_data=notification,
                        chunk_metadata={
                            'notification_codes': notification_codes,
                            'document_id': action.get('document_id'),
                            'reference_number': action.get('reference_number'),
                            'action_reasoning': action.get('reasoning')
                        }
                    )
                    
                    if doc_result and doc_result.get('status') == 'success':
                        added_uuids = doc_result.get('added_document_uuids', [])
                        uuids['added'].extend(added_uuids)
                        
                        results['operation_details'].append({
                            'operation': 'add',
                            'uuid_count': len(added_uuids),
                            'success': True,
                            'operation_type': 'affected_document_addition',
                            'document_id': action.get('document_id'),
                            'document_url': document_url,
                            'timestamp': datetime.utcnow().isoformat()
                        })
                        
                        logger.info(f"✅ Successfully added document {action.get('document_id')} with {len(added_uuids)} chunks")
                        for uuid_val in added_uuids:
                            logger.info(f"🆔 AFFECTED DOC UUID: {uuid_val}")
                    else:
                        logger.warning(f"⚠️ Failed to process document URL for {action.get('document_id')}")
                        results['operation_details'].append({
                            'operation': 'add',
                            'uuid_count': 0,
                            'success': False,
                            'operation_type': 'affected_document_addition',
                            'document_id': action.get('document_id'),
                            'document_url': document_url,
                            'timestamp': datetime.utcnow().isoformat()
                        })
                else:
                    # No URL available - log that we identified the document but can't process it
                    logger.info(f"📄 Identified ADD_DOCUMENT for {action.get('document_id')} but no URL available")
                    results['operation_details'].append({
                        'operation': 'add',
                        'uuid_count': 0,
                        'success': False,
                        'operation_type': 'affected_document_addition',
                        'document_id': action.get('document_id'),
                        'reason': 'No document URL provided',
                        'timestamp': datetime.utcnow().isoformat()
                    })
                
            except Exception as e:
                logger.error(f"❌ Error executing ADD_DOCUMENT for {action.get('document_id')}: {e}")
                results['operation_details'].append({
                    'operation': 'add',
                    'uuid_count': 0,
                    'success': False,
                    'operation_type': 'affected_document_addition',
                    'document_id': action.get('document_id'),
                    'error': str(e),
                    'timestamp': datetime.utcnow().isoformat()
                })
        
        # Log summary
        total_actions = len(document_actions)
        successful_ops = len([op for op in results['operation_details'] if op.get('success')])
        logger.info(f"📊 Affected Document Actions Summary:")
        logger.info(f"   - Total actions: {total_actions}")
        logger.info(f"   - Successful operations: {successful_ops}/{total_actions}")
        logger.info(f"   - Documents added: {len(uuids['added'])} chunks")
        logger.info(f"   - Documents removed: {len(uuids['removed'])} chunks")
        
        return results, uuids
    
    def _process_found_documents_enhanced(self, update_actions: list, notification: dict, notification_codes: dict) -> tuple:
        """Enhanced found document processing with UUID tracking"""
        found_results = []
        found_uuids = []
        
        # Filter out REMOVE_DOCUMENT actions - they should be handled separately
        add_actions = [action for action in update_actions if action.get('action_type') != 'REMOVE_DOCUMENT']
        remove_actions = [action for action in update_actions if action.get('action_type') == 'REMOVE_DOCUMENT']
        
        if remove_actions:
            logger.info(f"🗑️ Skipping {len(remove_actions)} REMOVE_DOCUMENT actions in found documents processing (handled separately)")
        
        # Process each ADD action only
        for action in add_actions:
            try:
                logger.info(f"📄 Processing found document: {action.get('document_title', '')}")
                
                # Enhance action with notification codes for better metadata
                action['source_notification_codes'] = notification_codes
                
                # Ensure links are included
                if 'notification_link' not in action and notification.get('Link'):
                    action['notification_link'] = notification.get('Link')
                if 'pdf_link' not in action and notification.get('PDF Link'):
                    action['pdf_link'] = notification.get('PDF Link')
                
                # Process the document with enhanced metadata
                res = self.process_found_document_for_chunking(
                    action, notification, 
                    chunk_metadata={
                        'notification_codes': notification_codes,
                        'notification_link': notification.get('Link'),
                        'pdf_link': notification.get('PDF Link')
                    }
                )
                found_results.append(res)
                
                # Extract UUIDs from result
                if isinstance(res, dict) and res.get('added_document_uuids'):
                    found_uuids.extend(res.get('added_document_uuids'))
                    for uuid_val in res['added_document_uuids']:
                        logger.info(f"🆔 FOUND DOC UUID: {uuid_val}")
                
            except Exception as e:
                logger.error(f"❌ Error processing found document: {e}")
                logger.error(traceback.format_exc())
                found_results.append({
                    'status': 'error',
                    'error': str(e),
                    'document_url': action.get('document_url', 'unknown')
                })
        
        return found_results, found_uuids
    
    def _process_new_document_urls_enhanced(self, affected_documents: dict, update_actions: list, found_results: list, notification: dict, notification_codes: dict) -> tuple:
        """Enhanced new document URL processing with UUID tracking"""
        new_results = []
        new_uuids = []
        
        # Collect URLs from various sources
        new_document_urls = set()
        already_processed = set()
        
        # Extract URLs from affected documents
        if affected_documents:
            if isinstance(affected_documents.get('new_document_url'), str) and affected_documents['new_document_url']:
                new_document_urls.add(affected_documents['new_document_url'])
            
            # Extract from PDF links
            for url in affected_documents.get('pdf_links', []):
                if url:
                    new_document_urls.add(url)
        
        # Extract from update actions
        for action in update_actions:
            if action.get('new_document_url'):
                new_document_urls.add(action.get('new_document_url'))
        
        # Build set of already processed URLs
        for res in found_results:
            if isinstance(res, dict) and res.get('document_url'):
                already_processed.add(res['document_url'])
        
        # Process new URLs
        logger.info(f"🔗 Processing {len(new_document_urls)} new document URLs (excluding {len(already_processed)} already processed)")
        
        for url in new_document_urls:
            if url and url not in already_processed:
                try:
                    logger.info(f"📄 Processing new document URL: {url}")
                    
                    # Create document info with enhanced metadata
                    doc_info = {
                        'document_url': url,
                        'document_title': f"Discovered Document: {url.split('/')[-1]}",
                        'document_type': '',
                        'collection_name': 'rbi_other',
                        'notification_link': notification.get('Link', ''),
                        'source_notification_title': notification.get('Title', ''),
                        'source_notification_codes': notification_codes,
                        'source_timestamp': datetime.now().isoformat()
                    }
                    
                    # Process the document
                    result = self.process_found_document_for_chunking(
                        doc_info, 
                        notification, 
                        chunk_metadata={
                            'notification_codes': notification_codes,
                            'notification_link': notification.get('Link'),
                            'pdf_link': notification.get('PDF Link')
                        }
                    )
                    new_results.append(result)
                    
                    # Extract UUIDs
                    if isinstance(result, dict) and result.get('added_document_uuids'):
                        new_uuids.extend(result['added_document_uuids'])
                        for uuid_val in result['added_document_uuids']:
                            logger.info(f"🆔 NEW DOC UUID: {uuid_val} for URL: {url}")
                
                except Exception as e:
                    logger.error(f"❌ Error processing new document URL {url}: {e}")
                    logger.error(traceback.format_exc())
                    new_results.append({
                        'status': 'error',
                        'document_url': url,
                        'error': str(e)
                    })
        
        # Log summary
        logger.info(f"📊 Processed {len(new_document_urls)} new document URLs")
        logger.info(f"   ✅ Successfully processed: {len([r for r in new_results if r.get('status') != 'error'])}")
        logger.info(f"   ❌ Failed: {len([r for r in new_results if r.get('status') == 'error'])}")
        logger.info(f"   🆔 Added UUIDs: {len(new_uuids)}")
        
        return new_results, new_uuids
    
    def _link_notification_chunks_enhanced(self, pdf_processing: dict, affected_documents: dict, notification: dict, title: str, notification_codes: dict) -> tuple:
        """Enhanced linking of notification chunks to affected documents with better tracking"""
        linking_results = []
        linking_uuids = []
        
        try:
            # Extract PDF chunks from PDF processing result
            pdf_chunks = []
            if pdf_processing and pdf_processing.get('status') == 'success':
                pdf_chunks = pdf_processing.get('processed_chunks', [])
            
            if not pdf_chunks:
                logger.info(f"ℹ️ No PDF chunks available for linking to affected documents")
                return [], []
                
            logger.info(f"📎 Linking {len(pdf_chunks)} PDF chunks to affected documents...")
            
            # Process each affected document
            if affected_documents and affected_documents.get('document_actions'):
                for doc_action in affected_documents['document_actions']:
                    doc_id = doc_action.get('document_identifier') or doc_action.get('reference')
                    collection = doc_action.get('collection', 'rbi_other')
                    action_type = doc_action.get('action_type', 'LINK_DOCUMENT')
                    
                    if not doc_id:
                        continue
                        
                    logger.info(f"📎 Processing affected document: {doc_id} in collection {collection}")
                    logger.info(f"   Action type: {action_type}")
                    
                    # Find relevant chunks for this document
                    relevant_chunks = pdf_chunks  # Could implement more sophisticated filtering here
                    
                    if not relevant_chunks:
                        logger.info(f"   ⚠️ No relevant chunks found for document {doc_id}")
                        continue
                        
                    # Process each chunk for this document
                    for chunk_idx, chunk in enumerate(relevant_chunks):
                        # Create enhanced update action - use LINK_DOCUMENT action type for proper linking
                        update_action = {
                            'action_type': 'LINK_DOCUMENT',  # Use LINK_DOCUMENT action type for proper linking
                            'collection_name': collection,
                            'document_id': doc_id,  # Explicit document_id for better tracking
                            'filter_fields': {'metadata.document_id': doc_id},  # Proper filter field path
                            'payload': {
                                'linked_notification_title': title,
                                'linked_notification_date': notification.get('Date', ''),
                                'linked_notification_id': notification_codes.get('full_code', ''),
                                'linked_chunk_content': chunk.get('content', ''),
                                'linked_chunk_index': chunk.get('chunk_index'),
                                'updated_timestamp': datetime.now().isoformat(),
                                'pdf_url': notification.get('PDF Link', ''),
                                'notification_link': notification.get('Link', ''),
                                'metadata': {
                                    'last_updated': datetime.now().isoformat(),
                                    'updated_from_notification': title,
                                    'notification_codes': notification_codes,
                                    'pdf_link': notification.get('PDF Link', ''),
                                    'notification_link': notification.get('Link', '')
                                }
                            }
                        }
                        
                        # Execute update action
                        try:
                            logger.info(f"   📝 Linking chunk {chunk_idx+1}/{len(relevant_chunks)} to document {doc_id}")
                            result = self.knowledge_base_executor.execute_updates([update_action], notification)
                            linking_results.extend(result)
                            
                            # Extract UUID if available - handle both UPDATE_DOCUMENT and LINK_DOCUMENT actions
                            for r in result:
                                if r.get('success') and r.get('action') in ['UPDATE_DOCUMENT', 'LINK_DOCUMENT']:
                                    uuid_val = r.get('uuid')
                                    if not uuid_val and r.get('details'):
                                        uuid_match = re.search(r'ID:\s*([a-f0-9-]{36})', r.get('details'))
                                        if uuid_match:
                                            uuid_val = uuid_match.group(1)
                                    
                                    if uuid_val:
                                        linking_uuids.append(uuid_val)
                                        logger.info(f"   🆔 Linked with UUID: {uuid_val} using action {r.get('action')}")
                        except Exception as e:
                            logger.error(f"   ❌ Error linking chunk to document {doc_id}: {e}")
            else:
                logger.info(f"ℹ️ No affected documents found for linking")
                
            return linking_results, linking_uuids
            
        except Exception as e:
            logger.error(f"❌ Error in enhanced linking of notification chunks: {e}")
            return [], []
    
    def _generate_uuid_summary(self, notification_uuids: list, pdf_uuids: list, found_uuids: list, new_uuids: list, linking_uuids: list, all_added: list, all_updated: list, all_removed: list) -> dict:
        """Generate comprehensive UUID summary with operation statistics"""
        # Ensure all inputs are valid lists
        notification_uuids = notification_uuids or []
        pdf_uuids = pdf_uuids or []
        found_uuids = found_uuids or []
        new_uuids = new_uuids or []
        linking_uuids = linking_uuids or []
        all_added = all_added or []
        all_updated = all_updated or []
        all_removed = all_removed or []
        
        # Calculate statistics
        total_operations = len(all_added) + len(all_updated) + len(all_removed)
        
        # Create detailed summary
        return {
            'notification_uuids': notification_uuids,
            'pdf_chunk_uuids': pdf_uuids,
            'found_document_uuids': found_uuids,
            'new_document_uuids': new_uuids,
            'linking_operation_uuids': linking_uuids,
            'total_added_uuids': all_added,
            'total_updated_uuids': all_updated,
            'total_removed_uuids': all_removed,
            'summary_stats': {
                'total_added': len(all_added),
                'total_updated': len(all_updated),
                'total_removed': len(all_removed),
                'total_operations': len(all_added) + len(all_updated) + len(all_removed),
                'notification_count': len(notification_uuids),
                'pdf_chunk_count': len(pdf_uuids),
                'found_document_count': len(found_uuids),
                'new_document_count': len(new_uuids),
                'linking_count': len(linking_uuids),
                'success_rate': f"{(len(all_added) + len(all_updated))/(len(all_added) + len(all_updated) + len(all_removed)):.2%}" if (len(all_added) + len(all_updated) + len(all_removed)) > 0 else "N/A"
            }
        }
    
    def _track_operation(self, operation_details: list, operation: str, uuid_count: int, success: bool, operation_type: str = None) -> None:
        """Track individual operations for summary reporting"""
        operation_details.append({
            'operation': operation,
            'uuid_count': uuid_count,
            'success': success,
            'operation_type': operation_type,
            'timestamp': datetime.utcnow().isoformat()
        })

    def _calculate_and_update_summary_stats(self, uuid_summary: dict, operation_details: list) -> None:
        """Calculate and update comprehensive summary statistics"""
        try:
            # Count operations by type
            total_added = sum(1 for op in operation_details if op.get('operation') == 'add')
            total_updated = sum(1 for op in operation_details if op.get('operation') == 'update')
            total_removed = sum(1 for op in operation_details if op.get('operation') == 'remove')
            total_operations = len(operation_details)
            
            # Count different UUID types
            notification_count = uuid_summary.get('notification_uuids', 0)
            pdf_chunk_count = uuid_summary.get('pdf_chunk_uuids', 0)
            found_document_count = uuid_summary.get('found_document_uuids', 0)
            new_document_count = uuid_summary.get('new_document_uuids', 0)
            linking_count = uuid_summary.get('linking_uuids', 0)
            
            # Calculate success rate
            successful_ops = sum(1 for op in operation_details if op.get('success', False))
            success_rate = f"{(successful_ops / total_operations * 100):.1f}%" if total_operations > 0 else "0%"
            
            # Update summary stats
            uuid_summary['summary_stats'] = {
                'total_operations': total_operations,
                'total_added': total_added,
                'total_updated': total_updated,
                'total_removed': total_removed,
                'success_rate': success_rate,
                'notification_count': notification_count,
                'pdf_chunk_count': pdf_chunk_count,
                'found_document_count': found_document_count,
                'new_document_count': new_document_count,
                'linking_count': linking_count
            }
        except Exception as e:
            logger.error(f"❌ Error calculating summary stats: {str(e)}")
            uuid_summary['summary_stats'] = {
                'total_operations': len(operation_details),
                'total_added': 0,
                'total_updated': 0,
                'total_removed': 0,
                'success_rate': "Error",
                'notification_count': 0,
                'pdf_chunk_count': 0,
                'found_document_count': 0,
                'new_document_count': 0,
                'linking_count': 0
            }

    def _log_comprehensive_summary(self, title: str, uuid_summary: dict, operation_details: list) -> None:
        """Log comprehensive processing summary"""
        stats = uuid_summary['summary_stats']
        
        logger.info(f"")
        logger.info(f"🎯 ═══════════════════════════════════════════════════════════════")
        logger.info(f"🎯 COMPREHENSIVE PROCESSING SUMMARY")
        logger.info(f"🎯 Notification: {title[:60]}...")
        logger.info(f"🎯 ═══════════════════════════════════════════════════════════════")
        logger.info(f"")
        logger.info(f"📊 OPERATION STATISTICS:")
        logger.info(f"   📈 Total Operations: {stats['total_operations']}")
        logger.info(f"   ➕ Documents Added: {stats['total_added']}")
        logger.info(f"   📝 Documents Updated: {stats['total_updated']}")
        logger.info(f"   🗑️ Documents Removed: {stats['total_removed']}")
        logger.info(f"   📊 Success Rate: {stats['success_rate']}")
        logger.info(f"")
        logger.info(f"🆔 UUID BREAKDOWN:")
        logger.info(f"   📋 Notification UUIDs: {stats['notification_count']}")
        logger.info(f"   📄 PDF Chunk UUIDs: {stats['pdf_chunk_count']}")
        logger.info(f"   📁 Found Document UUIDs: {stats['found_document_count']}")
        logger.info(f"   🔗 New Document UUIDs: {stats['new_document_count']}")
        logger.info(f"   🔗 Linking UUIDs: {stats['linking_count']}")
        logger.info(f"")
        logger.info(f"🎯 ═══════════════════════════════════════════════════════════════")

    def _log_comprehensive_summary_with_actions(self, title: str, uuid_summary: dict, operation_details: list, pdf_processing: dict):
        """Log comprehensive summary including action execution tracking"""
        try:
            logger.info(f"🎯 ═══════════════════════════════════════════════════════════════")
            logger.info(f"🎯 COMPREHENSIVE PROCESSING SUMMARY WITH ACTION TRACKING")
            logger.info(f"🎯 Notification: {title[:60]}...")
            logger.info(f"🎯 ═══════════════════════════════════════════════════════════════")
            logger.info("")
            
            # Operations statistics
            total_operations = len(operation_details)
            successful_operations = sum(1 for op in operation_details if op.get('success', False))
            
            logger.info(f"📊 OPERATION STATISTICS:")
            logger.info(f"   📈 Total Operations: {total_operations}")
            logger.info(f"   ✅ Successful Operations: {successful_operations}")
            logger.info(f"   📊 Success Rate: {(successful_operations/total_operations*100):.1f}%" if total_operations > 0 else "   📊 Success Rate: N/A")
            logger.info("")
            
            # Action execution tracking
            if isinstance(pdf_processing, dict) and pdf_processing.get('executed_actions'):
                executed_actions = pdf_processing['executed_actions']
                successful_actions = sum(1 for action in executed_actions if action.get('success', False))
                
                logger.info(f"🎯 ACTION EXECUTION TRACKING:")
                logger.info(f"   🔄 Total Actions Identified: {len(executed_actions)}")
                logger.info(f"   ✅ Successfully Executed: {successful_actions}")
                logger.info(f"   ❌ Failed Actions: {len(executed_actions) - successful_actions}")
                logger.info(f"   📊 Action Success Rate: {(successful_actions/len(executed_actions)*100):.1f}%" if len(executed_actions) > 0 else "   📊 Action Success Rate: N/A")
                logger.info("")
                
                # Log individual action results
                for i, action_result in enumerate(executed_actions[:5]):  # Show first 5 actions
                    action = action_result.get('action', {})
                    status = "✅ SUCCESS" if action_result.get('success') else "❌ FAILED"
                    action_type = action.get('action_type', 'UNKNOWN')
                    target = action.get('target_document', 'N/A')
                    logger.info(f"   🎯 Action {i+1}: {action_type} - {target} - {status}")
                
                if len(executed_actions) > 5:
                    logger.info(f"   ... and {len(executed_actions) - 5} more actions")
                logger.info("")
            
            # UUID breakdown
            logger.info(f"🆔 UUID BREAKDOWN:")
            for key, value in uuid_summary.items():
                if isinstance(value, list):
                    logger.info(f"   📋 {key.replace('_', ' ').title()}: {value}")
            logger.info("")
            
            # Chunk processing details
            if isinstance(pdf_processing, dict):
                chunks_processed = pdf_processing.get('chunks_processed', 0)
                chunks_indexed = pdf_processing.get('chunks_indexed', 0)
                logger.info(f"📄 CHUNK PROCESSING:")
                logger.info(f"   📋 Chunks Processed: {chunks_processed}")
                logger.info(f"   📊 Chunks Indexed: {chunks_indexed}")
                logger.info(f"   📈 Indexing Rate: {(chunks_indexed/chunks_processed*100):.1f}%" if chunks_processed > 0 else "   📈 Indexing Rate: N/A")
                logger.info("")
            
            logger.info(f"🎯 ═══════════════════════════════════════════════════════════════")
            
        except Exception as e:
            logger.error(f"❌ Error logging comprehensive summary: {e}")

    def _generate_metadata_embedding(self, text: str) -> list:
        """
        Generate embeddings using smart model selection compatible with metadata_vectors collection
        """
        try:
            # Try to use sentence-transformers with smart selection
            try:
                from sentence_transformers import SentenceTransformer
                
                # Use smart embedding model selection
                if not hasattr(self, '_metadata_model'):
                    # Get the smart embedding model for metadata tasks
                    if hasattr(self, 'model_selector') and self.model_selector:
                        smart_config = self.model_selector.get_model_for_task('metadata_embedding')
                        embedding_model = smart_config['embedding']  # Fixed: use 'embedding' not 'embedding_model'
                        logger.info(f"🧠 Using smart-selected embedding model for metadata: {embedding_model}")
                    else:
                        # Fallback to default if no smart selector
                        embedding_model = 'all-MiniLM-L6-v2'
                        logger.info(f"🔄 Using fallback embedding model for metadata: {embedding_model}")
                    
                    self._metadata_model = SentenceTransformer(embedding_model)
                
                embedding = self._metadata_model.encode(text).tolist()
                return embedding
                
            except ImportError:
                logger.warning("sentence-transformers not available, using OpenAI with truncation")
                # Fallback: use OpenAI embeddings and truncate to appropriate dimensions
                from utils.openai_utils import en_embeddings
                full_embedding = en_embeddings.embed_query(text)
                
                # Determine expected dimensions based on model
                expected_dims = 384  # Default for metadata compatibility
                if hasattr(self, 'model_selector') and self.model_selector:
                    smart_config = self.model_selector.get_model_for_task('metadata_embedding')
                    if 'all-mpnet-base-v2' in smart_config.get('embedding', ''):  # Fixed: use 'embedding' not 'embedding_model'
                        expected_dims = 768
                    elif 'all-roberta-large-v1' in smart_config.get('embedding', ''):  # Fixed: use 'embedding' not 'embedding_model'
                        expected_dims = 1024
                
                # Truncate to expected dimensions
                truncated_embedding = full_embedding[:expected_dims] if len(full_embedding) > expected_dims else full_embedding
                
                # Pad with zeros if too short
                expected_dims = self._get_embedding_dimensions()
                while len(truncated_embedding) < expected_dims:
                    truncated_embedding.append(0.0)
                
                return truncated_embedding
                
        except Exception as e:
            logger.error(f"❌ Error generating metadata embedding: {e}")
            # Return zero vector as fallback
            return [0.0] * self._get_embedding_dimensions()

    def _get_embedding_dimensions(self) -> int:
        """Get the expected embedding dimensions based on the selected model"""
        try:
            if hasattr(self, 'model_selector') and self.model_selector:
                smart_config = self.model_selector.get_model_for_task('metadata_embedding')
                embedding_model = smart_config.get('embedding', 'all-MiniLM-L6-v2')  # Fixed: use 'embedding' not 'embedding_model'
                
                # Map models to their dimensions
                if 'all-MiniLM-L6-v2' in embedding_model:
                    return 384
                elif 'all-mpnet-base-v2' in embedding_model:
                    return 768
                elif 'all-roberta-large-v1' in embedding_model:
                    return 1024
                else:
                    return 384  # Default fallback
            else:
                return 384  # Default when no smart selector
        except Exception as e:
            logger.warning(f"Error getting embedding dimensions: {e}, using default 384")
            return 384

    def _add_notification_to_metadata_vectors(self, notification: dict, content: str, notification_codes: dict, base_uuid: str = None) -> list:
        """
        Add notification to metadata_vectors collection with specific metadata vector fields
        """
        metadata_vectors_uuids = []
        
        try:
            from utils.qdrant_utils import qdrant_manager
            
            # Extract key metadata for vector generation
            title = notification.get('Title', '')
            document_id = notification_codes.get('full_code', title)
            
            # Extract document number from document_id or notification codes
            document_number = ''
            if notification_codes.get('long_code'):
                document_number = notification_codes.get('long_code')
            elif notification_codes.get('short_code'):
                document_number = notification_codes.get('short_code')
            elif document_id:
                # Try to extract document number pattern
                import re
                number_match = re.search(r'(\d{4}[-/]\d{2}[-/]\d+)', document_id)
                if number_match:
                    document_number = number_match.group(1)
                else:
                    document_number = document_id
            
            # Generate short summary from content
            short_summary = content[:500] + "..." if len(content) > 500 else content
            
            # Generate vectors for each metadata field
            try:
                document_title_vec = self._generate_metadata_embedding(title)
                document_number_vec = self._generate_metadata_embedding(document_number) if document_number else [0.0] * self._get_embedding_dimensions()
                short_summary_vec = self._generate_metadata_embedding(short_summary)
                document_id_vec = self._generate_metadata_embedding(document_id) if document_id else [0.0] * self._get_embedding_dimensions()
                
                # Create payload for metadata_vectors collection
                metadata_payload = {
                    'document_id': document_id,
                    'title': title,
                    'document_number': document_number,
                    'short_summary': short_summary,
                    'content': content,
                    'page_content': content,
                    'document_type': 'notification',
                    'year': notification_codes.get('year', notification.get('Year', '')),
                    'section': notification.get('Section', ''),
                    'watermark_status': notification_codes.get('watermark_status', 'UNKNOWN'),
                    'notification_link': notification.get('Link', ''),
                    'pdf_link': notification.get('PDF Link', ''),
                    'processing_timestamp': datetime.now().isoformat(),
                    'notification_codes': notification_codes,
                    # Include metadata for searchability
                    'metadata': {
                        'title': title,
                        'document_id': document_id,
                        'content': content,
                        'page_content': content
                    }
                }
                
                # Create named vectors dictionary for metadata_vectors collection
                vectors = {
                    'document_title_vec': document_title_vec,
                    'document_number_vec': document_number_vec, 
                    'short_summary_vec': short_summary_vec,
                    'document_id_vec': document_id_vec
                }
                
                # Generate unique point ID for metadata_vectors collection (use UUID format)
                metadata_point_id = str(uuid.uuid4())
                
                # Use direct Qdrant client to add to metadata_vectors with named vectors
                from qdrant_client.http.models import PointStruct
                
                point = PointStruct(
                    id=metadata_point_id,
                    vector=vectors,
                    payload=metadata_payload
                )
                
                # Add to metadata_vectors collection
                try:
                    logger.info(f"🔍 Adding notification to metadata_vectors with ID: {metadata_point_id}")
                    logger.info(f"🔍 Vector keys: {list(vectors.keys())}")
                    logger.info(f"🔍 Vector dimensions: {[(k, len(v) if isinstance(v, list) else 'unknown') for k, v in vectors.items()]}")
                    
                    # Check if metadata_vectors collection exists, create if needed
                    try:
                        qdrant_manager.client.get_collection("metadata_vectors")
                    except Exception:
                        logger.info("🔧 Creating metadata_vectors collection as it doesn't exist")
                        from qdrant_client.http.models import VectorParams, Distance
                        
                        qdrant_manager.client.create_collection(
                            collection_name="metadata_vectors",
                            vectors_config={
                                "document_title_vec": VectorParams(size=384, distance=Distance.COSINE),
                                "document_number_vec": VectorParams(size=384, distance=Distance.COSINE),
                                "short_summary_vec": VectorParams(size=384, distance=Distance.COSINE),
                                "document_id_vec": VectorParams(size=384, distance=Distance.COSINE),
                            }
                        )
                        logger.info("✅ Created metadata_vectors collection with named vectors")
                    
                    qdrant_manager.client.upsert(
                        collection_name="metadata_vectors",
                        points=[point],
                        wait=True
                    )
                    
                    metadata_vectors_uuids.append(metadata_point_id)
                    logger.info(f"✅ Added notification to metadata_vectors: {metadata_point_id}")
                    
                except Exception as upsert_error:
                    logger.error(f"❌ Failed to upsert notification to metadata_vectors: {upsert_error}")
                    logger.error(f"📋 Point ID: {metadata_point_id}")
                    logger.error(f"📋 Document ID: {document_id}")
                    logger.error(f"📋 Payload keys: {list(metadata_payload.keys())}")
                
            except Exception as e:
                logger.error(f"❌ Failed to generate vectors for notification: {e}")
                
        except Exception as e:
            logger.error(f"❌ Error adding notification to metadata_vectors: {e}")
            
        return metadata_vectors_uuids

    def _add_chunks_to_metadata_vectors(self, chunks_for_storage: list, title: str, notification_metadata: dict) -> list:
        """
        Add chunks to the metadata_vectors collection with specific metadata vector fields
        like document_title_vec, document_number_vec, short_summary_vec, document_id_vec
        """
        metadata_vectors_uuids = []
        
        try:
            from utils.qdrant_utils import qdrant_manager
            
            for idx, chunk_item in enumerate(chunks_for_storage):
                chunk_data = chunk_item['chunk_data']
                kb_action = chunk_item['kb_action']
                
                # Extract key metadata for vector generation
                document_id = kb_action['payload'].get('document_id', '')
                chunk_title = kb_action['payload'].get('title', title)
                content = chunk_data.get('content', '')
                
                # Extract document number from document_id (like "RBI/2005-06/47")
                document_number = ''
                if document_id:
                    # Try to extract document number pattern
                    import re
                    number_match = re.search(r'(\d{4}[-/]\d{2}[-/]\d+)', document_id)
                    if number_match:
                        document_number = number_match.group(1)
                    else:
                        # Fallback to using the full document_id
                        document_number = document_id
                
                # Generate short summary from content
                short_summary = content[:500] + "..." if len(content) > 500 else content
                
                # Generate vectors for each metadata field
                try:
                    document_title_vec = self._generate_metadata_embedding(chunk_title)
                    document_number_vec = self._generate_metadata_embedding(document_number) if document_number else [0.0] * self._get_embedding_dimensions()
                    short_summary_vec = self._generate_metadata_embedding(short_summary)
                    document_id_vec = self._generate_metadata_embedding(document_id) if document_id else [0.0] * self._get_embedding_dimensions()
                    
                    # Create payload for metadata_vectors collection
                    metadata_payload = {
                        'document_id': document_id,
                        'title': chunk_title,
                        'document_number': document_number,
                        'short_summary': short_summary,
                        'content': content,
                        'page_content': content,
                        'source_notification_title': title,
                        'chunk_index': chunk_data.get('chunk_index', idx),
                        'document_type': 'notification_chunk',
                        'classification': chunk_data.get('classification', ''),
                        'confidence': chunk_data.get('confidence', ''),
                        'processing_timestamp': datetime.now().isoformat(),
                        # Include original metadata for searchability
                        'metadata': {
                            'title': chunk_title,
                            'document_id': document_id,
                            'content': content,
                            'page_content': content
                        }
                    }
                    
                    # Create named vectors dictionary for metadata_vectors collection
                    vectors = {
                        'document_title_vec': document_title_vec,
                        'document_number_vec': document_number_vec, 
                        'short_summary_vec': short_summary_vec,
                        'document_id_vec': document_id_vec
                    }
                    
                    # Generate unique point ID for metadata_vectors collection (use UUID format)
                    metadata_point_id = str(uuid.uuid4())
                    
                    # Use direct Qdrant client to add to metadata_vectors with named vectors
                    from qdrant_client.http.models import PointStruct
                    
                    point = PointStruct(
                        id=metadata_point_id,
                        vector=vectors,
                        payload=metadata_payload
                    )
                    
                    # Add to metadata_vectors collection
                    try:
                        logger.info(f"🔍 Adding point to metadata_vectors with ID: {metadata_point_id}")
                        logger.info(f"🔍 Vector keys: {list(vectors.keys())}")
                        logger.info(f"🔍 Vector dimensions: {[(k, len(v) if isinstance(v, list) else 'unknown') for k, v in vectors.items()]}")
                        
                        qdrant_manager.client.upsert(
                            collection_name="metadata_vectors",
                            points=[point],
                            wait=True
                        )
                        
                        metadata_vectors_uuids.append(metadata_point_id)
                        logger.info(f"✅ Added chunk {idx+1} to metadata_vectors: {metadata_point_id}")
                        
                    except Exception as upsert_error:
                        logger.error(f"❌ Failed to upsert chunk {idx+1} to metadata_vectors: {upsert_error}")
                        logger.error(f"📋 Point ID: {metadata_point_id}")
                        logger.error(f"📋 Document ID: {document_id}")
                        logger.error(f"📋 Payload keys: {list(metadata_payload.keys())}")
                        continue
                    
                except Exception as e:
                    logger.error(f"❌ Failed to generate vectors for chunk {idx+1}: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"❌ Error adding chunks to metadata_vectors: {e}")
            
        return metadata_vectors_uuids

    def _link_notification_chunks_to_document(self, pdf_chunks: list, doc_info: dict, notification: dict, notification_codes: dict) -> list:
        """Link notification chunks to a specific document with enhanced metadata and unique UUIDs"""
        linking_results = []
        
        try:
            doc_url = doc_info.get('document_url', '')
            doc_title = doc_info.get('document_title', '')
            collection = doc_info.get('collection_name', 'rbi_other')
            
            logger.info(f"🔗 Linking {len(pdf_chunks)} notification chunks to document: {doc_title}")
            
            for chunk_idx, chunk in enumerate(pdf_chunks):
                try:
                    # Create unique linking action with timestamp for unique UUID
                    timestamp = datetime.now().isoformat()
                    link_action = {
                        'action_type': 'UPDATE_DOCUMENT',
                        'collection_name': collection,
                        'timestamp': f"{timestamp}_chunk_{chunk_idx}",  # Unique timestamp per chunk
                        'filter_criteria': {'document_url': doc_url},
                        'payload': {
                            'linked_notification_title': notification.get('Title', ''),
                            'linked_notification_date': notification.get('Date', ''),
                            'linked_chunk_content': chunk.get('content', ''),
                            'linked_chunk_index': chunk.get('chunk_index', chunk_idx),
                            'linked_chunk_id': f"{notification.get('Title', '')}_chunk_{chunk_idx}_{timestamp}",
                            'updated_timestamp': timestamp,
                            'pdf_url': notification.get('PDF Link', ''),
                            'notification_link': notification.get('Link', ''),
                            'notification_codes': notification_codes,
                            'metadata': {
                                'last_updated': timestamp,
                                'updated_from_notification': notification.get('Title', ''),
                                'chunk_index': chunk_idx,
                                'total_linked_chunks': len(pdf_chunks),
                                'notification_codes': notification_codes,
                                'unique_link_id': f"{doc_url}_{chunk_idx}_{timestamp}"
                            }
                        }
                    }
                    
                    # Execute the linking action
                    result = self.knowledge_base_executor.execute_updates([link_action], notification)
                    linking_results.extend(result)
                    
                    logger.info(f"   ✅ Linked chunk {chunk_idx + 1}/{len(pdf_chunks)} to {doc_title}")
                    
                except Exception as e:
                    logger.error(f"❌ Error linking chunk {chunk_idx} to document {doc_title}: {e}")
                    
        except Exception as e:
            logger.error(f"❌ Error in _link_notification_chunks_to_document: {e}")
            
        return linking_results

    def run_pipeline_test_enhanced(self, max_notifications: int = 5, start_index: int = 0) -> dict:
        """
        Run the enhanced pipeline test with comprehensive UUID tracking and metadata handling
        """
        try:
            logger.info(f"🚀 Starting ENHANCED pipeline test with up to {max_notifications} notifications...")
            if start_index > 0:
                logger.info(f"🎯 Starting from index {start_index}")
            
            if not self.load_notifications():
                raise ValueError("Failed to load notifications")
            
            # Process a subset of notifications using enhanced method
            notifications_to_process = self.notifications[start_index:start_index + max_notifications]
            results = []
            
            for i, notification in enumerate(notifications_to_process):
                logger.info(f"")
                logger.info(f"🔄 ═══════════════════════════════════════════════════════════════")
                logger.info(f"🔄 PROCESSING NOTIFICATION {i+1}/{len(notifications_to_process)} ({((i+1)/len(notifications_to_process)*100):.1f}%)")
                logger.info(f"🔄 Title: {notification.get('Title', 'Unknown')[:60]}...")
                logger.info(f"🔄 Date: {notification.get('Date', 'Unknown')}")
                logger.info(f"🔄 ═══════════════════════════════════════════════════════════════")
                logger.info(f"")
                
                try:
                    # Use enhanced processing method
                    result = self.process_notification_enhanced(notification)
                    results.append(result)
                    
                    # Log immediate result summary
                    if result.get('status') == 'success':
                        uuid_count = len(result.get('uuid_summary', {}).get('total_added_uuids', []))
                        logger.info(f"✅ Notification {i+1}/{len(notifications_to_process)} processed successfully - {uuid_count} documents added")
                    else:
                        logger.error(f"❌ Notification {i+1}/{len(notifications_to_process)} failed: {result.get('error', 'Unknown error')}")
                        
                    # Log progress every 10 notifications
                    if (i + 1) % 10 == 0:
                        processed_so_far = i + 1
                        successful_so_far = len([r for r in results if r.get('status') == 'success'])
                        failed_so_far = len([r for r in results if r.get('status') == 'error'])

                        logger.info(f"")
                        logger.info(f"📊 PROGRESS UPDATE - {processed_so_far}/{len(notifications_to_process)} COMPLETED ({((processed_so_far)/len(notifications_to_process)*100):.1f}%)")
                        logger.info(f"    ✅ Successful: {successful_so_far}")
                        logger.info(f"    ❌ Failed: {failed_so_far}")
                        logger.info(f"    📈 Success Rate: {(successful_so_far/processed_so_far*100):.1f}%")
                        logger.info(f"")

                        # Show KB operations summary
                        summary = get_kb_summary()
                        logger.info(f"📊 KB Operations Summary:")
                        logger.info(f"    Active actions: {summary['active_actions']}")
                        logger.info(f"    Completed actions: {summary['completed_actions']}")
                        logger.info(f"    Success rate: {summary['successful_actions']}/{summary['completed_actions']}")
                        logger.info(f"")
                        
                except Exception as e:
                    logger.error(f"❌ Notification {i+1}/{len(notifications_to_process)} failed with exception: {e}")
                    error_result = {
                        'status': 'error',
                        'error': str(e),
                        'notification': notification,
                        'timestamp': datetime.now().isoformat()
                    }
                    results.append(error_result)
            
            # Comprehensive summary
            successful = len([r for r in results if r.get('status') == 'success'])
            failed = len([r for r in results if r.get('status') == 'error'])
            
            # Calculate total UUIDs across all notifications
            total_uuids_added = []
            total_uuids_updated = []
            total_operations = 0
            
            for result in results:
                if result.get('status') == 'success' and result.get('uuid_summary'):
                    uuid_summary = result['uuid_summary']
                    total_uuids_added.extend(uuid_summary.get('total_added_uuids', []))
                    total_uuids_updated.extend(uuid_summary.get('total_updated_uuids', []))
                    total_operations += uuid_summary.get('summary_stats', {}).get('total_operations', 0)
            
            summary = {
                'total_processed': len(results),
                'successful': successful,
                'failed': failed,
                'results': results,
                'overall_uuid_summary': {
                    'total_documents_added': len(total_uuids_added),
                    'total_documents_updated': len(total_uuids_updated),
                    'total_operations': total_operations,
                    'all_added_uuids': total_uuids_added,
                    'all_updated_uuids': total_uuids_updated
                },
                'test_timestamp': datetime.now().isoformat()
            }
            
            # Log final comprehensive summary
            logger.info(f"")
            logger.info(f"🎯 ═══════════════════════════════════════════════════════════════")
            logger.info(f"🎯 FINAL ENHANCED PIPELINE TEST SUMMARY")
            logger.info(f"🎯 ═══════════════════════════════════════════════════════════════")
            logger.info(f"📊 Notifications Processed: {len(results)}")
            logger.info(f"✅ Successful: {successful}")
            logger.info(f"❌ Failed: {failed}")
            logger.info(f"🆔 Total Documents Added: {len(total_uuids_added)}")
            logger.info(f"📝 Total Documents Updated: {len(total_uuids_updated)}")
            logger.info(f"⚡ Total Operations: {total_operations}")
            logger.info(f"")
            logger.info(f"🆔 ALL ADDED DOCUMENT UUIDs ({len(total_uuids_added)}):")
            for i, uuid_val in enumerate(total_uuids_added):
                logger.info(f"   {i+1:3d}. {uuid_val}")
            logger.info(f"")
            logger.info(f"🎯 ═══════════════════════════════════════════════════════════════")
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ Enhanced pipeline test failed: {e}")
            return {
                'error': str(e),
                'test_timestamp': datetime.now().isoformat()
            }

    def load_notifications(self):
        """Load notifications from the JSON file"""
        try:
            if not self.notifications_file.exists():
                logger.error(f"❌ Notifications file not found: {self.notifications_file}")
                return False
            
            with open(self.notifications_file, 'r', encoding='utf-8') as f:
                self.notifications = json.load(f)
            
            logger.info(f"📂 Loaded {len(self.notifications)} notifications from {self.notifications_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error loading notifications: {e}")
            return False
        
def extract_pdf_links(notification_content):
    """Extract PDF links from notification content"""
    pdf_links = re.findall(r'https?://\S+\.pdf', notification_content)
    logger.info(f"Extracted {len(pdf_links)} PDF links")
    return pdf_links

if __name__ == "__main__":
    """
    Main test execution
    """
    import argparse
    try:
        logger.info("🚀 Starting Real Pipeline Test with Enhanced KB Storage...")

        # Parse command-line arguments for notifications file
        parser = argparse.ArgumentParser(description="Run Real Pipeline Test")
        parser.add_argument('--notifications', type=str, default=None, help='Path to notifications JSON file')
        parser.add_argument('--max', type=int, default=50, help='Max notifications to process')
        parser.add_argument('--start', type=int, default=0, help='Start index for notifications')
        args = parser.parse_args()

        # Determine notifications file path
        notifications_file = args.notifications or os.getenv('NOTIFICATIONS_FILE') or "rbi_notifications.json"
        if not os.path.exists(notifications_file):
            logger.error(f"❌ Notifications file not found: {notifications_file}")
            sys.exit(1)
        else:
            logger.info(f"📄 Using notifications file: {notifications_file}")

        # Initialize the pipeline tester with explicit notifications file
        pipeline_test = RealPipelineTest(notifications_file=notifications_file)

        # Run the enhanced test with comprehensive UUID tracking
        logger.info(f"🚀 Starting ENHANCED pipeline test with comprehensive UUID tracking...")
        test_results = pipeline_test.run_pipeline_test_enhanced(max_notifications=args.max, start_index=args.start)

        # Save results to JSON file with enhanced suffix
        output_file = "real_pipeline_results_enhanced.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, indent=2, ensure_ascii=False)

        logger.info(f"✅ Enhanced test completed! Results saved to {output_file}")
        logger.info(f"📊 Summary: {test_results.get('successful', 0)} successful, {test_results.get('failed', 0)} failed")
        
        # Print enhanced summary with UUIDs
        if test_results.get('results'):
            logger.info(f"")
            logger.info(f"📋 DETAILED NOTIFICATION RESULTS:")
            for i, result in enumerate(test_results['results']):
                notification_title = result.get('notification', {}).get('Title', 'Unknown')[:50]
                status = result.get('status', 'unknown')
                
                uuid_info = "No UUIDs"
                if result.get('uuid_summary'):
                    uuid_count = len(result['uuid_summary'].get('total_added_uuids', []))
                    operations = result['uuid_summary'].get('summary_stats', {}).get('total_operations', 0)
                    uuid_info = f"{uuid_count} UUIDs, {operations} operations"
                
                kb_status = 'N/A'
                if result.get('kb_decision'):
                    kb_decision = result['kb_decision']
                    kb_status = f"Process: {kb_decision['should_process']}, Store: {kb_decision['should_store']}"
                
                logger.info(f"  📋 {i+1:2d}. {notification_title}...")
                logger.info(f"       Status: {status} | KB: {kb_status} | {uuid_info}")
        
        # Print overall UUID summary
        overall_summary = test_results.get('overall_uuid_summary', {})
        if overall_summary:
            logger.info(f"")
            logger.info(f"🎯 OVERALL UUID SUMMARY:")
            logger.info(f"   📈 Total Documents Added: {overall_summary.get('total_documents_added', 0)}")
            logger.info(f"   📝 Total Documents Updated: {overall_summary.get('total_documents_updated', 0)}")
            logger.info(f"   ⚡ Total Operations: {overall_summary.get('total_operations', 0)}")
            logger.info(f"   🆔 All UUIDs: {len(overall_summary.get('all_added_uuids', []))}")

        # Display final KB operations dashboard
        logger.info(f"")
        logger.info(f"🔍 FINAL KNOWLEDGE BASE OPERATIONS DASHBOARD")
        logger.info(f"=" * 80)

        # Show summary
        show_summary()

        # Export tracking data
        from kb_tracking_system import kb_tracker
        export_file = kb_tracker.export_tracking_data()
        logger.info(f"📊 KB tracking data exported to: {export_file}")

    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        sys.exit(1)

    # At the end of your main script, add:
    try:
        force_flush_logs()
    except Exception:
        for handler in logger.handlers:
            handler.flush()

